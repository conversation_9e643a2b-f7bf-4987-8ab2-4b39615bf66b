<template>

    <el-container>
        <el-header>
            <el-tabs v-model="activeTabName" class="tabs" @tab-click="handleTabClick">
                <el-tab-pane label="所有测试活动类型" name=""></el-tab-pane>
                <el-tab-pane label="系统合格性测试" name="SAT"></el-tab-pane>
                <el-tab-pane label="系统集成测试" name="IST"></el-tab-pane>
                <el-tab-pane label="软件合格性测试" name="SQT"></el-tab-pane>
                <el-tab-pane label="软件集成测试" name="SIT"></el-tab-pane>
                <el-tab-pane label="软件单元测试" name="SUT"></el-tab-pane>
                <el-tab-pane label="硬件测试" name="HT"></el-tab-pane>
            </el-tabs>
        </el-header>

        <el-container>
            <el-aside>
                <el-scrollbar style="height: 72vh;">
                    <ModuleNav @check="onModuleCheck" :action_type="form.action_type" />
                </el-scrollbar>
            </el-aside>

            <el-main>

                <div class="tool-bar-container">
                    <el-button icon="Plus" type="primary" @click="handleAdd">新增</el-button>
                    <el-button icon="Plus" type="primary" @click="dialog_show = true">批量导入公共用例</el-button>
                    <el-button type="primary" @click="handlePTCCopy">复制项目测试用例</el-button>

                    <div style="margin-left: auto; display: flex; gap: 10px;">
                        <div>
                            <el-button icon="Setting" text bg @click="isSettingVisible = true">设置</el-button>
                            <!-- 设置弹窗 -->
                            <el-popover v-model:visible="isSettingVisible" width="180" trigger="manual"
                                placement="bottom">
                                <template #reference>
                                    <!-- 设置按钮作为触发器 -->
                                    <div style="display: inline-block;"></div>
                                </template>
                                <!-- 操作按钮 -->
                                <div class="column-popper-title">
                                    <div style="display: flex; align-items: center; justify-content: space-between;">
                                        <el-checkbox :model-value="tableColumns.every(item => item.show)"
                                            :indeterminate="tableColumns.some(item => item.show) && tableColumns.some(item => !item.show)"
                                            label="列展示" @change="selectAllColumn" />
                                        <el-button text @click="resetColumns"
                                            style="margin-right: -10px;">重置</el-button>
                                    </div>
                                </div>
                                <!-- 列设置内容 -->
                                <div class="column-content" style="max-height: 200px; overflow-y: auto;">
                                    <div class="column-item" v-for="column in tableColumns" :key="column.key">
                                        <el-checkbox v-model="column.show" :label="column.name"
                                            :disabled="column.disabled"></el-checkbox>
                                    </div>
                                </div>
                            </el-popover>

                        </div>
                        <el-tooltip class="box-item" effect="dark" content="重置已添加的筛选条件" placement="top-start">
                            <el-button text bg @click="handleReset">重置</el-button>
                        </el-tooltip>
                        <filterButton @click="onFilterStatusChange" :count="filterCount" />
                        <el-button icon="Refresh" text bg @click="handleRefresh">刷新</el-button>
                        <el-button icon="Download" text bg @click="handleLoad">下载</el-button>
                    </div>
                </div>

                <div class="filter-container" v-if="showFilterContainer">
                    <div style="display: flex;">
                        <el-input v-model="form.number" placeholder="请输入用例ID" @keyup.enter="onFilter" clearable>
                            <template #append>
                                <el-button icon="Search" @click="onFilter"></el-button>
                            </template>
                        </el-input>
                        <el-input v-model="form.name" placeholder="请输入用例名称" @keyup.enter="onFilter" clearable>
                            <template #append>
                                <el-button icon="Search" @click="onFilter"></el-button>
                            </template>
                        </el-input>
                        <el-select v-model="form.status_list" placeholder="请选择用例状态" @change="onFilter"
                            style="width: 400px;" multiple clearable>
                            <el-option label="待评审" value="PENDING" type="primary"></el-option>
                            <el-option label="评审中" value="REVIEWING" type="warning"></el-option>
                            <el-option label="评审通过" value="APPROVED" type="success"></el-option>
                            <el-option label="评审不通过" value="REJECTED" type="danger"></el-option>
                            <el-option label="已撤销" value="CANCEL" type="danger"></el-option>
                            <el-option label="废弃" value="DEPRECATED" type="info"></el-option>
                        </el-select>
                        <el-input v-model="form.creator_re" placeholder="请输入创建人" @keyup.enter="onFilter" clearable>
                            <template #append>
                                <el-button icon="Search" @click="onFilter"></el-button>
                            </template>
                        </el-input>
                    </div>
                    <div style="margin-top: 10px;display: flex;">
                        <el-select v-model="form.execute_mode_list" placeholder="请选择执行方式" @change="onFilter" multiple
                            clearable>
                            <el-option label="自动化测试" value="AUTOMATED_EXECUTION"></el-option>
                            <el-option label="手动测试" value="MANUAL_EXECUTION"></el-option>
                            <el-option label="半自动化测试" value="SEMI_AUTOMATED_EXECUTION"></el-option>
                        </el-select>
                        <el-select v-model="form.priority_list" placeholder="请选择优先级" @change="onFilter" multiple
                            clearable>
                            <el-option label="高" value="HIGH"></el-option>
                            <el-option label="中" value="MIDDLE"></el-option>
                            <el-option label="低" value="LOW"></el-option>
                        </el-select>
                        <el-select v-model="form.product_type_id_list" placeholder="请选择产品类型" clearable
                            @change="onFilter" multiple>
                            <el-option v-for="item in product_types" :key="item.id" :label="item.name"
                                :value="item.id"></el-option>
                        </el-select>
                        <el-select v-model="form.type" placeholder="请选择用例类型" @change="onFilter" clearable>
                            <el-option label="耐久测试" value="DURABLE_TEST"></el-option>
                            <el-option label="性能测试" value="PERFORMANCE_TEST"></el-option>
                            <el-option label="功能测试" value="FUNCTION_TEST"></el-option>
                            <el-option label="协议栈测试" value="PROTOCOL_STACK_TEST"></el-option>
                            
                        </el-select>
                    </div>
                </div>

                <el-table :data="tableData" stripe border style="width: 100%" class="table-container"
                    @sort-change="onSortChange" :header-cell-class-name="(params) => {
                        setHeaderClass(params)
                    }">
                    <el-table-column v-if="tableColumns[1].show" prop="number" label="用例ID" min-width="200"
                        align="left" sortable="custom"></el-table-column>
                    <el-table-column v-if="tableColumns[0].show" prop="name" label="用例名称" min-width="300"
                        align="left">
                        <template #default="{ row }">
                            <el-link type="primary" @click="onDetail(row)" :underline="false">{{ row.name
                            }}</el-link>
                        </template>
                    </el-table-column>
                    <el-table-column v-if="tableColumns[2].show" label="用例状态" min-width="120" align="center">
                        <template #default="{ row }">
                            <el-tag v-if="row.status == 'PENDING'" type="primary">待评审</el-tag>
                            <el-tag v-else-if="row.status == 'REVIEWING'" type="warning">评审中</el-tag>
                            <el-tag v-else-if="row.status == 'APPROVED'" type="success">评审通过</el-tag>
                            <el-tag v-else-if="row.status == 'REJECTED'" type="danger">评审不通过</el-tag>
                            <el-tag v-else-if="row.status == 'CANCEL'" type="danger">已撤销</el-tag>
                            <el-tag v-else-if="row.status == 'DEPRECATED'" type="info">废弃</el-tag>
                            <el-tag v-else type="danger">未知</el-tag>
                        </template>
                    </el-table-column>

                    <el-table-column v-if="tableColumns[3].show" prop="priority" label="优先级" min-width="100"
                        align="center">
                        <template #default="{ row }">
                            <el-tag v-if="row.priority == 'HIGH'" type="warning">高</el-tag>
                            <el-tag v-else-if="row.priority == 'MIDDLE'" type="success">中</el-tag>
                            <el-tag v-else-if="row.priority == 'LOW'" type="info">低</el-tag>
                            <el-tag v-else type="danger">未知</el-tag>
                        </template>
                    </el-table-column>

                    <el-table-column v-if="tableColumns[4].show" prop="project_name" label="所属项目" min-width="260"
                        align="center"></el-table-column>

                    <el-table-column v-if="tableColumns[5].show" prop="version" label="用例版本" min-width="120"
                        align="center" sortable="custom">
                        <template #default="{ row }">
                            <el-tag type="primary">V{{ row.version }}.0</el-tag>
                        </template>
                    </el-table-column>

                    <el-table-column v-if="tableColumns[6].show" prop="module" label="所属模块" min-width="200"
                        align="center">
                        <template #default="{ row }">
                            <span>{{ moduleMap[row.module] || row.module }}</span>
                            <span v-if="row.module_2level"> / {{ moduleMap[row.module + '-' + row.module_2level] ||
                                row.module_2level }}</span>
                            <span v-if="row.module_3level"> / {{ moduleMap[row.module + '-' + row.module_2level +
                                '-' + row.module_3level] || row.module_3level }}</span>
                        </template>
                    </el-table-column>

                    <el-table-column v-if="tableColumns[7].show" prop="source" label="用例来源" min-width="150"
                        align="center">
                        <template #default="{ row }">
                            <span>{{ sourceMap[row.source] || row.source }}</span>
                        </template>
                    </el-table-column>

                    <el-table-column v-if="tableColumns[8].show" prop="type" label="用例类型" min-width="150"
                        align="center">
                        <template #default="{ row }">
                            <span>{{ typeMap[row.type] || row.type }}</span>
                        </template>
                    </el-table-column>

                    <el-table-column v-if="tableColumns[9].show" label="用例活动类型" min-width="200" align="center">
                        <template #default="{ row }">
                            <span>{{ actionTypeMap[row.action_type] || row.action_type }}</span>
                        </template>
                    </el-table-column>

                    <el-table-column v-if="tableColumns[10].show" label="执行方式" min-width="150" align="center">
                        <template #default="{ row }">
                            <el-tag v-if="row.execute_mode == 'AUTOMATED_EXECUTION'" type="success">自动化测试</el-tag>
                            <el-tag v-else-if="row.execute_mode == 'MANUAL_EXECUTION'" type="success">手动测试</el-tag>
                            <el-tag v-else-if="row.execute_mode == 'SEMI_AUTOMATED_EXECUTION'"
                                type="success">半自动化测试</el-tag>
                            <el-tag v-else type="danger">未知</el-tag>
                        </template>
                    </el-table-column>

                    <el-table-column v-if="tableColumns[11].show" label="产品类型" min-width="150" align="center">
                        <template #default="{ row }">
                            <span>{{ producttypesmap[row.product_type_id] || row.product_type_id }}</span>
                        </template>
                    </el-table-column>

                    <el-table-column v-if="tableColumns[12].show" prop="creator_name" label="创建人" min-width="100"
                        align="center"></el-table-column>

                    <el-table-column v-if="tableColumns[13].show" prop="create_time" label="创建时间" min-width="200"
                        align="center" sortable="custom"></el-table-column>

                    <el-table-column v-if="tableColumns[14].show" label="操作" min-width="220" fixed="right"
                        align="center">
                        <template #default="{ row }">
                            <div style="display: flex; justify-content: center; gap: 10px;">
                                <el-button type="primary" size="small" @click="handleEdit(row)">编辑</el-button>
                                <el-button type="primary" size="small" @click="handleCopy(row)">复制</el-button>
                                <el-dropdown style="margin-left: 10px;">
                                    <el-button class="el-tooltip__trigger" type="primary" size="small">更多</el-button>
                                    <template #dropdown>
                                        <el-dropdown-menu>
                                            <el-dropdown-item><el-button type="primary" size="small"
                                                    @click="handleMove(row)">移动</el-button></el-dropdown-item>
                                            <el-dropdown-item><el-button type="danger" size="small"
                                                    @click="handleDelete(row)">删除</el-button></el-dropdown-item>
                                            <el-dropdown-item v-if="row.status == 'DEPRECATED'"><el-button
                                                    type="primary" size="small"
                                                    @click="handleRecover(row)">恢复</el-button></el-dropdown-item>
                                            <el-dropdown-item v-else><el-button type="danger" size="small"
                                                    @click="handleDiscard(row)">废弃</el-button></el-dropdown-item>
                                        </el-dropdown-menu>
                                    </template>
                                </el-dropdown>
                            </div>
                        </template>
                    </el-table-column>

                </el-table>

                <div class="pagination-container">
                    <el-pagination :page-sizes="[1, 10, 15, 20, 25, 50, 100]"
                        layout="prev, pager, next, jumper, total, sizes" :total="total" background
                        @change="onPageChange" v-model:current-page="form.page" v-model:page-size="form.pagesize" />
                </div>
            </el-main>

        </el-container>
    </el-container>

    <el-dialog v-if="dialogMoveVisible" v-model="dialogMoveVisible" title="移动测试用例" width="800"
        :close-on-click-modal="false">
        <Move @confirm="onMoveConfirm" @cancel="onMoveCancel" :r_id="r_id" />
    </el-dialog>

    <el-drawer v-model="drawerDetailVisible" :with-header="false" size="60%" :destroy-on-close="true">
        <TestCaseDetail :id="r_id" />
    </el-drawer>

    <el-dialog v-if="dialog_show" v-model="dialog_show" title="批量导入公共用例" width="1000" :close-on-click-modal="false">
        <All_Change @submit="onPatchImport" />
    </el-dialog>

    <el-dialog v-if="ptc_dialog" v-model="ptc_dialog" title="复制项目测试用例" width="1000" :close-on-click-modal="false">
        <ProjectTestCaseCopy @confirm="onPTCCopyConfirm" @cancel="onPTCCopyCancel" />
    </el-dialog>

</template>


<script setup>
import All_Change from './all_change.vue';
import { ref, reactive, onMounted, onActivated, watch } from 'vue';
import http from '@/utils/http/http.js';
import { useRouter, useRoute } from 'vue-router';
import ModuleNav from './moduleNav.vue';
import Move from './move.vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useUserStore } from '@/stores/user.js';
import { useProjectStore } from '@/stores/project.js';
import TestCaseDetail from '@/components/test_case.vue';
import filterButton from '@/components/filterButton.vue';
import ProjectTestCaseCopy from './project_test_case_copy.vue';
import { useAccessStat } from '@/utils/accessStat';

useAccessStat('/test_cases2/list', '测试用例列表');

const tableColumns = ref([
    { key: "number", name: "用例ID", show: true, disabled: true },
    { key: "name", name: "用例名称", show: true, disabled: true },
    { key: "status", name: "用例状态", show: true },
    { key: "priority", name: "优先级", show: true },
    { key: "project_name", name: "所属项目", show: true },
    { key: "version", name: "用例版本", show: true },
    { key: "module", name: "所属模块", show: true },
    { key: "source", name: "用例来源", show: true },
    { key: "type", name: "用例类型", show: true },
    { key: "action_type", name: "用例活动类型", show: true },
    { key: "execute_mode", name: "执行方式", show: true },
    { key: "product_type_id", name: "产品类型", show: true },
    { key: "creator_name", name: "创建人", show: true },
    { key: "create_time", name: "创建时间", show: true },
    { key: "operation", name: "操作", show: true }
]);


// 设置弹窗的显示状态
const isSettingVisible = ref(false);

// 全选或取消全选逻辑
const selectAllColumn = (checked) => {
    tableColumns.value.forEach((column) => {
        if (!column.disabled) { // 跳过禁用的列
            column.show = checked;
        }
    });
};


// 重置列设置
const resetColumns = () => {
    tableColumns.value.forEach((column) => {
        if (!column.disabled) { // 跳过禁用的列
            column.show = true;
        }
    });
};

let dialog_show = ref(false)
let ptc_dialog = ref(false)

let projectStore = useProjectStore();
let userStore = useUserStore();
const filterCount = ref(0);
const activeTabName = ref('');
const product_types = ref([]);
const pre_product_types = ref([]);
const router = useRouter();
const route = useRoute();
const dialogMoveVisible = ref(false);
const drawerDetailVisible = ref(false);
let r_id = ref(0);

let user_info = userStore.user_info;

const tableData = ref([]);

const producttypesmap = ref({});



let form = reactive({
    page: 1,
    pagesize: 15,
});

let total = ref(0);

let showFilterContainer = ref(false);

function onFilterStatusChange() {
    showFilterContainer.value = !showFilterContainer.value;
};

function update_table() {
    http.get('/test_cases', { params: form }).then(res => {
        tableData.value = res.data.data.results;
        total.value = res.data.data.count;
    });

    filterCount.value = Object.keys(form).filter(key => !['page', 'pagesize', 'project_number', 'module_list', 'module_2level_list', 'module_3level_list', 'action_type', 'order'].includes(key)).reduce((count, key) => {
        if (form[key] == '' || form[key] == undefined || form[key] == null || form[key].length == 0) {
            return count;
        } else {
            return count + 1;
        }
    }, 0)
};

function handleReset() {
    form = reactive({
        page: 1,
        pagesize: 15,
        project_number: form.project_number,
        module_list: form.module_list,
        action_type: form.action_type,
    });
    update_table();
};

function onPageChange() {
    update_table();
};

function onFilter() {
    form.page = 1;
    form.pagesize = 15;
    update_table();
};

watch(() => projectStore.project_info, () => {
    form.project_number = projectStore.project_info.projectCode;
    update_table();
});

watch([() => projectStore.project_info, pre_product_types], () => {

    form.product_type = '';
    product_types.value = pre_product_types.value;

    if (projectStore.project_info?.projectCode) {
        http.get(`/projects/detail/by_number`, { params: { number: projectStore.project_info?.projectCode } }).then(res => {

            let pt = res.data.data?.configs?.product_types || [];
            product_types.value = pre_product_types.value.filter(item => pt.includes(item.id));

        });
    }
})

function handleAdd() {
    router.push({ path: '/test_cases2/add', query: { project_number: form.project_number } });
};


function handleEdit(row) {
    router.push({ path: '/test_cases2/add', query: { type: 'edit', id: row.id } });
};

function handleCopy(row) {
    router.push({ path: '/test_cases2/add', query: { type: 'copy', id: row.id } });
};

function handleDiscard(row) {
    ElMessageBox.confirm(
        '确定废弃吗?',
        '提示',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }
    ).then(() => {
        http.post(`/test_cases/${row.id}/discard`).then(res => {
            ElMessage({
                message: '废弃成功.',
                type: 'success',
            });
            update_table();
        }).catch(err => {
            ElMessage({
                type: 'error',
                message: err.response.data.msg
            });
        });
    }).catch(() => {
        ElMessage({
            type: 'info',
            message: '已取消废弃'
        });
    });
};

function handleRecover(row) {
    ElMessageBox.confirm(
        '确定恢复吗?',
        '提示',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }
    ).then(() => {
        http.post(`/test_cases/${row.id}/recover`).then(res => {
            ElMessage({
                message: '恢复成功.',
                type: 'success',
            });
            update_table();
        }).catch(err => {
            console.log(err);
        });
    }).catch(() => {
        ElMessage({
            type: 'info',
            message: '已取消恢复'
        });
    });
};

function handleDelete(row) {
    ElMessageBox.confirm(
        '确定删除吗?',
        '提示',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }
    ).then(() => {
        http.delete(`/test_cases/${row.id}`).then(res => {
            ElMessage({
                message: '删除成功.',
                type: 'success',
            });
            update_table();
        }).catch(err => {
            ElMessage({
                type: 'error',
                message: err.response.data.msg
            });
        });
    }).catch(() => {
        ElMessage({
            type: 'info',
            message: '已取消删除'
        });
    });
};

function handleMove(row) {
    r_id.value = row.id;
    dialogMoveVisible.value = true;
};

function handleTabClick(tab) {
    form.action_type = tab.paneName;
    update_table(form);
};

function onModuleCheck(module1, module2, module3) {
    form.module_list = module1;
    form.module_2level_list = module2;
    form.module_3level_list = module3;
    update_table();
};

function handlePTCCopy() {
    ptc_dialog.value = true;
};

let moduleMap = ref({});

let actionTypeMap = ref({});

let typeMap = ref({
    "DURABLE_TEST": "耐久测试",
    "PERFORMANCE_TEST": "性能测试",
    "FUNCTION_TEST": "功能测试",
    "PROTOCOL_STACK_TEST": "协议栈测试"
});

let sourceMap = ref({
    "TASK_CHANGE": "用例库沿用",
    "TASK_AFFIRM": "需求分析",
    "NEW_PROJECT": "需求变更",
    "HORIZONTAL_SCALING": "横向扩展"
});

function onMoveConfirm() {
    dialogMoveVisible.value = false;
    update_table(form);
};

function onMoveCancel() {
    dialogMoveVisible.value = false;
};

function onDetail(row) {
    r_id.value = row.id;
    drawerDetailVisible.value = true;
};

function onPatchImport() {
    dialog_show.value = false;
    update_table();
};

function handleRefresh() {
    update_table();
};

function handleLoad() {
    let url = import.meta.env.VITE_BASE_URL + '/test_cases/download';

    let params = new URLSearchParams();
    for (let key in form) {
        if (form[key] != '' && form[key] != undefined && form[key] != null) {
            params.append(key, form[key]);
        }
    }
    url = url + '?' + params.toString();

    window.open(url);
};

function setHeaderClass({ column }) {
    if (form.order) {
        if (form.order.includes(column.property)) {
            column.order = 'ascending';
        } else if (form.order.includes('-' + column.property)) {
            column.order = 'descending';
        }
    }
}

function onSortChange({ prop, order }) {
    if (!form.order) {
        form.order = [];
    }

    form.order = form.order.filter(item => item != prop && item != '-' + prop);
    var sort = '';
    if (order == 'ascending') {
        sort = prop;
        form.order.push(sort);
    } else if (order == 'descending') {
        sort = '-' + prop;
        form.order.push(sort);
    }

    update_table();
};

function onPTCCopyConfirm() {
    ptc_dialog.value = false;
    update_table();
};

function onPTCCopyCancel() {
    ptc_dialog.value = false;
};

onMounted(() => {
    http.get('/functions').then(res => {
        res.data.data.results.forEach(item => {

            moduleMap.value[item.number] = item.name;

            if (item.children) {
                item.children.forEach(item2 => {
                    moduleMap.value[item.number + '-' + item2.number] = item2.name;
                    if (item2.children) {
                        item2.children.forEach(item3 => {
                            moduleMap.value[item.number + '-' + item2.number + '-' + item3.number] = item3.name;
                        });
                    }
                });
            }
        });
    });

    http.get('/test_m/test_case_types', { params: { pagesize: 10000 } }).then(res => {
        res.data.data.results.forEach(item => {
            actionTypeMap.value[item.number] = item.name;
        });

    });

    http.get('/product_types', { params: { pagesize: 100000 } }).then(res => {
        let data = res.data.data.results;
        pre_product_types.value = data;

        pre_product_types.value.forEach(item => {
            producttypesmap.value[item.id] = item.name;
        });

    }).catch(err => {
        console.log(err);
    });
});

onActivated(() => {
    form.project_number = projectStore.project_info.projectCode;
    update_table();
});

</script>


<style lang="scss" scoped>
.search-container {
    width: 100%;
    display: flex;
    justify-content: center;
    justify-items: center;
    height: 40px;
}

.search-input {
    width: 50%;
    max-width: 500px;
}

.search-button {
    border: 1px solid #dcdfe6;
}

.filter-container {
    width: 100%;
    margin-bottom: 10px;

    .el-input,
    .el-select {
        width: 50%;
        max-width: 300px;
        margin-right: 10px;
    }

    :deep(.el-select__wrapper) {
        height: 100%;
    }
}

.tool-bar-container {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    justify-items: center;

    .el-select {
        width: 500px;
        margin-left: 10px;
    }

}

.el-aside {
    height: 100% !important;

    margin-top: 20px;
}

.el-main {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);

    margin-top: 0px;

    .table-container {
        flex: 1;
        overflow: auto;
    }
}

.el-table {
    --el-table-row-hover-bg-color: #d2e3ff;
}

:deep(.el-tooltip__trigger:focus-visible) {
    outline: unset;
}

.column-popper-title {
    border-bottom: 1px solid #ebeef5;
}

/* 自定义滚动条样式 */
.column-content::-webkit-scrollbar {
    width: 6px;
    /* 滚动条宽度 */
}

.column-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    /* 轨道背景色 */
    border-radius: 4px;
    /* 轨道圆角 */
}

/* 滚动条滑块默认样式（浅色） */
.column-content::-webkit-scrollbar-thumb {
    background: #e4e3e3;
    /* 浅色 */
    border-radius: 4px;
    /* 滑块圆角 */
}

/* 滚动条滑块悬停样式（深色） */
.column-content::-webkit-scrollbar-thumb:hover {
    background: #c7c7c7;
    /* 深色 */
}

.pagination-container {
  margin-top: auto; /* 自动顶到底部 */
  align-self: flex-start; /* 左对齐 */
  padding-top: 15px;
  width: 100%;
}
</style>