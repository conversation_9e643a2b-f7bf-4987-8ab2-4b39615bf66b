<template>
  <div ref="containerRef" class="pin-diagram-container">
    <!-- 动态计算viewBox以适配所有内容 -->
    <svg
      :viewBox="`0 0 ${svgWidth} ${svgHeight}`"
      :style="svgStyle"
      preserveAspectRatio="xMidYMid meet"
      width="100%"
      height="100%"
    >
      <!-- 定义渐变和阴影效果 -->
      <defs>
        <linearGradient id="chipGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#B3D9FF;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#B3D9FF;stop-opacity:1" />
        </linearGradient>
        <filter id="chipShadow" x="-20%" y="-20%" width="140%" height="140%">
          <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#000" flood-opacity="0.3"/>
        </filter>
      </defs>

      <!-- 芯片主体和引脚 -->
      <g :transform="`translate(${chipAreaX}, ${chipAreaY})`">
        <!-- 芯片主体 -->
        <rect
          :x="chipX"
          :y="chipY"
          :width="chipSize"
          :height="chipSize"
          rx="12"
          ry="12"
          fill="url(#chipGradient)"
          stroke="#fff"
          stroke-width="3"
          filter="url(#chipShadow)"
        />

        <!-- 1脚标识圆点 -->
        <circle
          :cx="chipX + chipSize / (pinsPerSide + 1) * 1"
          :cy="chipY + chipSize * 0.03"
          :r="chipSize * 0.01"
          fill="#fff"
          stroke="#333"
          stroke-width="0.5"
          opacity="0.95"
        />

        <text
          :x="chipX + chipSize / 2"
          :y="chipY + chipSize / 2"
          text-anchor="middle"
          dominant-baseline="middle"
          :font-size="chipModelFontSize"
          font-weight="bold"
          fill="#fff"
        >
          {{ chipModel }}
        </text>

        <!-- 引脚 -->
        <template v-for="(pin, index) in pinPositions" :key="index">
          <g
            class="pin-group"
            :transform="`translate(${pin.x}, ${pin.y})`"
            @click="onPinClick(pin)"
          >
            <rect
              :x="pin.rectX"
              :y="pin.rectY"
              :width="pin.rectW"
              :height="pin.rectH"
              rx="1"
              ry="1"
              :fill="getPinColor(pin)"
              stroke="#999"
              stroke-width="1"
            />
            <text
              :x="pin.rectX + pin.rectW / 2"
              :y="pin.rectY + pin.rectH / 2"
              text-anchor="middle"
              dominant-baseline="middle"
              :font-size="pinFontSize"
              fill="#000"
            >
              {{ getPinNumber(pin) }}
            </text>
          </g>
        </template>
      </g>

      <!-- 引脚类型图例（侧边显示） -->
      <g v-if="showLegend && legendItems.length > 0" :transform="`translate(${legendX}, ${legendY})`">
        <!-- 图例背景框 - 白色卡片样式 -->
        <rect
          x="0"
          y="0"
          :width="legendBoxWidth"
          :height="legendBoxHeight"
          :rx="12 * 0.8"
          :ry="12 * 0.8"
          fill="#ebedf066"
          stroke="#e0e0e0"
          stroke-width="1"
          filter="url(#legendShadow)"
        />
        <!-- 图例标题 -->
        <text
          :x="10 * 0.8"
          :y="16 * 0.8"
          :font-size="legendTitleFontSize"
          font-weight="600"
          fill="#333"
        >
          引脚类型图例 ({{ legendItems.length }})
        </text>
        <!-- 图例项目 -->
        <template v-for="(item, idx) in legendItems" :key="item.type">
          <g :transform="`translate(${10 * 0.8}, ${(35 + idx * legendItemSpacing) * 0.8})`">
            <!-- 颜色方块 -->
            <rect
              x="0"
              y="0"
              :width="legendIconSize"
              :height="legendIconSize"
              :fill="item.color"
              stroke="#999"
              stroke-width="1"
              :rx="3"
            />
            <!-- 类型文本 -->
            <text
              :x="legendIconSize + 6 * 0.8"
              :y="legendIconSize / 2"
              :font-size="legendTextFontSize"
              fill="#555"
              alignment-baseline="middle"
              font-weight="400"
            >
              {{ item.type }}
            </text>
          </g>
        </template>
      </g>
      <!-- 添加阴影滤镜定义 -->
      <defs>
        <filter id="legendShadow" x="-20%" y="-20%" width="140%" height="140%">
          <feDropShadow dx="0" dy="2" stdDeviation="4" flood-color="#000000" flood-opacity="0.1"/>
        </filter>
      </defs>


    </svg>
  </div>
</template>

<script setup>
import { computed, ref, watch, onMounted, onUnmounted, nextTick } from 'vue'

const props = defineProps({
  pinCount: {
    type: Number,
    required: true,
    validator: (val) => val >= 0,
  },
  chipModel: {
    type: String,
    default: '',
  },
  pins: {
    type: Array,
    default: () => [],
  },
  pinSize: {
    type: Number,
    default: 1.0,
    validator: (val) => val > 0 && val <= 2.0,
  },
  showLegend: {
    type: Boolean,
    default: true,
  },
  typeStyleConfig: {
    type: Object,
    default: () => ({}),
  },
  highlightedPin: {
    type: [String, Number],
    default: null,
  },
})

// 容器引用和尺寸检测
const containerRef = ref(null)
const containerSize = ref({ width: 800, height: 600 })
const resizeObserver = ref(null)

// 颜色映射 - 基于传入的typeStyleConfig动态生成
const pinColors = computed(() => {
  console.log('ChipPinDiagram - typeStyleConfig:', props.typeStyleConfig);
  console.log('ChipPinDiagram - typeStyleConfig type:', typeof props.typeStyleConfig);

  // 处理数组格式的颜色配置
  if (Array.isArray(props.typeStyleConfig)) {
    console.log('ChipPinDiagram - 处理数组格式的颜色配置');
    const colors = {};
    props.typeStyleConfig.forEach(item => {
      if (item.module && item.color) {
        colors[item.module] = item.color;
        // console.log(`ChipPinDiagram - 添加颜色映射: ${item.module} -> ${item.color}`);
      }
    });
    // 添加默认颜色 - 未选择模块时为#ECEFF1
    colors.DEFAULT = '#B0BEC5';
    colors[''] = '##B0BEC5'; // 空值时也为#ECEFF1
    colors['未配置'] = '##B0BEC5'; // 未配置时也为#ECEFF1
    colors['OTHER'] = '#ECEFF1';  // OTHER属于其他模块，使用默认颜色
    console.log('ChipPinDiagram - 数组格式最终颜色映射:', colors);
    return colors;
  }

  // 处理对象格式或空配置
  if (!props.typeStyleConfig || Object.keys(props.typeStyleConfig).length === 0) {
    console.log('ChipPinDiagram - 使用默认颜色配置');
    // 如果没有传入配置，使用默认配置
    return {
        Power: '#F28B82',
        GPIO: '#81C784',
        UART: '#FFB74D',
        SPI: '#BAA0FF',
        IIC: '#80D8FF',
        ADC: '#FF8A65',
        PWM: '#B388FF',
        EXIT: '#80DEEA',
        CAN: '#A1887F',
        OTHER: '#ECEFF1', // OTHER属于其他模块
        DEFAULT: '#ECEFF1', // 未选择模块时为#ECEFF1
        '': '#ECEFF1', // 空值时也为#ECEFF1
        '未配置': '#ECEFF1', // 未配置时也为#ECEFF1
    };
  }

  console.log('ChipPinDiagram - 处理对象格式的颜色配置');
  // 基于typeStyleConfig生成颜色映射
  const colors = {};
  Object.entries(props.typeStyleConfig).forEach(([type, config]) => {
    console.log(`ChipPinDiagram - 处理类型 ${type}:`, config);
    // 处理不同的数据格式
    if (typeof config === 'string') {
      colors[type] = config;
    } else if (config && typeof config === 'object' && config.color) {
      colors[type] = config.color;
    } else {
      colors[type] = '#ECEFF1'; // 默认颜色为#ECEFF1
    }
  });
  colors.OTHER = '#ECEFF1'; // OTHER属于其他模块
  colors.DEFAULT = '#ECEFF1'; // 未选择模块时为#ECEFF1
  colors[''] = '#ECEFF1'; // 空值时也为#ECEFF1
  colors['未配置'] = '#ECEFF1'; // 未配置时也为#ECEFF1
  console.log('ChipPinDiagram - 对象格式最终颜色映射:', colors);
  return colors;
});

// 图例相关计算
const legendItems = computed(() => {
  console.log('ChipPinDiagram - legendItems 计算开始');
  console.log('ChipPinDiagram - showLegend:', props.showLegend);
  console.log('ChipPinDiagram - typeStyleConfig:', props.typeStyleConfig);

  if (!props.showLegend) return []

  // 如果传入的是数组格式的颜色配置，直接基于此生成图例
  if (Array.isArray(props.typeStyleConfig)) {
    console.log('ChipPinDiagram - 基于数组格式颜色配置生成图例');
    const result = props.typeStyleConfig.map(item => ({
      type: item.module,
      color: item.color
    }));
    console.log('ChipPinDiagram - 数组格式图例项目:', result);
    return result;
  }

  // 如果传入的是对象格式的颜色配置，基于此生成图例
  if (props.typeStyleConfig && typeof props.typeStyleConfig === 'object' && Object.keys(props.typeStyleConfig).length > 0) {
    console.log('ChipPinDiagram - 基于对象格式颜色配置生成图例');
    const result = Object.entries(props.typeStyleConfig)
      .filter(([type, config]) => type !== 'DEFAULT')
      .map(([type, config]) => ({
        type,
        color: typeof config === 'string' ? config : (config.color || '#ECEFF1')
      }));
    console.log('ChipPinDiagram - 对象格式图例项目:', result);
    return result;
  }

  // 如果没有颜色配置，则基于引脚数据生成图例（后备方案）
  console.log('ChipPinDiagram - 基于引脚数据生成图例（后备方案）');
  const typesUsed = new Set()

  props.pins.forEach(pin => {
    const mainType = pin.pinType || pin.type || pin.displayType;
    if (mainType) {
      typesUsed.add(mainType)
    }
  })

  // 确保至少有一个类型
  if (typesUsed.size === 0) {
    typesUsed.add('GPIO')
  }

  // 按类型名称排序
  const typeOrder = ['GPIO', 'ADC', 'PWM', 'UART', 'SPI', 'IIC', 'EXIT', 'CAN', 'Power']
  const sortedTypes = Array.from(typesUsed).sort((a, b) => {
    const indexA = typeOrder.indexOf(a)
    const indexB = typeOrder.indexOf(b)
    if (indexA === -1 && indexB === -1) return a.localeCompare(b)
    if (indexA === -1) return 1
    if (indexB === -1) return -1
    return indexA - indexB
  })

  const result = sortedTypes.map(type => ({
    type,
    color: pinColors.value[type] || pinColors.value.DEFAULT
  }))

  console.log('ChipPinDiagram - 后备方案图例项目:', result);
  return result;
})

// 图例尺寸计算（侧边布局）- 等比例缩小所有字体
const legendIconSize = computed(() => Math.max(8, 10 * adaptiveScale.value) * 0.7)
const legendItemSpacing = computed(() => Math.max(16, 18 * adaptiveScale.value) * 0.8)
const legendTitleFontSize = computed(() => Math.max(8, 10 * adaptiveScale.value) * 0.6)
const legendTextFontSize = computed(() => Math.max(6, 8 * adaptiveScale.value) * 0.6)

const legendBoxWidth = computed(() => {
  if (legendItems.value.length === 0) return 0
  return Math.max(75, 90 * adaptiveScale.value) * 0.8
})

const legendBoxHeight = computed(() => {
  if (legendItems.value.length === 0) return 0
  // 上边距5px，下边距10px，标题高度约20px，项目间距计算
  return (10 + 20 + legendItems.value.length * legendItemSpacing.value + 10) * 0.8
})

// 布局参数（侧边图例）
const chipAreaMargin = 10 // 芯片区域的边距（增加上下边距为50px）
const legendToChipSpacing = computed(() => Math.max(40, 60 * adaptiveScale.value) * 0.8) // 图例与芯片之间的间距（缩小20%）

// 芯片区域计算（居中布局）
const chipAreaX = computed(() => {
  const pinHeight = (25 + 2) * adaptiveScale.value * props.pinSize
  return chipAreaMargin + pinHeight // 为左侧引脚预留空间
})
const chipAreaY = computed(() => {
  // 确保顶部引脚不会超出上边距
  const pinHeight = (25 + 2) * adaptiveScale.value * props.pinSize
  return chipAreaMargin + pinHeight
})

// 图例位置计算（右侧）
const legendX = computed(() => {
  const chipAreaWidth = chipSize.value + pinOffset.value * 2
  return chipAreaX.value + chipAreaWidth + legendToChipSpacing.value
})
const legendY = computed(() => {
  const chipAreaHeight = chipSize.value + pinOffset.value * 2
  const chipCenterY = chipAreaY.value + chipAreaHeight / 2
  return chipCenterY - legendBoxHeight.value / 2
})

// 自适应尺寸计算
const adaptiveScale = computed(() => {
  if (!containerSize.value.width || !containerSize.value.height) return 1

  // 根据引脚数量计算基础尺寸
  const pinsPerSideCount = Math.ceil(props.pinCount / 4);
  const baseChipSize = 300;
  const pinSize = 25;
  const pinOffset = 10;
  const margin = 20;

  // 计算所需的最小尺寸
  const requiredWidth = baseChipSize + (pinOffset + pinSize) * 2 + margin * 2;
  const requiredHeight = baseChipSize + (pinOffset + pinSize) * 2 + margin * 2;

  // 如果显示图例，增加宽度
  const legendWidth = props.showLegend && legendItems.value.length > 0 ? 200 : 0;
  const totalRequiredWidth = requiredWidth + legendWidth;

  // 计算容器可用空间（留出边距）
  const availableWidth = containerSize.value.width - 20;
  const availableHeight = containerSize.value.height - 20;

  // 计算缩放比例，确保所有内容都能显示
  const scaleX = availableWidth / totalRequiredWidth;
  const scaleY = availableHeight / requiredHeight;

  // 取较小值以确保完全适配，最小缩放0.3，最大缩放2
  return Math.max(0.3, Math.min(scaleX, scaleY, 2));
})

// 芯片尺寸计算（基于自适应缩放）
const baseChipSize = computed(() => {
  // 根据引脚数量动态调整芯片大小
  const minSize = 300;
  const sizeMultiplier = Math.max(1, Math.sqrt(props.pinCount / 32)); // 32引脚为基准
  return minSize * sizeMultiplier * adaptiveScale.value;
})
const chipSize = computed(() => baseChipSize.value * props.pinSize)
const pinOffset = computed(() => 20 * adaptiveScale.value * props.pinSize) // 设置间距为5
const chipX = computed(() => pinOffset.value)
const chipY = computed(() => pinOffset.value)
const pinsPerSide = computed(() => Math.ceil(props.pinCount / 4))
const pinFontSize = computed(() => Math.max(6, 8 * adaptiveScale.value * props.pinSize))
const chipModelFontSize = computed(() => Math.min(24 * adaptiveScale.value * props.pinSize, chipSize.value / 8))

// SVG总尺寸计算（侧边图例布局）
const svgWidth = computed(() => {
  const pinHeight = (25 + 2) * adaptiveScale.value * props.pinSize
  // 为左侧引脚预留额外空间
  const chipAreaWidth = chipSize.value + pinOffset.value * 2 + chipAreaMargin * 2 + pinHeight
  const legendWidth = props.showLegend && legendItems.value.length > 0
    ? legendToChipSpacing.value + legendBoxWidth.value + 20
    : 0
  return chipAreaWidth + legendWidth
})

const svgHeight = computed(() => {
  // 计算引脚高度
  const pinHeight = (25 + 2) * adaptiveScale.value * props.pinSize

  // 现在chipAreaY已经包含了顶部引脚的空间(chipAreaMargin + pinHeight)
  // 内容高度：chipY(pinOffset) + 芯片高度 + pinOffset + 底部引脚高度
  // 总高度：chipAreaY + 内容高度 + 下边距(chipAreaMargin)
  const contentHeight = pinOffset.value + chipSize.value + pinOffset.value + pinHeight
  const totalHeight = chipAreaY.value + contentHeight + chipAreaMargin

  const legendHeight = props.showLegend && legendItems.value.length > 0
    ? legendBoxHeight.value + 40
    : 0

  return Math.max(totalHeight, legendHeight)
})

// SVG样式计算
const svgStyle = computed(() => {
  return {
    width: '100%',
    height: '100%',
    maxWidth: '100%',
    maxHeight: '100%'
  }
})

// 引脚位置计算（自适应缩放）
const pinPositions = computed(() => {
  const actualPinsPerSide = pinsPerSide.value;
  const step = chipSize.value / (actualPinsPerSide + 1) // 引脚间距
  const pins = []
  const pinWidth = (10 + 2) * adaptiveScale.value * props.pinSize
  const pinHeight = (25 + 2) * adaptiveScale.value * props.pinSize

  // 计算实际需要显示的引脚数量
  const totalPins = props.pinCount;
  let pinIndex = 1;

  // 顶部引脚
  for (let i = 0; i < actualPinsPerSide && pinIndex <= totalPins; i++) {
    const offset = step * (i + 1)
    pins.push({
      x: chipX.value + offset,
      y: chipY.value - pinOffset.value,
      rectX: -pinWidth / 2,
      rectY: -pinHeight,
      rectW: pinWidth,
      rectH: pinHeight,
      label: `PIN${pinIndex}`,
    })
    pinIndex++;
  }

  // 右侧引脚
  for (let i = 0; i < actualPinsPerSide && pinIndex <= totalPins; i++) {
    const offset = step * (i + 1)
    pins.push({
      x: chipX.value + chipSize.value + pinOffset.value,
      y: chipY.value + offset,
      rectX: 0,
      rectY: -pinWidth / 2,
      rectW: pinHeight,
      rectH: pinWidth,
      label: `PIN${pinIndex}`,
    })
    pinIndex++;
  }

  // 底部引脚（从右到左）
  for (let i = 0; i < actualPinsPerSide && pinIndex <= totalPins; i++) {
    const offset = step * (i + 1)
    pins.push({
      x: chipX.value + chipSize.value - offset,
      y: chipY.value + chipSize.value + pinOffset.value,
      rectX: -pinWidth / 2,
      rectY: 0,
      rectW: pinWidth,
      rectH: pinHeight,
      label: `PIN${pinIndex}`,
    })
    pinIndex++;
  }

  // 左侧引脚（从下到上）
  for (let i = 0; i < actualPinsPerSide && pinIndex <= totalPins; i++) {
    const offset = step * (i + 1)
    pins.push({
      x: chipX.value - pinOffset.value,
      y: chipY.value + chipSize.value - offset,
      rectX: -pinHeight,
      rectY: -pinWidth / 2,
      rectW: pinHeight,
      rectH: pinWidth,
      label: `PIN${pinIndex}`,
    })
    pinIndex++;
  }

  return pins
})
// 选中状态和辅助函数
const selectedPin = ref(null)
const emit = defineEmits(['pinClick', 'pinEdit'])

const getPinColor = (pin) => {
  // 优先检查外部传入的highlightedPin prop
  if (props.highlightedPin !== null) {
    // 从pin.label中提取引脚编号 (PIN1 -> 1)
    const pinNumber = pin.label.replace('PIN', '');
    if (pinNumber === props.highlightedPin.toString()) {
      console.log(`ChipPinDiagram - 引脚 ${pin.label} 高亮匹配成功, pinNumber: ${pinNumber}, highlightedPin: ${props.highlightedPin}`);
      return '#FF4500' // 高亮颜色
    }
  }
  // 如果没有外部高亮，检查内部选中状态
  else if (selectedPin.value === pin.label) {
    return '#FF4500'
  }

  // 查找引脚数据并获取类型
  const pinData = props.pins.find((p) => `PIN${p.pinId}` === pin.label);
  if (pinData) {
    const type = pinData.pinType || pinData.type || pinData.displayType || 'DEFAULT';
    // console.log(`ChipPinDiagram - 引脚 ${pin.label} 类型: ${type}, 颜色: ${pinColors.value[type] || pinColors.value.DEFAULT}`);
    return pinColors.value[type] || pinColors.value.DEFAULT;
  }

  return pinColors.value.DEFAULT;
}

const getPinNumber = (pin) => {
  const pinData = props.pins.find((p) => `PIN${p.pinId}` === pin.label)
  return pinData ? pinData.pinId : '未知'
}

const onPinClick = (pin) => {
  const pinId = pin.label.replace('PIN', '')
  const pinData = props.pins.find((p) => p.pinId.toString() === pinId)

  if (pinData) {
    // 设置选中状态
    selectedPin.value = pin.label

    // 触发编辑事件，传递完整的引脚数据
    emit('pinEdit', {
      pinId: pinData.pinId,
      pinName: pinData.pinName || `Pin${pinData.pinId}`,
      pinType: pinData.pinType || 'GPIO',
      desc: pinData.desc || '',
      status: pinData.status || '可用',
      electricalType: pinData.electricalType || 'TTL'
    })
  } else {
    // 如果没有找到引脚数据，创建默认数据
    selectedPin.value = pin.label
    emit('pinEdit', {
      pinId: parseInt(pinId),
      pinName: `Pin${pinId}`,
      pinType: 'GPIO',
      desc: '',
      status: '可用',
      electricalType: 'TTL'
    })
  }
}

// 容器尺寸检测
const updateContainerSize = () => {
  if (containerRef.value) {
    const rect = containerRef.value.getBoundingClientRect()
    containerSize.value = {
      width: rect.width || 800,
      height: rect.height || 600
    }
  }
}

// 生命周期钩子
onMounted(async () => {
  await nextTick()
  updateContainerSize()

  // 设置ResizeObserver监听容器尺寸变化
  if (containerRef.value && window.ResizeObserver) {
    resizeObserver.value = new ResizeObserver(() => {
      updateContainerSize()
    })
    resizeObserver.value.observe(containerRef.value)
  }

  // 监听窗口尺寸变化作为备选方案
  window.addEventListener('resize', updateContainerSize)
})

onUnmounted(() => {
  if (resizeObserver.value) {
    resizeObserver.value.disconnect()
  }
  window.removeEventListener('resize', updateContainerSize)
})

watch(() => props.pins, () => {
  selectedPin.value = null
}, { deep: true })
</script>

<style scoped>
.pin-diagram-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  min-height: 400px;
  padding: 0;
  box-sizing: border-box;
  overflow: hidden;
  position: relative;
}

.pin-group {
  cursor: pointer;
  /* 移除悬停闪烁效果 */
}

/* SVG自适应样式 */
svg {
  display: block;
  overflow: visible;
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
}
</style>