<template>
    <div style="display: flex; flex-direction: column; height: calc(100vh - 200px); margin-right: 20px;">

        <div class="tool-bar-container">
            <el-button icon="Plus" type="primary" @click="handleAdd">派发点检任务</el-button>
            <div style="margin-left: auto; display: flex; gap: 10px;">

                <el-date-picker v-model="form.date" type="daterange" unlink-panels range-separator="至" start-placeholder="开始日期"
                    end-placeholder="结束日期" :shortcuts="shortcuts" size="default" @change="onFilter" />

                <el-tooltip class="box-item" effect="dark" content="重置已添加的筛选条件" placement="top-start">
                    <el-button text bg @click="handleReset">重置</el-button>
                </el-tooltip>
                <filterButton @click="onFilterStatusChange" :count="filterCount" />
                <el-button icon="Refresh" text bg @click="handleRefresh">刷新</el-button>
                <el-button icon="Download" text bg @click="handleLoad">下载</el-button>
            </div>
        </div>

        <div class="filter-container" v-if="showFilterContainer">
            <el-input v-model="form.item_name" placeholder="请输入点检项名称" @keyup.enter="onFilter" clearable>
                <template #append>
                    <el-button icon="Search" @click="onFilter"></el-button>
                </template>
            </el-input>
            <el-input v-model="form.item_code" placeholder="请输入点检项编号" @keyup.enter="onFilter" clearable>
                <template #append>
                    <el-button icon="Search" @click="onFilter"></el-button>
                </template>
            </el-input>
        </div>

        <el-table :data="tableData" stripe border row-key="id" style="width: 100%; table-layout: fixed; flex: 1; overflow: auto;">

            <el-table-column prop="item_name" label="点检项名称" min-width="200" align="center"></el-table-column>
            <el-table-column prop="item_code" label="点检项编号" min-width="200" align="center"></el-table-column>
            <el-table-column prop="time_date" label="点检时间" min-width="130" align="center"></el-table-column>
            <el-table-column prop="person_name" label="点检人员" min-width="100" align="center"></el-table-column>
            <el-table-column prop="is_pass" label="点检状态" min-width="100" align="center">
                <template #default="{ row }">
                    <el-tag v-if="row.is_pass == null" type="info">未点检</el-tag>
                    <el-tag v-else-if="row.is_pass" type="success">通过</el-tag>
                    <el-tag v-else type="danger">不通过</el-tag>
                </template>
            </el-table-column>
            <el-table-column prop="desc" label="点检描述" min-width="400" align="center"></el-table-column>
            <el-table-column label="操作" min-width="290" fixed="right" align="center">
                <template #default="{ row }">
                    <div style="display: flex; justify-content: center; gap: 10px;">
                        <el-button type="primary" size="small" @click="handleEdit(row)">编辑</el-button>
                    </div>
                </template>
            </el-table-column>

        </el-table>

        <div class="pagination-container">
            <el-pagination :page-sizes="[1, 10, 15, 20, 25, 50, 100]" layout="prev, pager, next, jumper, total, sizes"
                :total="total" v-model:current-page="form.page" v-model:page-size="form.pagesize" background
                @change="onPageChange" />
        </div>

        <el-dialog v-if="dialogAddVisible" v-model="dialogAddVisible" title="派发点检任务" width="800"
            :close-on-click-modal="false">
            <Add @confirm="onAddConfirm" @cancel="onAddCancel" :parent="parent" />
        </el-dialog>

        <el-dialog v-if="dialogEditVisible" v-model="dialogEditVisible" title="点检项点检" width="800"
            :close-on-click-modal="false">
            <Edit @confirm="onEditAffirm" @cancel="onEditCancel" :r_id="r_id" />
        </el-dialog>
    </div>

</template>


<script setup>
import { ref, reactive, onMounted } from 'vue';
import http from '@/utils/http/http.js';
import Add from './add.vue';
import Edit from './edit.vue';
import dayjs from 'dayjs';
import { ElMessage } from 'element-plus';

const tableData = ref([]);
const total = ref(0);
const filterCount = ref(0);

let r_id = ref(0);

const dialogAddVisible = ref(false);

const dialogEditVisible = ref(false);

let form = reactive({
    page: 1,
    pagesize: 10,
    date: [new Date(), new Date()],
});

const shortcuts = [
  {
    text: '昨天',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24)
      end.setTime(end.getTime() - 3600 * 1000 * 24)
      return [start, end]
    },
  },
  {
    text: '今天',
    value: () => {
      const end = new Date()
      const start = new Date()
      return [start, end]
    },
  },
  {
    text: '这个月',
    value: () => {
        const start = new Date(new Date().getFullYear(), new Date().getMonth(), 1)
        const end = new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0)
      return [start, end]
    },
  },
  {
    text: '上个月',
    value: () => {
      const start = new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1)
      const end = new Date(new Date().getFullYear(), new Date().getMonth(), 0)
      return [start, end]
    },
  },
]

let showFilterContainer = ref(false);

function update_table() {

    let data = {
        ...form,
    };
    if (form.date && form.date.length === 2) {
        data.start_time = dayjs(form.date[0]).format('YYYY-MM-DD');
        data.end_time = dayjs(form.date[1]).format('YYYY-MM-DD');
    } else {
        delete data.start_time;
        delete data.end_time;
    }
    delete data.date;

    http.get('/inspecs/records', { params: data }).then(res => {
        let items = res.data.data.results;
        tableData.value = items;
        total.value = res.data.data.count;
    });

    filterCount.value = Object.keys(form).filter(key => !['page', 'pagesize', 'date'].includes(key)).reduce((count, key) => {
        if (form[key] == '' || form[key] == undefined || form[key] == null || form[key].length == 0) {
            return count;
        } else {
            return count + 1;
        }
    }, 0)
};

function onFilterStatusChange() {
    showFilterContainer.value = !showFilterContainer.value;
};

function onFilter() {
    update_table();
};

function handleAdd() {
    parent.value = null;
    dialogAddVisible.value = true;
};

function onPageChange() {
    update_table();
};

function handleReset() {
    Object.keys(form).forEach(key => {
        delete form[key];
    });
    form.page = 1;
    form.pagesize = 10;
    form.date = [new Date(), new Date()];
    showFilterContainer.value = false;
    update_table();
};


function handleEdit(row) {
    r_id.value = row.id;
    dialogEditVisible.value = true;
};


function onAddConfirm() {
    dialogAddVisible.value = false;
    update_table();
};

function onAddCancel() {
    dialogAddVisible.value = false;
};

function onEditAffirm() {
    dialogEditVisible.value = false;
    update_table();
};

function onEditCancel() {
    dialogEditVisible.value = false;
};

function handleRefresh() {
    update_table();
};

function handleLoad() {
    // let url = import.meta.env.VITE_BASE_URL + '/inspecs/records/excel_report';

    // let params = new URLSearchParams();
    // let start_time;
    // let end_time;

    //  if (form.date && form.date.length === 2) {
    //     start_time = dayjs(form.date[0]).format('YYYY-MM-DD');
    //     end_time = dayjs(form.date[1]).format('YYYY-MM-DD');
    // } else {
    //     start_time = null;
    //     end_time = null;
    // }
    // params.append("start_time", start_time)
    // params.append("end_time", end_time)

    // url = url + '?' + params.toString();

    // window.open(url);

    let start_time;
    let end_time;

     if (form.date && form.date.length === 2) {
        start_time = dayjs(form.date[0]).format('YYYY-MM-DD');
        end_time = dayjs(form.date[1]).format('YYYY-MM-DD');
    } else {
        start_time = null;
        end_time = null;
    }

    http.downloadFile('/inspecs/records/excel_report', {
        params: {
            start_time: start_time,
            end_time: end_time,
        }
    }).then(res => {
        ElMessage({
            message: '下载成功',
            type: 'success',
        });
    }).catch(err => {
        ElMessageBox.alert(err.response.data.msg, '下载失败', {
            confirmButtonText: '确定',
            type: 'error',
        });
    });
};

onMounted(() => {
    update_table();
});

</script>


<style scoped>
.search-container {
    width: 100%;
    display: flex;
    justify-content: center;
    justify-items: center;
    height: 40px;
}

.search-input {
    width: 50%;
    max-width: 500px;
}

.search-button {
    border: 1px solid #dcdfe6;
}

.filter-container {
    width: 100%;
    display: flex;
    justify-content: flex-start;

    margin-bottom: 10px;

    .el-input {
        width: 50%;
        max-width: 300px;
        margin-right: 10px;
    }
}

.tool-bar-container {
    display: flex;
    justify-content: flex-start;

}

.mind_map {
    margin-left: 10px;


}

.pagination-container {
  margin-top: auto; /* 自动顶到底部 */
  align-self: flex-start; /* 左对齐 */
  padding-top: 15px;
  width: 100%;
}

</style>