<template>
  <div class="project-management-container" v-custom-loading="loading">
    <!-- Project Information Header -->


    <!-- Input and Action Row -->
    <div class="input-action-row">
      <el-button icon="Plus"  class="action-button" type="primary" @click="createproject">新建仓库</el-button>
    </div>
     <create 
      v-if="createDialogVisible" 
      @success="onCreateSuccess"
      @close="handleDialogClose"
      @refresh="getProjectList"
      @sdk-created="handleSdkCreated"
      v-model="createDialogVisible"
      :project_code="project_code"
      :project_name="project_name"
      @update:formLoading="(status) => {loading = status
      console.log('formLoading updated:', status);}
      " 
      
    />
    <SdkInit 
    v-if="sdk_config" 
    @success="onCreateSuccess"
    :modelValue="sdk_config"
    :form-data="sdkFormData"
    :functional-modules="sdkFunctionalModules"
    :pick-chips="sdkPickChips"
    :chip-list="sdkChipList"
    :project_description="project_description"
    @update:modelValue="sdk_config = $event"
    @update:submitLoading="(status) => {loading = status
    console.log('formLoading updated:', status);}" 
    :project_code="project_code"
    :project_name="project_name"
  />
    <!-- Project List Table -->
    <el-table :data="paginatedData" stripe border style="width: 100%; margin-top: 20px;" class="custom-table">
        

        <el-table-column label="所属项目" min-width="300" align="center" >
        <template #default="{ row }">
          <div>{{ row.description }}({{ row.code }})</div>
        </template>
      </el-table-column>
      <el-table-column prop="gitlab" label="GitLab仓库" min-width="300"  align="center" />
      <!-- <el-table-column prop="versionRules" label="版本规则" min-width="200"  header-align="center" align="center"></el-table-column> -->
      <el-table-column label="操作" width="220" fixed="right"  align="center">
        <template #default="{ row }" >
           <div style="display: flex; justify-content: center; gap: 10px;">
              <el-button size="small" type="primary" @click="handleTestSubmit(row)">提测</el-button>
              <el-button size="small" type="primary"  @click="handleDetail(row)"> 详情 </el-button>
              <el-tooltip content="删除功能仅删除数据库，不删除gitlab仓库" placement="top">
                <!-- <el-button link type="danger" @click="handleDelete(row)">删除</el-button> -->
                <el-button size="small" type="danger" @click="openDeleteDialog(row)">删除</el-button>
                
              </el-tooltip>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <!-- 删除弹框 -->
    <el-dialog
      v-model="deleteDialogVisible"
      title="确认删除"
      width="400px"
    >
      <span>是否确认删除该项目？删除仅影响数据库，GitLab 仓库不受影响。</span>
      <template #footer>
        <el-button @click="deleteDialogVisible = false">取消</el-button>
        <el-button type="danger" @click="confirmDelete">删除</el-button>
      </template>
    </el-dialog>
    <div class = "custom-pagination">
      <el-pagination
          style=" text-align: right;"
          background
          layout="prev, pager, next, jumper, total, sizes"
          :page-sizes="[10, 20, 30, 50]"
          :current-page="currentPage"
          :page-size="pageSize"
          :total="tableData.length"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          />
    </div>
    <!-- Instructions -->
    
    <!-- 版本提测弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      title="新增软件版本提测"
      width="50%"
      top="15vh"
      :fullscreen="false"
      center
    >
      <el-form :model="submissionForm" label-width="220px" :rules="rules" ref="submissionFormRef">
        <el-form-item label="所属项目" prop="project">
          <Projects ref="projectsRef" v-model="submissionForm.project" :includePrefix="false" @change="onProjectChange"    :disabled="true" />
        </el-form-item>
        <el-form-item label="产品软件类型" prop="softwareType">
          <el-select v-model="submissionForm.softwareType" placeholder="请选择产品软件类型" style="width: 100%" @change="handleSoftwareTypeChange">
            <el-option v-for="item in versionProfiles" :key="item.id" :label="item.version_type" :value="item.id" />
          </el-select>
        </el-form-item>
        <!-- 动态渲染 version_meta 字段（必填） -->
        <template v-if="selectedVersionProfile && selectedVersionProfile.version_meta">
          <el-form-item
            v-for="meta in selectedVersionProfile.version_meta"
            :key="meta.code"
            :label="meta.name"
            :prop="'version_fields.' + meta.code"
            :rules="[{ required: true, message: `请输入${meta.name}`, trigger: ['blur', 'change'] }]"
          >
            <el-input
              v-model="submissionForm.version_fields[meta.code]"
              :placeholder="meta.meaning || `请输入${meta.name}`"
            />
          </el-form-item>
        </template>
        <el-form-item label="版本用途" prop="version_usage">
          <el-select v-model="submissionForm.version_usage" placeholder="请选择版本用途" style="width: 100%"> 
            <el-option label="正式" value="RELEASE" />
            <el-option label="临时" value="TEMP" />
            <el-option label="实验" value="EXPERIMENTAL" />
          </el-select>
        </el-form-item>
        <el-form-item label="软件版本变更内容" prop="change_description">
          <el-input 
            v-model="submissionForm.change_description" 
            type="textarea" 
            rows="6"
            style="width: 100%; min-height: 150px;"
            placeholder="请输入软件版本变更内容描述" 
          />
        </el-form-item>
        <el-form-item label="工程组" prop="engineeringProject">
          <el-input 
            v-model="submissionForm.engineeringProject" 
            placeholder="工程组" 
            style="width: 100%"
            :disabled="true"
            readonly
          />
        </el-form-item>
        <el-form-item label="工程路径" prop="engineeringPath">
          <el-input 
            v-model="submissionForm.engineeringPath" 
            placeholder="工程路径" 
            style="width: 100%"
            :disabled="true"
            readonly
          />
        </el-form-item>
        <el-form-item label="源分支" prop="originalBranch">
          <el-input 
            v-model="submissionForm.originalBranch" 
            placeholder="源分支" 
            style="width: 100%"
            :disabled="true"
            readonly
          />
        </el-form-item>
        <el-form-item label="目标分支" prop="targetBranch">
          <el-input 
            v-model="submissionForm.targetBranch" 
            placeholder="目标分支" 
            style="width: 100%"
            :disabled="true"
            readonly
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </span>
      </template>
    </el-dialog>


        <!-- 详情弹窗 -->
        <el-dialog
        v-model="dialogVersion"
        width="600px"
        >
        <div class="custom-dialog-header">
              <span>SDK 项目信息</span>
            </div>
        <el-table :data="sdkData" border style="width: 100%;">
            <el-table-column prop="chip" label="芯片平台" width="120px" align="center"/>
            <el-table-column label="功能模块"  align="center">
            <template #default="{ row }">
                <el-tag
                v-for="(module, idx) in row.modules"
                :key="idx"
                type="info"
                size="small"
                style="margin: 2px;"
                >
                {{ module }}
                </el-tag>
                <span v-if="!row.modules || row.modules.length === 0" style="color: #999;">无数据</span>
            </template>
            </el-table-column>
        </el-table>

        <template #footer>
            <el-button type="primary" @click="dialogVersion = false">关闭</el-button>
        </template>
        </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch, computed, watchEffect, nextTick } from 'vue';
import { ElMessage, ElMessageBox, type FormInstance } from 'element-plus';
import http from '@/utils/http/http';
import { useProjectStore } from '@/stores/project';
import Projects from '@/components/projects.vue';
import { useRoute, useRouter } from 'vue-router';
import create from '@/views/project_repository/create.vue';
import SdkInit from '@/views/project_repository/sdk_init.vue';

const loading = ref(false);
const router = useRouter();

// 版本详情
const dialogVersion = ref(false);


// 创建仓库
const createDialogVisible = ref(false)


const sdkData =  ref([])

// sdk 详情
const sdk_config = ref(false);

// 用于控制界面只有一条弹框消息
let currentMessage = null;


// 类型定义
interface ProjectItem {
  name: string;
  description: string;
  versionRules: string;
  gitlab: string;
  code: string;
  isSDKBased?: boolean;
}

interface ProjectBranch {
  project: string;
  branch: string[];
}

interface SubmissionFormData {
  id: string;
  project: string;
  softwareType: string;
  change_description: string;
  engineeringProject: string;
  engineeringPath: string;
  fullEngineeringPath?: string;
  originalBranch: string;
  targetBranch: string;
  status: string;
  version_usage: string;
  version_fields: Record<string, string>;
}

// 新增：版本类型配置
const versionProfiles = ref([]); // 存储API返回的所有类型
const selectedVersionProfile = ref(null); // 当前选中的类型对象

// 定义工程名称下拉选项
const projectOptions = ref<string[]>([]);
// 加载状态
const projectLoading = ref(false);


// 新增：存储从create.vue接收的sdk相关数据
const sdkFormData = ref({});
const sdkFunctionalModules = ref([]);
const sdkPickChips = ref([]);
const sdkChipList = ref([]);
const project_description = ref('');
// 处理从create.vue传来的sdk-created事件
const handleSdkCreated = (data) => {
  // 存储接收的数据
  sdkFormData.value = data.formData;
  sdkFunctionalModules.value = data.functionalModules;
  sdkPickChips.value = data.pickChips;
  sdkChipList.value = data.chipList;
  project_description.value = data.formData.projectDesc;
  // 打开sdk_init弹窗
  sdk_config.value = true;
  console.log("sdk_config:", sdk_config.value)
  console.log('从create组件接收的数据已转发给sdk_init:', data);
};


// 创建sdk工程
const handleConfigGitlab = () => {
    console.log('配置SDK仓库');
    sdk_config.value = true;
    router.push('/projects_repository/sdk')
};


// 表单数据统一管理
const formdata = reactive({
  selectedGroup: '',
  projectName: '',
  projectDescription: '',
  versionRule: '',
});

const teamdata = ref<string[]>([]);
const tableData = ref<ProjectItem[]>([]);

// 分页数据
const currentPage = ref(1);
const pageSize = ref(10);

// 分页后的数据
const paginatedData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return tableData.value.slice(start, end);
});

// 版本提测相关数据
const dialogVisible = ref(false);
const submissionFormRef = ref<FormInstance>();
const projectsRef = ref();

// 表单数据
const submissionForm = reactive<SubmissionFormData>({
  id: '',
  project: '',
  softwareType: '',
  change_description: '',
  engineeringProject: '',
  engineeringPath: '',
  fullEngineeringPath: '',
  originalBranch: 'dev',
  targetBranch: 'release',
  status: '待审核',
  version_usage: '',
  version_fields: {}
});

// 项目和分支数据
const projectsAndBranches = ref<ProjectBranch[]>([]);


const isEngineeringPathAutoFilled = ref(false);
// 表单验证规则
const rules = {
  project: [{ required: true, message: '请选择所属项目', trigger: 'change' }],
  softwareType: [
    { required: true, message: '请选择产品软件类型', trigger: 'change' }
  ],
  version_usage: [{ required: true, message: '请选择版本用途', trigger: 'change' }],
  change_description: [{ required: false, message: '请输入软件版本变更内容', trigger: 'blur' }],
  engineeringProject: [{ required: true, message: '请输入工程组', trigger: 'blur' }],
  engineeringPath: [{ required: true, message: '请输入工程路径', trigger: 'blur' }],
  originalBranch: [{ required: true, message: '请输入源分支', trigger: 'blur' }],
  targetBranch: [{ required: true, message: '请输入目标分支', trigger: 'blur' }]
};


const handleSizeChange = (val: number) => {
  pageSize.value = val;
  currentPage.value = 1; // 切换页大小时返回首页
};

const handleCurrentChange = (val: number) => {
  currentPage.value = val;
};

// 项目号
let project_code = "";
// 项目名称
let project_name = "";

const projectStore = useProjectStore();
// 处理项目信息可能为空的情况
if (projectStore.project_info && typeof projectStore.project_info === 'object') {
  project_code = (projectStore.project_info as any).projectCode || '';
  project_name = (projectStore.project_info as any).name || '';
}





// 重置表单
const resetForm = () => {
  if (submissionFormRef.value) {
    submissionFormRef.value.resetFields();
  }
  submissionForm.id = '';
  submissionForm.project = '';
  submissionForm.softwareType = '';
  submissionForm.change_description = '';
  submissionForm.engineeringProject = '';
  submissionForm.engineeringPath = '';
  submissionForm.fullEngineeringPath = '';
  submissionForm.originalBranch = 'dev';
  submissionForm.targetBranch = 'release';
  submissionForm.status = '待审核';
  submissionForm.version_usage = '';
  // 清空动态字段
  submissionForm.version_fields = {};
  selectedVersionProfile.value = null;
};

// 提交表单
const submitForm = () => {
  submissionFormRef.value?.validate((valid) => {
    if (valid) {
      // 获取项目信息并拼接项目名称和代码
      const projectInfo = projectsRef.value.getProjectInfo(submissionForm.project);
      const projectDisplay = projectInfo ? `${projectInfo.name}(${projectInfo.projectCode})` : submissionForm.project;
      // 打包表单数据
      const formData = {
        project: projectDisplay,
        software_type: selectedVersionProfile.value ? selectedVersionProfile.value.version_type : '',
        version_fields: { ...submissionForm.version_fields }, // 动态字段对象
        change_description: submissionForm.change_description,
        engineering_group: submissionForm.engineeringProject,
        engineering_path: submissionForm.fullEngineeringPath || submissionForm.engineeringPath,
        source_branch: submissionForm.originalBranch,
        target_branch: submissionForm.targetBranch,
        version_usage: submissionForm.version_usage, // 添加版本用途字段
        status: '待审核'
      };
      http.post('/softtrack/submitsoftware', formData)
        .then(response => {
          if (response.data && response.data.id) {
            ElMessage.success('新增提测成功');
            dialogVisible.value = false;
          } else {
            ElMessage.error(`提交失败: ${response.data.message || '未知错误'}`);
          }
        })
        .catch(error => {
          console.error('提交提测信息失败:', error);
          ElMessage.error(`提交失败: ${error.message || '网络错误'}`);
        });
    }
  });
};

// 项目选择变更处理
const onProjectChange = (projectInfo: any) => {
  if (projectInfo) {
    console.log('项目已变更:', projectInfo);
  }
};

// 处理软件类型变更
const handleSoftwareTypeChange = async (value) => {
  // 查找选中的类型对象
  selectedVersionProfile.value = versionProfiles.value.find(v => v.id === value);
  // 重置并初始化动态字段
  submissionForm.version_fields = {};
  if (selectedVersionProfile.value && selectedVersionProfile.value.version_meta) {
    selectedVersionProfile.value.version_meta.forEach(meta => {
      submissionForm.version_fields[meta.code] = '';
    });
  }
  await nextTick();
  submissionFormRef.value?.clearValidate();
};



// 获取工程列表信息
const getProjectList = () => {
    // 获取数据库数据
    console.log('项目信息 project_code:', project_code);
    http.get('/code_management/project_info',{
        params:{project_code:project_code}
    }).then(response => {
        console.log('项目数据库请求:', response.data);
        if (response.data.select_status === 1) {
            // 获取工程
            // 获取gitlab项目信息
            // 获取版本规则
            tableData.value = response.data.table_data;
            currentPage.value = 1; // 重置页码
        } else {
            console.info('项目数据库请求失败');
        }
    }).catch(error => {
        console.error('项目数据库请求失败:', error);
    });
};

// 监控项目信息变化
watch(() => projectStore.project_info, ( newval, oldval) => {
    if (newval && typeof newval === 'object') {
      project_code = (newval as any).projectCode || '';
      project_name = (newval as any).name || '';
      console.log('当前项目号：', project_code);
      console.log('当前项目名称：', project_name);
      getProjectList();
    }
}, { immediate: true, deep: true });





const createproject = () => {
  if ((project_code === undefined && project_name === undefined) || (project_code === "" && project_name === "")) {
    // 判断界面是否存在ElMessage，有则关闭
    if (currentMessage) {
      currentMessage.close();
    }
    currentMessage = ElMessage.warning("请先选择项目");
    return; // 不显示弹窗
  }
  // 显示创建弹窗
  createDialogVisible.value = true;
  console.log('尝试显示弹窗，createDialogVisible:', createDialogVisible.value);
};


// 创建项目成功后的回调
const onCreateSuccess = () => {
  // ElMessage.success('项目创建成功');
  createDialogVisible.value = false;
  sdk_config.value = false;
  getProjectList(); // 重新获取项目列表，刷新表格数据
};

// 关闭创建弹窗
const handleDialogClose = () => {
  console.log('弹窗已关闭');
  createDialogVisible.value = false;
};

const parseGitlabUrl = (gitlabUrl: string) => {
  try {
    // 匹配 http://10.1.1.99/ 后面的路径
    const match = gitlabUrl.match(/http:\/\/10\.1\.1\.99\/(.+)/);
    if (match && match[1]) {
      const fullPath = match[1];
      const pathParts = fullPath.split('/');
      
      if (pathParts.length > 0) {
        const engineeringGroup = pathParts[0]; // 第一部分作为工程组
        
        // 提取可能的项目标识符（最后一个部分和完整路径）
        const projectIdentifiers = {
          lastPart: pathParts[pathParts.length - 1], // 最后一部分，如 'test'
          fullPath: fullPath, // 完整路径，如 'mcu-team/alps/test'
          pathWithoutGroup: pathParts.slice(1).join('/') // 去掉工程组的路径，如 'alps/test'
        };
        
        return {
          engineeringGroup,
          projectIdentifiers,
          fullPath
        };
      }
    }
    return null;
  } catch (error) {
    console.error('解析GitLab地址失败:', error);
    return null;
  }
};


// 提测操作 - 简化版本，直接从GitLab URL提取信息
const handleTestSubmit = (row: ProjectItem) => {
  console.log('提交测试', row);
  resetForm();
  submissionForm.project = row.code;

  // 初始化 version_fields（如果已有选中的类型）
  submissionForm.version_fields = {};
  if (submissionForm.softwareType && versionProfiles.value.length > 0) {
    const profile = versionProfiles.value.find(v => v.id === submissionForm.softwareType);
    if (profile && profile.version_meta) {
      profile.version_meta.forEach(meta => {
        submissionForm.version_fields[meta.code] = '';
      });
    }
  }

  // 解析GitLab地址并自动填充所有字段
  if (row.gitlab) {
    const parsed = parseGitlabUrl(row.gitlab);
    if (parsed) {
      console.log('解析GitLab地址成功:', parsed);
      // 直接设置所有字段
      submissionForm.engineeringProject = parsed.engineeringGroup;
      submissionForm.engineeringPath = parsed.projectIdentifiers.lastPart;
      submissionForm.fullEngineeringPath = parsed.fullPath;
      // 固定分支
      submissionForm.originalBranch = 'dev';
      submissionForm.targetBranch = 'release';
    } else {
      console.warn('GitLab地址解析失败:', row.gitlab);
    }
  }

  dialogVisible.value = true;
};



// 查看详情
const handleDetail = (row: ProjectItem) => {
  if (!row?.code) {
    ElMessage.error("项目数据无效")
    return
  }

  http.get("/code_management/sdk_info", {
    params: {
      project_code: row.code,
      project_name: row.description,
      version_rule: row.versionRules,
      project_gitlab: row.gitlab
    }
  }).then(response => {
    console.log('查看详情返回数据:', response.data);
    const data = response.data
    if (data.sdk_status === 1) {
      sdkData.value = data.sdk_info || []
      dialogVersion.value = true
      console.log('详情信息:', response.data.sdk_info);
    } else if (data.sdk_status === 2) {
      // 判断界面是否存在ElMessage， 有则关闭
      if (currentMessage) {
        currentMessage.close();
      }
      currentMessage = ElMessage.info("非SDK项目，无详情信息")
    } else {
      // 判断界面是否存在ElMessage， 有则关闭
      if (currentMessage) {
        currentMessage.close();
      }
      currentMessage = ElMessage.warning("未获取有效数据：" + data.message)
    }
  }).catch(err => {
    console.error("请求失败:", err)
    // 判断界面是否存在ElMessage， 有则关闭
      if (currentMessage) {
        currentMessage.close();
      }
      currentMessage = ElMessage.error("查询失败，请检查控制台")
  })
}




// 删除项目
const deleteDialogVisible = ref(false)
const rowToDelete = ref(null)

const openDeleteDialog = (row) => {
  rowToDelete.value = row
  deleteDialogVisible.value = true
}

const confirmDelete = () => {
  if (rowToDelete.value) {
    loading.value = true;
    handleDelete(rowToDelete.value)
  }
  deleteDialogVisible.value = false
}

const handleDelete = (row: ProjectItem) => {
  http.post('/code_management/delete_project', {
    params:{
        // 删除逻辑
        project_code: row.code,
        project_name: row.description,
        version_rule: row.versionRules,
        project_gitlab: row.gitlab
    }
  }).then(response => {
    console.log('删除项目返回数据:', response.data);

    if (response.data.delete_status === 1) {
      loading.value = false;
      ElMessage.success("删除成功")
      getProjectList()
    } else {  
       ElMessage.error("删除失败")
     }

  })
};

// 自动重试函数封装
async function retryRequest(fn: () => Promise<any>, retries = 3, delay = 1000) {
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      if (attempt < retries) {
        console.warn(`请求失败，重试中（${attempt}/${retries})...`);
        await new Promise(res => setTimeout(res, delay));
      } else {
        throw error;
      }
    }
  }
}

// 获取 GitLab 项目信息
onMounted(() => {
  // 获取组数据
  // retryRequest(() => http.get('/code_management/project_submit', { timeout: 60000 }), 3, 2000)
  //   .then(response => {
  //     const data = response.data;
  //     console.log('获取组数据信息:', data);
  //     if (data.project_status === 1) {
  //       teamdata.value = data.subgroup_paths;
  //     } else {
  //       console.info('获取组数据失败，状态码:', data.project_status);
  //     }
  //   }).catch(error => {
  //     if (error.code === 'ECONNABORTED') {
  //       console.error('请求超时，请检查后端状态');
  //     } else {
  //       console.error('组数据请求失败:', error);
  //     }
  //   });
  //   getProjectList();
    http.get('/softtrack/version-profiles/').then(res => {
      if (res.data && Array.isArray(res.data.data)) {
        versionProfiles.value = res.data.data;
      }
    });
});

// 已移除对旧动态对象的联动校验，转为逐项必填校验
</script>


<style scoped>
.project-management-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding-left: 20px;
  box-sizing: border-box;
}

.project-info-header {
  background-color: #ecf5ff; /* Light blue background */
  border: 1px solid #a0cfff; /* Blue border */
  border-radius: 4px;
  padding: 10px 20px;
  text-align: center;
  margin-bottom: 25px;
  display: inline-block; /* Adjust width to content */
  align-self: flex-start; /* Align to start in a flex container */
  margin-left: 20px; /* Adjust as per image */
}

.header-text {
  color: #409eff; /* Blue text */
  font-weight: bold;
  font-size: 16px;
}

.input-action-row {
/*   padding: 0 20px; 保持原有的内边距 
 display: flex;    使用flex布局 
  justify-content: flex-end; 元素靠右对齐
  margin-bottom: 10px;  可选：增加底部间距，与表格分隔开 */
}



.action-input {
  width: 250px; /* Fixed width for input fields */
}

.instructions {
  margin-top: 30px;
  padding: 0 20px;
  font-size: 14px;
  color: #606266;
  line-height: 1.8;
}

.instructions p {
  margin-bottom: 8px;
}

.highlight {
  font-weight: bold;
  color: #409eff; /* Match button/header blue */
}

.el-button.action-button:hover {
  background-color: #409eff;
  border-color: #409eff;
  color: white;
}

.el-button.action-button{
    background-color: #409eff;
  border-color: #409eff;
  color: white;
}

/* 弹窗样式 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

.el-form :deep(.el-form-item__label) {
  font-weight: 500;
}

.el-dialog :deep(.el-dialog__body) {
  padding: 20px 30px;
  max-height: 80vh;
  overflow-y: auto;
}

.el-dialog :deep(.el-dialog__header) {
  padding: 15px 20px;
  margin-right: 0;
  border-bottom: 1px solid #ebeef5;
}

.el-dialog :deep(.el-dialog__title) {
  font-weight: 600;
  font-size: 18px;
}

.el-dialog :deep(.el-dialog__footer) {
  border-top: 1px solid #ebeef5;
  padding: 15px 20px;
}

.el-dialog :deep(.el-input),
.el-dialog :deep(.el-select),
.el-dialog :deep(.el-date-picker) {
  width: 70%;
}

.el-dialog :deep(.el-textarea) {
  width: 85%;
}

/* 弹窗标题居中 */
.custom-dialog-header {
  text-align: center;
  font-size: 18px;
  font-weight: normal;
  padding-bottom: 30px;
}
.custom-table {
  flex: 1;
  min-height: 0;
}

/* 确保表格无数据时下方显示空白 */
:deep(.el-table__body-wrapper) {
  min-height: calc(100% - 40px); /* 减去表头高度，可根据实际表头高度调整 */
}

/* 自定义分页组件样式，使其位于屏幕底部左侧 */
.custom-pagination {
  margin-top: auto; /* 自动顶到底部 */
  align-self: flex-start; /* 左对齐 */
  padding: 15px 0 0px 0px;
  width: 100%;
}
</style>





