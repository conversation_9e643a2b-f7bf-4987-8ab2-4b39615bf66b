<template>
    <el-divider />
    <div class="add-container">
        <el-form :model="form" label-width="auto" :rules="rules" status-icon ref="formRef">

            <el-form-item label="轮班人员">
                <el-input v-model="form.user_name" readonly></el-input>
            </el-form-item>

            <el-form-item label="状态" prop="is_available">
                <el-select v-model="form.is_available" placeholder="请选择状态">
                    <el-option label="在岗" :value="true"></el-option>
                    <el-option label="不在岗" :value="false"></el-option>
                </el-select>
            </el-form-item>

            <div class="submit-button-container">
                <el-button type="default" @click="onCancel">取消</el-button>
                <el-button type="primary" @click="onSubmit">提交</el-button>
            </div>

        </el-form>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import http from '@/utils/http/http.js';

const props = defineProps({
    r_id: {
        type: Number,
        required: true,
    },
});

const formRef = ref(null);

const form = ref({
    user_name: '',
    is_available: '',
});

const rules = ref({
    is_available: [
        { required: true, message: '请输入状态', trigger: 'blur' },
    ],
});

const emit = defineEmits(['confirm', 'cancel'])

const onSubmit = () => {
    formRef.value.validate(async (valid) => {
        if (valid) {
            let data = {
                ...form.value,
            };

            http.put(`/inspecs/persons/${props.r_id}`, data).then(res => {
                emit('confirm');
            }).catch(err => {
                ElMessageBox.alert(err.response.data.msg, '编辑失败', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'error',
                })
            });
        };
    });
};

const onCancel = () => {
    emit('cancel');
};

onMounted(() => {
    if (props.r_id) {
        http.get(`/inspecs/persons/${props.r_id}`).then(res => {
            let data = res.data.data;

            form.value.user_name = data.user_name;
            form.value.is_available = data.is_available;
        });
    };
});

</script>


<style lang="scss" scoped>
.add-container {
    padding: 0 20px;
}

.submit-button-container {
    display: flex;
    justify-content: flex-end;
}
</style>