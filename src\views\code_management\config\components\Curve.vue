<template>
  <div class="interactive-chart-container">
    <!-- 控制区域 -->
    <div class="chart-controls">
      <h2>温度亮度曲线参数设定</h2>
      <div class="control-group">
        <div class="data-controls">
          <div class="radio-group">
            <label class="radio-item">
              <input 
                type="radio" 
                v-model="chartType" 
                value="line" 
                name="chartType"
                checked
              >
              折线图
            </label>
          </div>
          <div class="radio-group">
              <button @click="addDataPoint" class="add-point-btn">
                <i class="fa fa-plus"></i> 添加数据点
              </button>
              <button @click="removeDataPoint" class="remove-point-btn" >
                <i class="fa fa-minus"></i> 移除数据点
              </button>
          </div>
          
          <div class="right-buttons">
                <button class="reset-btn" @click="resetView">
                    <i class="fa fa-refresh"></i> 重置视图
                  </button>
                <button class="edit-btn" @click="toggleEditMode" :class="{ 'active': editMode }">
                  <i class="fa" :class="editMode ? 'fa-lock' : 'fa-unlock'"></i> 
                  {{ editMode ? '禁用编辑' : '启用编辑' }}
                </button>

                <button class="submit-btn" @click="submitView">
                    <i class="fa fa-check"></i> 提交视图
                </button>
          </div>
        </div>
        </div>
        
        <div class="series-controls" style="display: none">
          <span>数据系列:</span>
          <button 
            v-for="(series, index) in chartData.series || []"
            :key="index"
            @click="toggleSeries(index)"
            :class="{ 'active': series.visible }"
          >
            {{ series.name }}
          </button>
        </div>
      </div>
    </div>
    
    <!-- 图表容器 -->
    <div class="chart-container" ref="chartContainer">
      <canvas 
        ref="chartCanvas"
        @mousemove="handleMouseMove"
        @mousedown="handleMouseDown"
        @mouseup="handleMouseUp"
        @mouseleave="handleMouseLeave"
        @wheel="handleWheel"
      ></canvas>
      
      <div 
        class="tooltip" 
        v-if="tooltipVisible"
        :style="{ 
          left: `${tooltipPosition.x}px`, 
          top: `${tooltipPosition.y}px` 
        }"
      >
        <div class="tooltip-title">温度: {{ tooltipData.x }}°C</div>
        <div v-for="item in tooltipData.values" :key="item.name">
          <span :style="{ backgroundColor: item.color }" class="color-marker"></span>
          {{ item.name }}: {{ item.value }}
        </div>
      </div>
    </div>
    
    <!-- 数据表格 -->
    <div class="data-table-container">
  <h3>数据表格</h3>
  <div class="data-table">
    <div class="table-header">
      <div class="header-cell">序号</div>
      <div class="header-cell">温度 (°C)</div>
      <div class="header-cell">亮度</div>
      
      <div class="header-cell">序号</div>
      <div class="header-cell">温度 (°C)</div>
      <div class="header-cell">亮度</div>
    </div>
    <!-- 循环显示分组数据，每组两个数据点 -->
    <div v-for="(group, groupIndex) in groupedData" :key="groupIndex" class="table-row">
      <!-- 第一个数据点 -->
      <div class="table-cell" style="padding-top: 15px;">{{ group.first.index + 1 }}</div>
      <div class="table-cell">
        <input 
          type="number" 
          v-model.number="chartData.points[0].data[group.first.index]"
          @change="updateXFromTable(group.first.index)"
          step="0.1" 
          min="0"
          max="200"
        >
      </div>
      <div class="table-cell">
        <input 
          type="number" 
          v-model.number="chartData.series[0].data[group.first.index]"
          @change="updateFromTable(group.first.index, 0)"
          min="0"
          max="100"
        >
      </div>
     
      <!-- 第二个数据点（如果存在） -->
      <div v-if="group.second" class="table-cell" style="padding-top: 15px;">{{ group.second.index + 1 }}</div>
      <div v-if="group.second" class="table-cell">
        <input 
          type="number" 
          v-model.number="chartData.points[0].data[group.second.index]"
          @change="updateXFromTable(group.second.index)"
          step="0.1" 
        >
      </div>
      <div v-if="group.second" class="table-cell">
        <input 
          type="number" 
          v-model.number="chartData.series[0].data[group.second.index]"
          @change="updateFromTable(group.second.index, 0)"
          min="0"
          max="100"
        >
      </div>
      
      <!-- 当数据为奇数时，补充空单元格保持表格结构 -->
      <div v-if="!group.second" class="table-cell" colspan="3"></div>
    </div>
  </div>
</div>
</template>

<script setup>
import { ref, onMounted, watch, nextTick, ref as vueRef , computed } from 'vue';
import http from '@/utils/http/http';

// 图表数据
const chartData = ref({
  points: [{
    name: 'NTC温度采样值',
    data: []
  }],
  series: [
    {
      name: '亮度设置值',
      data: [],
      color: '#3B82F6',
      visible: true
    }
  ]
});

let currentMessage = null; // 用于存储当前消息实例

// 排序后的点数据
const sortedPoints = ref([]);
const computedRanges = ref({ xMin: 0, xMax: 0, xRange: 0 });

const props = defineProps({
  config: { type: Object, required: true },
  workspacePath: { type: String, required: true },
  branchStatus: { type: String, required: true },
  project_code: { type: String, required: true },
  project_name: { type: String, required: true },
  project_gitlab: { type: String, required: true },
  project_branch: { type: String, required: true },
  node_level: { type: String, required: true },
  nodeName: { type: String, required: true },
  previousConfig: { type: Object, default: () => ({}) },
  hasEditPermission: { type: Boolean, default: true }
});

// 图表状态
const chartType = ref('line');
const chartCanvas = ref(null);
const chartContainer = ref(null);
const ctx = ref(null);
const canvasWidth = ref(800);
const canvasHeight = ref(400);

// 交互状态
const tooltipVisible = ref(false);
const tooltipData = ref({ x: '', values: [] });
const tooltipPosition = ref({ x: 0, y: 0 });
const isDragging = ref(false);
const dragStart = ref({ x: 0, y: 0, xValue: 0, yValue: 0 });
const viewOffset = ref({ x: 0, y: 0 });
const viewScale = ref(1);
const hoveredIndex = ref(-1);
const hoveredSeries = ref(-1);
const editMode = ref(false);
const editingPoint = ref({ sortedIndex: -1, series: -1, x: 0, y: 0, originalIndex: -1 });
const editingValue = ref(0);
const editInput = vueRef(null);
const dragStartValue = ref(0);
const dragStartXValue = ref(0);
const isNodeDragging = ref(false);

// 初始化原始数据
const initialChartData = ref(null);
let isInitialized = false;

// 数据转换
const adaptToTemplate = (apiData) => {
  const adapted = {
    points: [{ name: 'NTC温度采样值', data: [] }],
    series: [{ name: '亮度设置值', data: [], color: '#3B82F6', visible: true }]
  };
  if (apiData.points?.[0]?.data) {
    adapted.points[0].data = apiData.points[0].data.map(String);
  }
  if (apiData.series?.[0]?.data) {
    adapted.series[0].data = apiData.series[0].data.map(String);
    adapted.series[0].name = apiData.series[0].name || '亮度设置值';
  }
  return adapted;
};

// 获取数据
const fetchChartData = async () => {
  try {
    const response = await http.post('/code_management/liminance_cure', {
      params: { 
        node_level: props.node_level,
        nodeName: props.nodeName, 
        workspace_path: props.workspacePath, 
        branch_status: props.branchStatus,
        project_code: props.project_code,
        project_name: props.project_name,
        project_gitlab: props.project_gitlab,
        project_branch: props.project_branch
      }
    });
    if (response.data.status === 1 && response.data.data) {
      const adaptedData = adaptToTemplate(response.data.data);
      chartData.value.points[0].data = adaptedData.points[0].data;
      chartData.value.series[0].data = adaptedData.series[0].data;
      chartData.value.series[0].name = adaptedData.series[0].name;
    }
    initialChartData.value = JSON.parse(JSON.stringify(chartData.value));
    drawChart();
  } catch (error) {
    console.error('API请求失败:', error);
    // 模拟数据，方便测试
    if (!initialChartData.value) {
      chartData.value = {
        points: [{ 
          name: 'NTC温度采样值', 
          data: ['20.0', '25.0', '30.0', '35.0', '40.0'] 
        }],
        series: [{ 
          name: '亮度设置值', 
          data: ['30', '40', '50', '60', '70'], 
          color: '#3B82F6', 
          visible: true 
        }]
      };
      initialChartData.value = JSON.parse(JSON.stringify(chartData.value));
      drawChart();
    }
  }
};

// 初始化图表
onMounted(() => {
  const initChart = () => {
    if (isInitialized) return;
    const canvas = chartCanvas.value;
    if (!canvas) return;
    
    const container = canvas.parentElement;
    canvasWidth.value = container ? container.clientWidth : 800;
    canvasHeight.value = 400;
    canvas.width = canvasWidth.value;
    canvas.height = canvasHeight.value;
    ctx.value = canvas.getContext('2d');
    
    if (!window.resizeHandlerBound) {
      window.addEventListener('resize', handleResize);
      window.resizeHandlerBound = true;
    }
    
    drawChart();
    fetchChartData().finally(() => {
      isInitialized = true;
    });
  };
  nextTick(initChart);
});

// 处理窗口大小变化
const handleResize = () => {
  clearTimeout(window.resizeTimeout);
  window.resizeTimeout = setTimeout(() => {
    const container = chartCanvas.value?.parentElement;
    if (container) {
      canvasWidth.value = container.clientWidth;
      chartCanvas.value.width = canvasWidth.value;
      drawChart();
    }
  }, 100);
};

// 计算坐标范围参数
const getCoordinateRanges = () => {
  if (chartData.value.points[0].data.length === 0) {
    return { xMin: 0, xMax: 100, xRange: 100 };
  }
  
  const tempValues = chartData.value.points[0].data.map(Number);
  const xMinRaw = Math.min(...tempValues);
  const xMaxRaw = Math.max(...tempValues);
  const xRangeRaw = xMaxRaw - xMinRaw || 1;
  
  // 统一添加边距
  const xMin = xMinRaw - 1;
  const xMax = xMaxRaw + 1;
  const xRange = xMax - xMin;
  
  computedRanges.value = { xMin, xMax, xRange };
  return computedRanges.value;
};

// 获取排序后的点并预计算屏幕坐标
const getSortedPoints = () => {
  const tempValues = chartData.value.points[0].data.map(Number);
  const brightnessValues = chartData.value.series[0].data.map(Number);
  const { xMin, xMax, xRange } = getCoordinateRanges();
  const padding = 60;
  
  const innerWidth = canvasWidth.value - padding * 2;
  const innerHeight = canvasHeight.value - padding * 2;
  
  return tempValues
    .map((value, index) => ({ 
      xValue: value, 
      brightness: brightnessValues[index], 
      index,
      screenX: padding + ((value - xMin) / xRange) * innerWidth,
      screenY: padding + innerHeight - (brightnessValues[index] / 100) * innerHeight
    }))
    .sort((a, b) => a.xValue - b.xValue);
};

// 绘制图表主函数
const drawChart = () => {
  if (!ctx.value) return;
  
  ctx.value.clearRect(0, 0, canvasWidth.value, canvasHeight.value);
  sortedPoints.value = getSortedPoints();
  drawGrid();
  drawAxes();
  drawLineChart();
  drawLegend();
};

// 绘制网格
const drawGrid = () => {
  const padding = 60;
  const innerWidth = canvasWidth.value - padding * 2;
  const innerHeight = canvasHeight.value - padding * 2;
  
  if (chartData.value.points[0].data.length === 0) return;
  
  const { xMin, xMax, xRange } = computedRanges.value;
  const tempValues = chartData.value.points[0].data.map(Number);
  const actualXMin = Math.min(...tempValues);
  const actualXMax = Math.max(...tempValues);
  
  ctx.value.strokeStyle = '#e5e7eb';
  ctx.value.lineWidth = 1;
  
  // 水平网格线（Y轴）
  const ySteps = 10;
  for (let i = 0; i <= ySteps; i++) {
    const y = padding + innerHeight - (innerHeight / ySteps) * i;
    ctx.value.beginPath();
    ctx.value.moveTo(padding, y);
    ctx.value.lineTo(canvasWidth.value - padding, y);
    ctx.value.stroke();
    
    // Y轴刻度
    const value = 0 + (i * 10);
    ctx.value.fillStyle = '#6b7280';
    ctx.value.font = '12px sans-serif';
    ctx.value.textAlign = 'right';
    ctx.value.fillText(value, padding - 10, y + 4);
  }
  
  // 垂直网格线（X轴）
  const xSteps = 5;
  for (let i = 0; i <= xSteps; i++) {
    const xValue = actualXMin + ((actualXMax - actualXMin) / xSteps) * i;
    const x = padding + ((xValue - xMin) / xRange) * innerWidth;
    ctx.value.beginPath();
    ctx.value.moveTo(x, padding);
    ctx.value.lineTo(x, canvasHeight.value - padding);
    ctx.value.stroke();
    
    // X轴刻度
    ctx.value.fillStyle = '#6b7280';
    ctx.value.font = '12px sans-serif';
    ctx.value.textAlign = 'center';
    ctx.value.fillText(xValue.toFixed(1), x, canvasHeight.value - padding + 20);
  }
};

// 绘制坐标轴
const drawAxes = () => {
  const padding = 60;
  
  ctx.value.strokeStyle = '#374151';
  ctx.value.lineWidth = 2;
  
  // X轴
  ctx.value.beginPath();
  ctx.value.moveTo(padding, canvasHeight.value - padding);
  ctx.value.lineTo(canvasWidth.value - padding, canvasHeight.value - padding);
  ctx.value.stroke();
  
  // Y轴
  ctx.value.beginPath();
  ctx.value.moveTo(padding, padding);
  ctx.value.lineTo(padding, canvasHeight.value - padding);
  ctx.value.stroke();
  
  // 轴标签
  ctx.value.fillStyle = '#1f2937';
  ctx.value.font = '14px sans-serif';
  ctx.value.textAlign = 'center';
  ctx.value.fillText('温度 (°C)', canvasWidth.value / 2, canvasHeight.value - 10);
  
  ctx.value.save();
  ctx.value.translate(15, canvasHeight.value / 2);
  ctx.value.rotate(-Math.PI / 2);
  ctx.value.fillText('亮度(%)', 0, 0);
  ctx.value.restore();
};

// 绘制折线图
const drawLineChart = () => {
  const padding = 60;
  const innerWidth = canvasWidth.value - padding * 2;
  const innerHeight = canvasHeight.value - padding * 2;
  
  if (chartData.value.points[0].data.length === 0 || chartData.value.series[0].data.length === 0) return;
  
  const { xMin, xMax, xRange } = computedRanges.value;
  const points = sortedPoints.value;
  
  chartData.value.series.forEach(series => {
    if (!series.visible) return;
    const seriesIdx = chartData.value.series.indexOf(series);
    
    // 绘制线条
    ctx.value.beginPath();
    ctx.value.strokeStyle = series.color;
    ctx.value.lineWidth = 3;
    ctx.value.lineJoin = 'round';
    ctx.value.lineCap = 'round';
    
    points.forEach((point, index) => {
      if (index === 0) {
        ctx.value.moveTo(point.screenX, point.screenY);
      } else {
        ctx.value.lineTo(point.screenX, point.screenY);
      }
    });
    ctx.value.stroke();
    
    // 绘制数据点
    points.forEach((point, sortedIndex) => {
      const isHovered = hoveredIndex.value === sortedIndex && hoveredSeries.value === seriesIdx;
      const isEditing = editingPoint.value.sortedIndex === sortedIndex && editingPoint.value.series === seriesIdx;
      
      // 编辑模式下突出显示
      if (editMode.value) {
        ctx.value.strokeStyle = series.color;
        ctx.value.lineWidth = 2;
        ctx.value.beginPath();
        ctx.value.arc(point.screenX, point.screenY, 10, 0, Math.PI * 2);
        ctx.value.stroke();
      }
      
      // 绘制点
      ctx.value.fillStyle = isHovered || isEditing ? '#ffffff' : series.color;
      ctx.value.beginPath();
      ctx.value.arc(point.screenX, point.screenY, isHovered || isEditing ? 6 : 4, 0, Math.PI * 2);
      ctx.value.fill();
      
      // 点的边框
      ctx.value.strokeStyle = isHovered || isEditing ? series.color : '#ffffff';
      ctx.value.lineWidth = 2;
      ctx.value.beginPath();
      ctx.value.arc(point.screenX, point.screenY, isHovered || isEditing ? 8 : 4, 0, Math.PI * 2);
      ctx.value.stroke();
      
      // 显示数值
      if (isHovered || isEditing) {
        ctx.value.fillStyle = '#374151';
        ctx.value.font = '12px sans-serif';
        ctx.value.textAlign = 'center';
        ctx.value.fillText(series.data[point.index], point.screenX, point.screenY - 15);
      }
    });
  });
};

// 绘制图例
const drawLegend = () => {
  const padding = 20;
  const itemHeight = 20;
  const startY = padding;
  
  chartData.value.series.forEach((series, index) => {
    if (!series.visible) return;
    
    const y = startY + index * itemHeight;
    ctx.value.fillStyle = series.color;
    ctx.value.fillRect(padding, y, 12, 12);
    ctx.value.fillStyle = '#374151';
    ctx.value.font = '12px sans-serif';
    ctx.value.textAlign = 'start';
    ctx.value.fillText(series.name, padding + 20, y + 10);
  });
};

// 处理鼠标移动
const handleMouseMove = (e) => {
  if (!chartCanvas.value) return;
  
  const rect = chartCanvas.value.getBoundingClientRect();
  const mouseX = e.clientX - rect.left;
  const mouseY = e.clientY - rect.top;
  
  // 拖拽处理
  if (isDragging.value && isNodeDragging.value && editMode.value) {
    const padding = 60;
    const innerWidth = canvasWidth.value - padding * 2;
    const innerHeight = canvasHeight.value - padding * 2;
    const { xMin, xMax, xRange } = computedRanges.value;
    
    if (chartData.value.points[0].data.length === 0) return;
    if (editingPoint.value.originalIndex === -1) return;
    
    // 计算X值变化（温度）
    const xRatio = Math.max(0, Math.min(1, (mouseX - padding) / innerWidth));
    const newValueX = xMin + xRatio * xRange;
    
    // 计算Y值变化（亮度）
    const yRatio = Math.max(0, Math.min(1, 1 - (mouseY - padding) / innerHeight));
    const newValueY = Math.round(yRatio * 100);
    
    // 更新数据
    chartData.value.points[0].data[editingPoint.value.originalIndex] = newValueX.toFixed(1);
    chartData.value.series[0].data[editingPoint.value.originalIndex] = newValueY.toString();
    
    editingPoint.value.x = e.clientX;
    editingPoint.value.y = e.clientY;
    editingValue.value = newValueY;
    
    // 强制重绘
    drawChart();
    return;
  }
  
  // 非拖拽状态 - 检测悬停
  if (!isDragging.value) {
    const padding = 60;
    if (mouseX > padding && mouseX < canvasWidth.value - padding && mouseY > padding && mouseY < canvasHeight.value - padding) {
      let closestPoint = null;
      let minDistance = Math.min(30, Math.max(10, canvasWidth.value * 0.02));
      
      chartData.value.series.forEach((series, seriesIdx) => {
        if (!series.visible) return;
        
        sortedPoints.value.forEach((point, sortedIndex) => {
          const dx = mouseX - point.screenX;
          const dy = mouseY - point.screenY;
          const distance = Math.sqrt(dx * dx + dy * dy);
          
          if (distance < minDistance) {
            minDistance = distance;
            closestPoint = { 
              sortedIndex, 
              series: seriesIdx, 
              originalIndex: point.index 
            };
          }
        });
      });
      
      if (closestPoint) {
        // 更新悬停状态
        hoveredIndex.value = closestPoint.sortedIndex;
        hoveredSeries.value = closestPoint.series;
        
        // 显示节点tooltip
        const point = sortedPoints.value[closestPoint.sortedIndex];
        const containerRect = chartContainer.value.getBoundingClientRect();
        showTooltip(
          e.clientX - containerRect.left + 10, 
          e.clientY - containerRect.top - 10, 
          point.xValue.toFixed(1)
        );
      } else {
        // 显示X轴提示
        const { xMin, xMax, xRange } = computedRanges.value;
        const xValue = xMin + ((mouseX - padding) / innerWidth) * xRange;
        const containerRect = chartContainer.value.getBoundingClientRect();
        showTooltip(
          e.clientX - containerRect.left + 10, 
          e.clientY - containerRect.top - 10, 
          xValue.toFixed(1)
        );
      }
    } else {
      // 鼠标在画布外
      hoveredIndex.value = -1;
      hoveredSeries.value = -1;
      tooltipVisible.value = false;
    }
    drawChart();
  }
};

// 显示 tooltip
const showTooltip = (x, y, xValue) => {
  const values = chartData.value.series
    .filter(s => s.visible)
    .map(series => {
      const closestIndex = chartData.value.points[0].data
        .map((temp, i) => ({ 
          i, 
          diff: Math.abs(Number(temp) - parseFloat(xValue)) 
        }))
        .sort((a, b) => a.diff - b.diff)[0].i;
      
      return {
        name: series.name,
        value: series.data[closestIndex],
        color: series.color
      };
    });
  
  tooltipData.value = { x: xValue, values };
  tooltipPosition.value = { x, y };
  tooltipVisible.value = true;
};

// 处理鼠标按下
const handleMouseDown = (e) => {
  if (e.button !== 0) return; // 只处理左键
  if (!chartCanvas.value) return;
  
  const rect = chartCanvas.value.getBoundingClientRect();
  const x = e.clientX - rect.left;
  const y = e.clientY - rect.top;
  
  isDragging.value = false;
  isNodeDragging.value = false;
  
  // 编辑模式下拖拽点
  if (editMode.value && hoveredIndex.value !== -1 && hoveredSeries.value !== -1) {
    const selectedPoint = sortedPoints.value[hoveredIndex.value];
    if (selectedPoint) {
      editingPoint.value = {
        sortedIndex: hoveredIndex.value,
        series: hoveredSeries.value,
        x: e.clientX,
        y: e.clientY,
        originalIndex: selectedPoint.index
      };
      editingValue.value = Number(chartData.value.series[hoveredSeries.value].data[selectedPoint.index]);
      dragStartXValue.value = Number(chartData.value.points[0].data[selectedPoint.index]);
      dragStart.value = { x, y };
      
      isDragging.value = true;
      isNodeDragging.value = true;
      chartCanvas.value.style.cursor = 'move';
      return;
    }
  }
  
  // 拖拽视图
  dragStart.value = { x, y };
  isDragging.value = true;
  chartCanvas.value.style.cursor = 'grabbing';
};

// 处理鼠标释放
const handleMouseUp = () => {
  if (isDragging.value) {
    isDragging.value = false;
    isNodeDragging.value = false;
    chartCanvas.value.style.cursor = 'default';
  }
};

// 处理鼠标离开
const handleMouseLeave = () => {
  isDragging.value = false;
  isNodeDragging.value = false;
  chartCanvas.value.style.cursor = 'default';
  
  if (hoveredIndex.value !== -1) {
    hoveredIndex.value = -1;
    hoveredSeries.value = -1;
    tooltipVisible.value = false;
    drawChart();
  }
};

// 处理鼠标滚轮缩放
const handleWheel = (e) => {
  e.preventDefault();
  if (editingPoint.value.sortedIndex !== -1) return;
  
  const rect = chartCanvas.value.getBoundingClientRect();
  const mouseX = e.clientX - rect.left;
  
  const scaleFactor = e.deltaY < 0 ? 1.1 : 0.9;
  const beforeScaleX = (mouseX - 60 - viewOffset.value.x) / viewScale.value;
  viewScale.value *= scaleFactor;
  viewOffset.value.x = mouseX - 60 - beforeScaleX * viewScale.value;
  viewScale.value = Math.max(0.5, Math.min(3, viewScale.value));
  
  drawChart();
};

// 其他函数
const toggleSeries = (index) => {
  chartData.value.series[index].visible = !chartData.value.series[index].visible;
  drawChart();
};

const resetView = () => {
  if (initialChartData.value) {
    chartData.value = JSON.parse(JSON.stringify(initialChartData.value));
  }
  viewOffset.value = { x: 0, y: 0 };
  viewScale.value = 1;
  drawChart();
};

const toggleEditMode = () => {
  editMode.value = !editMode.value;
  editingPoint.value = { sortedIndex: -1, series: -1, x: 0, y: 0, originalIndex: -1 };
  drawChart();
};

const submitView = () => {
  const submitData = {
    points: chartData.value.points[0].data.map(Number),
    series: chartData.value.series[0].data.map(Number)
  };
  console.log('提交的数据:', submitData);
  // 提交逻辑可以在这里实现
  http.post('/code_management/submit_curve', {
    params: {
      value: submitData,
      workspace_path: props.workspacePath,
      branch_status: props.branchStatus,
      project_code: props.project_code,
      project_name: props.project_name,
      project_gitlab: props.project_gitlab,
      project_branch: props.project_branch,
      node_level: props.node_level,
      nodeName: props.nodeName

    }
  }).then(response => {
        console.log("配置更改详情：", response.data);
        if (response.data.status === 1) {
            // 关闭之前的消息并显示新消息
            if (currentMessage) {
              currentMessage.close();
            }
            currentMessage = ElMessage.success('配置更新成功');
        } else {
            if (currentMessage) {
                currentMessage.close();
              }
            currentMessage = ElMessage.error('配置更新失败，正在恢复上一次值');
            resetView()
          }
    }).catch(error => {
    // 🎯 关闭加载对话框
    isConfigChangeRequesting.value = false;
    currentRequestInfo.value = null;

    // 处理请求错误
    if (currentMessage) {
      currentMessage.close();
    }
    currentMessage = ElMessage.error('请求失败，请稍后再试');
    console.error('请求错误:', error);
  })
    
};

const finishEditing = () => {
  if (editingPoint.value.sortedIndex !== -1) {
    const newValue = Math.max(0, Math.min(100, Math.round(editingValue.value)));
    chartData.value.series[editingPoint.value.series].data[editingPoint.value.originalIndex] = newValue.toString();
    editingPoint.value = { sortedIndex: -1, series: -1, x: 0, y: 0, originalIndex: -1 };
    drawChart();
  }
};

const addDataPoint = () => {
  if (chartData.value.points[0].data.length === 0) return;
  
  const lastTemp = Number(chartData.value.points[0].data.at(-1));
  chartData.value.points[0].data.push((lastTemp + 1).toString());
  
  const lastBrightness = chartData.value.series[0].data.at(-1);
  chartData.value.series[0].data.push(lastBrightness);
  
  drawChart();
};

const removeDataPoint = () => {
  if (chartData.value.points[0].data.length <= 3) return;
  
  chartData.value.points[0].data.pop();
  chartData.value.series[0].data.pop();
  
  drawChart();
};

const updateXFromTable = (index) => {
  const value = Number(chartData.value.points[0].data[index]);
  chartData.value.points[0].data[index] = isNaN(value) ? '0' : value.toFixed(1);
  drawChart();
};

const updateFromTable = (index, seriesIndex) => {
  const value = Math.max(0, Math.min(100, Math.round(chartData.value.series[seriesIndex].data[index])));
  chartData.value.series[seriesIndex].data[index] = value.toString();
  drawChart();
};

// 在script setup中添加
const groupedData = computed(() => {
  const groups = [];
  const dataLength = chartData.value.points[0].data.length;
  
  // 每两个数据点分为一组
  for (let i = 0; i < dataLength; i += 2) {
    const group = {
      first: { index: i }
    };
    
    // 检查是否有第二个数据点
    if (i + 1 < dataLength) {
      group.second = { index: i + 1 };
    }
    
    groups.push(group);
  }
  
  return groups;
});


// 监听数据变化重绘图表
watch(() => chartData.value.series[0].data, drawChart, { deep: true });
watch(() => chartData.value.points[0].data, drawChart, { deep: true });
watch(chartType, drawChart);
</script>

<style scoped>
.interactive-chart-container {
  font-family: 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  padding: 20px;
  max-width: 100%;
  width: 100%;
}

/* 控制栏样式：标题单独一行 */
.chart-controls {
  display: flex;
  flex-direction: column; /* 垂直排列 */
  background: #f9fafb;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  padding: 15px 20px;
  margin-bottom: 15px;
}

.chart-controls h2 {
  color: #555;
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0 0 15px 0; /* 底部留出空间 */
  white-space: nowrap;
}

/* 控制组容器 */
.control-group {
  display: flex;
  width: 100%; /* 占满整行 */
}

/* 数据控制区：让内容分布更合理 */
.data-controls {
  display: flex;
  align-items: center;
  width: 100%; /* 占满控制组宽度 */
}

/* 单选框和增删按钮区域 */
.data-controls > div:first-child,
.data-controls > div:nth-child(2) {
  flex: 0 0 auto; /* 不自动伸缩 */
}

/* 右对齐按钮组：通过margin-left: auto实现右对齐 */
.right-buttons {
  display: flex;
  gap: 10px;
  margin-left: auto; /* 关键：推到右侧 */
}

/* 单选框样式 */
.radio-group {
  display: flex;
  gap: 10px;
  padding: 5px 0;
  margin-right: 15px; /* 与增删按钮保持距离 */
}

.radio-item {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #4b5563;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.radio-item:hover {
  background-color: #f0f0f0;
}

/* 按钮样式 */
.add-point-btn, .remove-point-btn, 
.reset-btn, .edit-btn, .submit-btn {
  padding: 6px 17px;
  border-radius: 6px;
  border: none;
  color: #fff;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.3s ease;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  height: 32px;
  min-width: 100px;
}

.add-point-btn { background: #4ade80; }
.add-point-btn:hover { background: #22c55e; }
.remove-point-btn { background: #f87171; }
.remove-point-btn:hover { background: #ef4444; }
.reset-btn { background: #fb923c; }
.reset-btn:hover { background: #f97316; }
.edit-btn { background: #60a5fa; }
.edit-btn:hover, .edit-btn.active { background: #3b82f6; }
.submit-btn { background: #a78bfa; }
.submit-btn:hover { background: #8b5cf6; }

button:hover {
  transform: translateY(-2px);
}
button:active {
  transform: translateY(0);
}

/* 图表容器 */
.chart-container {
  position: relative;
  width: 100%;
  height: 400px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
  margin-top: 10px;
  background-color: white;
}

canvas {
  width: 100%;
  height: 100%;
  cursor: default;
}

canvas:hover {
  cursor: crosshair;
}

/* Tooltip 样式 */
.tooltip {
  position: absolute;
  background: rgba(255, 255, 255, 0.98);
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 10px 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 10;
  pointer-events: none;
  min-width: 150px;
}

.tooltip-title {
  font-weight: 600;
  margin-bottom: 5px;
  color: #1f2937;
  border-bottom: 1px solid #f3f4f6;
  padding-bottom: 5px;
}

.color-marker {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 5px;
}

/* 数据表格样式 */
.data-table-container {
  margin-top: 20px;
}

.data-table-container h3 {
  color: #666;
  margin-bottom: 10px;
  font-size: 1.1rem;
}

.data-table {
  display: grid; /* 核心修复：启用Grid布局 */
  grid-template-columns: 120px 1fr 1fr 120px 1fr 1fr; /* 增加50px间隔列 */
  gap: 1px;
  background-color: #e5e7eb;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
  width: 100%; /* 确保表格占满容器 */
  min-width: 600px; /* 防止过窄导致布局错乱 */
}

.table-header {
  grid-column: 1 / -1;
  display: contents;
}

.header-cell {
  background-color: #f3f4f6;
  padding: 8px 10px;
  font-weight: 600;
  color: #1f2937;
  text-align: center;
}

.table-row {
  display: contents;
  text-align: center;
}

.table-row:nth-child(even) .table-cell {
  background-color: #f9fafb;
}

.table-cell {
  background-color: white;
  padding: 8px 10px;
  transition: background-color 0.2s;
}



.table-cell:hover {
  background-color: #f3f4f6;
}

.table-cell input {
  width: 100%;
  padding: 6px 8px;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  text-align: center;
  height: 30px;
}

.table-cell input:focus {
  outline: none;
  border-color: #60a5fa;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

/* 小屏适配 */
@media (max-width: 768px) {
    .data-table {
    grid-template-columns: 60px 1fr 1fr 60px 1fr 1fr; /* 小屏也保持间隔 */
  }
  .data-controls {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .right-buttons {
    margin-left: 0;
    width: 100%;
    justify-content: flex-end;
  }
  
  .radio-group {
    margin-right: 0;
  }
}
</style>
