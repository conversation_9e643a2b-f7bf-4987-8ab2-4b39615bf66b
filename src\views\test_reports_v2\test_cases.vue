<template>
    <!-- 增加筛选框 -->
    <div class="tool-bar-container">
        <div style="margin-left: auto; display: flex; gap: 10px;">
            <el-tooltip class="box-item" effect="dark" content="重置已添加的筛选条件" placement="top-start">
                <el-button text bg @click="handleReset">重置</el-button>
            </el-tooltip>
            <filterButton @click="onFilterStatusChange" :count="filterCount" />
            <el-button icon="Refresh" text bg @click="handleRefresh">刷新</el-button>
        </div>
    </div>
    <div class="filter-container" v-if="showFilterContainer">
        <el-select v-model="form.module" placeholder="请选择功能模块" @change="onFilter"
            style="width: 200px;" clearable>
            <el-option v-for="m in modules" :label="m.label" :value="m.value"></el-option>
        </el-select>
        <el-select v-model="form.result" placeholder="请选择测试结果" @change="onFilter"

            style="width: 200px; margin-left: 20px" clearable>

            <el-option label="PASS" :value="1"></el-option>
            <el-option label="NG" :value="0"></el-option>
            <el-option label="NA" :value="2"></el-option>
            <el-option label="NT" :value="3"></el-option>
        </el-select>
    </div>

    <el-table ref="tableRef" :data="testCaseData" stripe border
        style="width: 100%; height: calc(100vh - 350px);padding: 10px 0;" row-key="id">
        <el-table-column label="序号" width="80" align="center" fixed="left">
            <template #default="{ row, $index }">
                {{ $index + 1 }}
            </template>
        </el-table-column>
        <el-table-column label="需求ID" width="200" align="center" fixed="left">
            <template #default="{ row }">
                <span>{{ parseRequirements(row) }}</span>
            </template>
        </el-table-column>
        <el-table-column prop="number" label="用例ID" width="200" align="center" fixed="left"></el-table-column>
        <el-table-column prop="name" label="用例名称" width="200" align="center" fixed="left"></el-table-column>
        <el-table-column prop="source" label="用例来源" width="150" align="center">
            <template #default="{ row }">
                <span>{{ sourceMap[row.source] || row.source }}</span>
            </template>
        </el-table-column>
        <el-table-column prop="type" label="用例类型" width="150" align="center">
            <template #default="{ row }">
                <span>{{ typeMap[row.type] || row.type }}</span>
            </template>
        </el-table-column>
        <el-table-column label="用例生成方法" width="150" align="center">
            <template #default="{ row }">
                <span>{{ generationMethodMap[row.generation_method] || row.generation_method }}</span>
            </template>
        </el-table-column>
        <el-table-column label="测试方法" width="150" align="center">
            <template #default="{ row }">
                <el-tag type="primary" v-for="i in row.test_method">{{ testMethodMap[i] || i }}</el-tag>
            </template>
        </el-table-column>
        <el-table-column label="用例版本" width="100" align="center">
            <template #default="{ row }">
                V{{ row.version }}.0
            </template>
        </el-table-column>
        <el-table-column label="优先级" width="100" align="center">
            <template #default="{ row }">
                <span>{{ priorityMap[row.priority] || row.priority }}</span>
            </template>
        </el-table-column>
        <el-table-column label="执行方式" width="120" align="center">
            <template #default="{ row }">
                <el-tag v-if="row.execute_mode == 'AUTOMATED_EXECUTION'" type="success">自动化测试</el-tag>
                <el-tag v-else-if="row.execute_mode == 'MANUAL_EXECUTION'" type="success">手动测试</el-tag>
                <el-tag v-else-if="row.execute_mode == 'SEMI_AUTOMATED_EXECUTION'" type="success">半自动化测试</el-tag>
                <el-tag v-else type="danger">未知</el-tag>
            </template>
        </el-table-column>
        <el-table-column label="前提条件" width="300" align="center">
            <template #default="{ row }">
                <el-scrollbar height="150px">
                    <div style="white-space: pre-wrap;word-break: break-word;text-align: left;padding: 5px;">
                        <span>{{ row.preconditions }}</span>
                    </div>
                </el-scrollbar>
            </template>
        </el-table-column>
        <el-table-column label="操作步骤" width="300" align="center">
            <template #default="{ row }">
                <el-scrollbar height="150px">
                    <div
                        style="display: flex;flex-direction: column; justify-content: start; text-align: left;padding: 5px;">
                        <span v-for="(step, index) in row.test_steps">{{ index + 1 }}. {{ step.desc }}</span>
                    </div>
                </el-scrollbar>
            </template>
        </el-table-column>
        <el-table-column label="预期结果" width="300" align="center">
            <template #default="{ row }">
                <el-scrollbar height="150px">
                    <div style="display: flex;flex-direction: column; justify-content: start; text-align: left;">
                        <span v-for="(step, index) in row.test_steps">{{ index + 1 }}. {{ step.expectation }}</span>
                    </div>
                </el-scrollbar>
            </template>
        </el-table-column>
        <el-table-column label="测试值" width="180" align="left">
            <template #default="{ row }">
                <el-scrollbar height="150px">
                    <div style="display: flex;flex-direction: column; justify-content: start; text-align: left;">
                <span style="white-space: pre-line;">
                 {{ typeof row.result?.value === 'string' ? row.result.value.replace(/\\n/g, '\n') : row.result?.value || "" }}
                </span>
            </div>
        </el-scrollbar>
            </template>
        </el-table-column>

        <el-table-column label="测试结果" width="100" align="center">
            <template #default="{ row }">
                <el-tag v-if="row.result?.result_two === 1" type="success">PASS</el-tag>
                <el-tag v-else-if="row.result?.result_two === 0" type="danger">NG</el-tag>
                <el-tag v-else-if="row.result?.result_two === 2" type="warning">NA</el-tag>
                <el-tag v-else-if="row.result?.result_two === 3" type="info">NT</el-tag>
            </template>
        </el-table-column>
    </el-table>

</template>

<script setup>
import { ref, computed, defineProps,reactive, watch, onMounted } from 'vue';
import filterButton from '@/components/filterButton.vue';
import http from '@/utils/http/http.js';


const props = defineProps({
    testCases: {
        type: Array,
        default: () => []
    }
})

const modules = ref([]);
const moduleMap = ref({});

const testCaseData = ref([]);

let sourceMap = ref({
    "TASK_CHANGE": "用例库沿用",
    "TASK_AFFIRM": "需求分析",
    "NEW_PROJECT": "需求变更",
    "HORIZONTAL_SCALING": "横向扩展"
});

let typeMap = ref({
    "DURABLE_TEST": "耐久测试",
    "PERFORMANCE_TEST": "性能测试",
    "FUNCTION_TEST": "功能测试",
    "PROTOCOL_STACK_TEST": "协议栈测试"
});
let priorityMap = ref({
    "HIGH": "高",
    "MIDDLE": "中",
    "LOW": "低",
});
let generationMethodMap = ref({
    "BOUNDARY_VALUE_METHOD": "边界值法",
    "FRUIT_GRAPH_METHOD": "因果法",
    "DECISION_TABLE_DRIVE": "判定表驱法",
    "FUNCTION_DIAGRAM_METHOD": "功能图法",
    "SCENE_METHOD": "场景法",
    "EQUIVALENCE_CLASS": "等价类",
    "FIELD_EXPERIENCE_ANALYSIS": "现场经验分析",
    "EXTERNAL_AND_INTERNAL_INTERFACE_ANALYSIS": "外部和内部接口分析法",
    "PROCESS_ANALYSIS": "流程分析法",
    "BACKWARD_ANALYSIS": "反向分析",
    "FORWARD_ANALYSIS": "正向分析",
    "ENVIRONMENTAL_CONDITIONS_AND_OPERATIONAL_USE_CASE_ANALYSIS": "环境条件和操作用例分析",
    "MISGUESS": "错误猜错法",
    "SEQUENCE_AND_SOURCE_ANALYSIS": "序列和来源的分析",
    "COMMON_LIMIT_CONDITIONS_4_DEPENDENCE": "相依性的常见极限条件",
    "ANALYSIS_OPERATING_CONDITIONS_USE_CASES": "用例的运行条件分析",
    "DEMAND_ANALYSIS": "基于需求分析",
});
let testMethodMap = ref({
    "PRESSURE_TEST": "压力测试",
    "RESOURCE_USAGE_TESTING": "资源使用情况测试",
    "INTERACTION_COMMUNICATION_TESTING": "互动/沟通测试",
    "INTERFACE_CONSISTENCY_CHECK": "接口一致性检查",
    "TESTS_BASED_ON_FIELD_EXPERIENCE": "根据现场经验进行的测试",
    "FALSE_GUESS_TEST": "错误猜测测试",
    "PERFORMANCE_TEST": "性能测试",
    "FAULT_INJECTION_TEST": "故障注入测试",
    "BACK_TO_BACK_TESTING": "背靠背测试",
    "INTERFACE_TEST": "基于接口测试",
    "DEMAND_TEST": "基于需求测试",
});

let form = reactive({
    name: '',
    number: '',
});

let total = ref(0);
const filterCount = ref(0);
let showFilterContainer = ref(false);

function onFilterStatusChange() {
    showFilterContainer.value = !showFilterContainer.value;
};

function update_table() {
    
    testCaseData.value = props.testCases;
    total.value = props.testCases.length;

    if (form.result !== null && form.result !== undefined && form.result !== '') {
        testCaseData.value = testCaseData.value.filter(item => item.result?.result_two === form.result);
    }

    if (form.module) {
        testCaseData.value = testCaseData.value.filter(item => item.module == form.module);
    }
};

function parseRequirements(item) {
    let reqs = item.requirements || [];
    reqs = reqs.map(req => req.requirement_number);
    reqs = reqs.join(", ")
    return reqs;
    
};

function handleReset() {
    form = reactive({
        page: 1,
        pagesize: 10,
        project_number: form.project_number,
    });
    update_table();
};



function onFilter() {
    form.page = 1;
    form.pagesize = 10;
    update_table();
};

function handleRefresh() {
    update_table();
};

watch([() => props.testCases, moduleMap], () => {
    update_table();

    var mCode = new Set();
    props.testCases.forEach(item => {
        mCode.add(item.module);
    });
    modules.value = Array.from(mCode).map(item => {
        return {
            label: moduleMap.value[item],
            value: item
        }
    });
});

onMounted(() => {
    http.get('/functions').then(res => {
        res.data.data.results.forEach(item => {
            moduleMap.value[item.number] = item.name;    
        });
    });
});

</script>

<style lang="scss" scoped>
.search-container {
    width: 100%;
    display: flex;
    justify-content: center;
    justify-items: center;
    height: 40px;
}

.search-input {
    width: 50%;
    max-width: 500px;
}

.search-button {
    border: 1px solid #dcdfe6;
}

.filter-container {
    width: 100%;
    display: flex;
    justify-content: flex-start;

    margin-bottom: 10px;

    .el-input {
        width: 50%;
        max-width: 300px;
        margin-right: 10px;
    }
}

.tool-bar-container {
    display: flex;
    justify-content: flex-start;
}
</style>