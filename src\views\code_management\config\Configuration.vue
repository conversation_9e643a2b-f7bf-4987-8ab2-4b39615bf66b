<template>
  <div class="dynamic-form">
    <!-- 版本号显示区域 -->

    <!-- 1. 背光IC选择（保持原有逻辑） -->
    <template v-for="(item, idx) in (config?.userDefined || [])" :key="idx">
      <div class="form-item" :class="{ 'first-item': idx === 0 }" v-if="item.list">
        <label class="form-label user-defined-label">
          {{ item.name }}
          <el-tooltip
            :content="Array.isArray(item.desc) ? item.desc.join(', ') : (item.desc || '暂无描述')"
            placement="top"
            effect="dark"
          >
            <span class="help-icon">?</span>
          </el-tooltip>
        </label>
        <el-select
          v-model="formData[item.name]"
          placeholder="请选择"
          class="form-select input-control"
          @change="(val) => handleChange(item.name, val)"
        >
          <el-option
            v-for="(opt, optKey) in item.list"
            :key="optKey"
            :label="opt.name"
            :value="opt.name"
          />
        </el-select>
      </div>

      <!-- 用户自定义项分割线 -->
      <div
        v-if="idx < (config?.userDefined?.length || 0) - 1 && idx > 0"
        class="user-defined-divider"
      ></div>
    </template>

    <!-- 用户自定义区域和变量分组之间的分割线 -->
    <div
      v-if="config?.userDefined && config.userDefined.length > 0"
      class="section-divider"
    ></div>

    <!-- 2. 变量分组（模拟左侧菜单层级，优化箭头显示） -->
    <div class="variables-container">
      <template v-for="(group, groupIdx) in (config?.variables || [])" :key="groupIdx">
        <!-- 分组标题（优化箭头样式，更明显） -->
        <div 
            class="group-title" 
            @click="toggleGroup(groupIdx)"
          >
           <!-- 优化后的箭头图标，使用Element Plus图标并确保类名正确 -->
            <i 
              :class="[
                'el-icon-caret-right', 
                'group-arrow', 
                { 'is-open': activeGroup.includes(groupIdx) }
              ]"
              style="margin-right: 8px;"
            />
            {{ group.group }}
          </div>

        <!-- 分组内容（展开时显示，模拟子项缩进） -->
        <div
          class="group-content"
          v-show="activeGroup.includes(groupIdx)"
          :style="{ marginLeft: '24px' }"
        >
          <div
            v-for="(field, fieldKey) in group.list"
            :key="fieldKey"
            class="form-item"
            :style="{ display: isFieldVisible(group, field, fieldKey) ? '' : 'none' }"
          >
            <label class="form-label">
              {{ fieldKey }}
              <el-tooltip
                :content="Array.isArray(field.desc) ? field.desc.join(', ') : (field.desc || '暂无描述')"
                placement="top"
                effect="dark"
              >
                <span class="help-icon">?</span>
              </el-tooltip>
            </label>

            <div class="input-container">
              <template v-if="field.type === 'enum'">
                <el-select
                  v-model="formData[fieldKey]"
                  placeholder="请选择"
                  class="form-select input-control"
                  
                  @change="(val) => handleChange(fieldKey, val)"
                >
                  <el-option
                    v-for="(opt, optIdx) in field.list"
                    :key="optIdx"
                    :label="opt"
                    :value="opt"
                  />
                </el-select>
              </template>
              <template v-else-if="field.type === 'uint8'">
                <el-input-number
                  v-model="formData[fieldKey]"
                  :min="field.min"
                  :max="field.max"
                  :unit="field.unit || ''"
                  class="form-input input-control"
                  
                  @change="(val) => handleChange(fieldKey, val)"
                />
              </template>
              <template v-else-if="field.type === 'PWM' || field.type === 'IIC'">
                <el-select
                  v-model="formData[fieldKey]"
                  placeholder="请选择"
                  class="form-select input-control"
                  
                  @change="(val) => handleChange(fieldKey, val)"
                >
                  <el-option
                    v-for="(opt, optIdx) in getOptions(field.type)"
                    :key="optIdx"
                    :label="opt"
                    :value="opt"
                  />
                </el-select>
              </template>
              <template v-else-if="field.type === 'array'">
                <el-input
                  v-model="formData[fieldKey]"
                  placeholder="请输入数字数组，用逗号分隔"
                  class="form-input input-control"
                
                  @change="(val) => handleArrayChange(fieldKey, val)"
                />
              </template>
            </div>
          </div>
        </div>

        <!-- 分组分割线 -->
        <div
          v-if="groupIdx < (config?.variables?.length || 0) - 1"
          class="group-divider"
        ></div>
      </template>
    </div>

    <!-- 🎯 配置修改加载对话框 -->
    <el-dialog
      v-model="isConfigChangeRequesting"
      title=""
      width="480px"
      :close-on-click-modal="false"
      :show-close="false"
      :draggable="false"
      align-center
      class="config-request-dialog"
    >
      <div class="request-dialog-content">
        <!-- 图标和标题 -->
        <div class="request-dialog-header">
          <div class="request-icon">
            <el-icon class="rotating-icon">
              <Loading />
            </el-icon>
          </div>
          <h3 class="request-title">配置修改中</h3>
        </div>

        <!-- 内容描述 -->
        <div class="request-dialog-body">
          <div class="request-info">
            <div class="info-item">
              <span class="info-label">修改字段：</span>
              <span class="info-value">{{ currentRequestInfo?.fieldLabel }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">修改值：</span>
              <span class="info-value">{{ currentRequestInfo?.fieldValue }}</span>
            </div>
          </div>
          <div class="request-message">
            请等待当前修改完成后，再进行其他配置更改
          </div>
        </div>

        <!-- 进度指示 -->
        <div class="request-progress">
          <el-progress
            :percentage="100"
            :indeterminate="true"
            :duration="3"
            :stroke-color="'#409eff'"
            :show-text="false"
          />
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, nextTick, watch, toRaw } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus';
import { Loading } from '@element-plus/icons-vue';
import http from '@/utils/http/http';


// 定义props接收config、workspace_path和branch_status
const props = defineProps({
  config: {
    type: Object,
    required: true
  },
  workspacePath: {
    type: String,
    required: true
  },
  branchStatus: {
    type: String,
    required: true
  },
  previousConfig: {
    type: Object,
    default: () => ({})
  },
  hasEditPermission: {
    type: Boolean,
    default: true
  }
});

// 用于接收子组件配置变更事件
const emit = defineEmits(['configChanged']);

// 表单数据初始化
const formData = reactive({});
// 保存上一次的表单数据
const previousFormData = reactive({});
// 标记配置是否已变更
const isConfigChanged = ref(false);

console.log('workspacePath:', props.workspacePath);
console.log('branchStatus:', props.branchStatus);

let currentMessage = null; // 用于存储当前消息实例

// 🎯 配置修改请求状态管理
const isConfigChangeRequesting = ref(false);
const currentRequestInfo = ref(null);

// 深度克隆对象（处理数组和对象）
const deepClone = (obj) => {
  if (obj === null || typeof obj !== 'object') {
    // 对于数字类型，直接返回数字而非字符串
    return typeof obj === 'string' && !isNaN(Number(obj)) ? Number(obj) : obj;
  }
  if (Array.isArray(obj)) {
    return obj.map(item => deepClone(item));
  } else {
    const clone = {};
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clone[key] = deepClone(obj[key]);
      }
    }
    return clone;
  }
};

// 初始化 userDefined 默认值 + blinding_content
props.config.userDefined.forEach(item => {
  const defaultOpt = item.list[item.default_id];
  formData[item.name] = defaultOpt ? defaultOpt.name : '';
  previousFormData[item.name] = formData[item.name]; // 初始化上一次数据
  
  if (defaultOpt && defaultOpt.blinding_content) {
    Object.entries(defaultOpt.blinding_content).forEach(([fieldKey, field]) => {
      // 处理数组默认值，转换为数字数组
      formData[fieldKey] = Array.isArray(field.default) 
        ? field.default.map(val => Number(val)) 
        : field.default;
      previousFormData[fieldKey] = formData[fieldKey]; // 初始化上一次数据
    });
  }
});

// 初始化 variables 默认值 + 关联关系
const fieldRelations = reactive({});
props.config.variables.forEach(group => {
  Object.entries(group.list).forEach(([key, field]) => {
    // 处理数组默认值，转换为数字数组
    formData[key] = Array.isArray(field.default) 
      ? field.default.map(val => Number(val)) 
      : field.default;
    previousFormData[key] = formData[key]; // 初始化上一次数据
    
    if (field.type === 'enum' && !field.list.includes(field.default)) {
      console.error(`字段 ${key} 默认值错误，已修正为第一个选项`);
      formData[key] = field.list[0];
      previousFormData[key] = field.list[0]; // 更新上一次数据
    }
    
    fieldRelations[key] = 
      typeof field.blinding_index === 'number' 
        ? [field.blinding_index] 
        : (typeof field.blinding_index === 'string' 
            ? field.blinding_index.split(',').map(Number) 
            : []);
  });
});

// 监听formData变化，更新previousFormData
watch(
  () => toRaw(formData),
  (newVal) => {
    if (isConfigChanged.value) return; // 正在处理变更时不更新
    // 深度克隆并正确处理数字数组
    Object.keys(newVal).forEach(key => {
      previousFormData[key] = deepClone(newVal[key]);
    });
  },
  { deep: true, immediate: true }
);

// 控制分组展开收起 - 默认展开所有分组
const activeGroup = ref(props.config.variables.map((_, index) => index)); 

function toggleGroup(groupIdx) {
  const index = activeGroup.value.indexOf(groupIdx);
  if (index > -1) {
    activeGroup.value.splice(index, 1); // 收起分组
  } else {
    activeGroup.value.push(groupIdx); // 展开分组
  }
}

// ========== 关联逻辑 ==========
function isUserDefinedFieldVisible(item, selectedOpt, fieldKey) {
  const currentOpt = Object.values(item.list).find(opt => opt.name === formData[item.name]);
  return currentOpt ? currentOpt.blinding_content[fieldKey].default === "STD_ON" : false;
}

function handleEnumChange(group, field, fieldKey) {
  const selectedIndex = field.list.indexOf(formData[fieldKey]);
  Object.entries(group.list).forEach(([key, f]) => {
    if (key === fieldKey) return;
    const relIndex = fieldRelations[key];
    if (relIndex.length > 0 && !relIndex.includes(selectedIndex)) {
      formData[key] = f.default;
      // 触发变更检测
      handleChange(key, f.default);
    }
  });
  nextTick();
}

function isFieldVisible(group, field, fieldKey) {
  const relIndex = fieldRelations[fieldKey];
  let enumField = null, enumFieldKey = '';
  Object.entries(group.list).forEach(([key, f]) => {
    if (f.blinding_items) {
      enumField = f;
      enumFieldKey = key;
    }
  });
  if (field.blinding_items) return true;
  if (!enumField) return true;
  const selectedIndex = enumField.list.indexOf(formData[enumFieldKey]);
  return relIndex.length === 0 || (selectedIndex !== -1 && relIndex.includes(selectedIndex));
}

// 模拟 PWM/IIC 通道选项
function getOptions(type) {
  return type === 'PWM' 
    ? ['PWM_CH1', 'PWM_CH2', 'PWM_CH3'] 
    : type === 'IIC' 
      ? ['I2C_CH1', 'I2C_CH2'] 
      : [];
}

const handleChange = (label_name, newVal) => {
  // 处理非数组类型的值
  let valueToSave = newVal;
  if (!Array.isArray(newVal)) {
    valueToSave = typeof newVal === 'string' && !isNaN(Number(newVal))
      ? Number(newVal)
      : newVal;
  }

  console.log('数据变更:', label_name, valueToSave, '权限状态:', props.hasEditPermission);

  // 检查编辑权限
  if (!props.hasEditPermission) {
    // 没有编辑权限时，自动回退到上一次的数据
    console.log('无编辑权限，自动回退数据:', label_name, previousFormData[label_name]);

    // 先关闭可能存在的旧消息
    if (currentMessage) {
      currentMessage.close();
    }

    // 显示权限提示消息
    currentMessage = ElMessage.warning('当前无编辑权限，数据已自动回退');

    // 延迟回退数据，确保用户能看到变化过程
    setTimeout(() => {
      formData[label_name] = previousFormData[label_name];
      isConfigChanged.value = false;
    }, 500);

    return; // 直接返回，不执行后续的API调用
  }

  // 有编辑权限时，正常更新数据并调用API
  formData[label_name] = valueToSave; // 更新表单数据
  isConfigChanged.value = true; // 标记配置已变更

  const config_path = "Brightness/Config_Brightness" + '/' + label_name;
  let config_value = valueToSave;

  // 对于数组类型，转换为逗号分隔的字符串进行传输
  if (Array.isArray(valueToSave)) {
    config_value = valueToSave.map(String).join(',');
  }

  // 先关闭可能存在的旧消息
  if (currentMessage) {
    currentMessage.close();
  }

  // 🎯 显示加载对话框
  isConfigChangeRequesting.value = true;
  currentRequestInfo.value = {
    fieldLabel: label_name,
    fieldValue: Array.isArray(valueToSave) ? valueToSave.join(', ') : valueToSave
  };

  http.post('/code_management/config_params', {
    params: {
      path: config_path,
      value: config_value,
      workspace_path: props.workspacePath,
      branch_status: props.branchStatus
    }
  }).then(response => {
    console.log("配置更改详情：", response.data);

    // 🎯 关闭加载对话框
    isConfigChangeRequesting.value = false;
    currentRequestInfo.value = null;

    if (response.data.config_status === 1) {
      // 关闭之前的消息并显示新消息
      if (currentMessage) {
        currentMessage.close();
      }
      currentMessage = ElMessage.success('配置更新成功');
      // 成功后更新上一次配置
      previousFormData[label_name] = valueToSave;
      isConfigChanged.value = false;
      // 通知父组件配置已变更
      emit('configChanged', formData);
    } else {
      // 关闭之前的消息并显示新消息
    if (currentMessage) {
      currentMessage.close();
    }
      currentMessage = ElMessage.error('配置更新失败，正在恢复上一次值');
      // 失败后恢复配置
      setTimeout(() => {
        formData[label_name] = previousFormData[label_name];
        isConfigChanged.value = false;
        if (currentMessage) {
          currentMessage.close();
        }
        currentMessage = ElMessage.warning('已恢复到上一次配置');
      }, 1000);
    }
  }).catch(error => {
    // 🎯 关闭加载对话框
    isConfigChangeRequesting.value = false;
    currentRequestInfo.value = null;

    // 处理请求错误
    if (currentMessage) {
      currentMessage.close();
    }
    currentMessage = ElMessage.error('请求失败，请稍后再试');
    console.error('请求错误:', error);
    // 网络错误时恢复配置
    setTimeout(() => {
      formData[label_name] = previousFormData[label_name];
      isConfigChanged.value = false;
      // 关闭之前的消息并显示新消息
    if (currentMessage) {
      currentMessage.close();
    }
      currentMessage = ElMessage.warning('已恢复到上一次配置');
    }, 1000);
  });
}

// 处理数组输入框的变更
const handleArrayChange = (key, val) => {
  // 将输入的字符串转换为数字数组
  const arrayValue = val.trim().split(',').map(item => {
    const num = Number(item.trim());
    return isNaN(num) ? item.trim() : num;
  });
  handleChange(key, arrayValue);
}
</script>

<style scoped>
/* 整体容器 */
.dynamic-form {
  background-color: #fff;
  color: #666;
}

/* 背光IC选择项样式（优化布局） */
.form-item {
  display: flex;
  align-items: center;
  margin-bottom: 15px; /* 减少底部边距，避免与分割线冲突 */
  position: relative; /* 为绝对定位做准备 */
}


.form-label {
  width: 180px; /* 适当增加标签宽度，确保标签内容不溢出 */
  text-align: left;
  margin-right: 120px; /* 减少标签与控件之间的间距 */
  color: #666;
  font-size: 14px;
  font-weight: 400;
  white-space: nowrap;
  flex-shrink: 0; /* 防止标签宽度被压缩 */
}

/* 用户自定义项标签特殊样式 */
.user-defined-label {
  color: #666;
  font-weight: 600;
  margin-left: 6px;
}

/* 帮助图标样式 */
.help-icon {
  display: inline-block;
  margin-left: 6px;
  width: 16px;
  height: 16px;
  line-height: 16px;
  text-align: center;
  background-color: #e1f3ff;
  color: #409eff;
  border: 1px solid #b3d8ff;
  border-radius: 50%;
  font-size: 12px;
  font-weight: bold;
  cursor: help;
  transition: all 0.3s ease;
}

.help-icon:hover {
  background-color: #409eff;
  color: #fff;
  border-color: #409eff;
  transform: scale(1.1);
  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.3);
}

/* 统一表单控件布局 */
.input-container {
  flex: 1; /* 占据剩余空间 */
  position: relative;
}

.input-control {
  width: 100%; /* 宽度占满容器 */
  font-size: 14px;
  box-sizing: border-box; /* 确保内边距和边框不影响宽度 */
}


.el-input-number {
  width:100%;
  font-size: 14px;
  margin-left: 0; /* 移除多余的左边距 */
}

/* 变量分组区域（核心：优化箭头样式） */
.variables-container {
  margin-top: 20px; /* 和上方模块拉开距离 */
}


.group-title {
  font-size: 14px;
  color: #666;
  font-weight: 600;
  cursor: pointer;
  padding: 12px 0; /* 🔧 增大分组标题的上下间距，从8px调整为12px */
  margin: 8px 0; /* 🔧 增加分组标题的外边距 */
  display: flex;
  align-items: center;
  /* 让标题和左侧菜单的折叠项视觉一致 */
  &:hover {
    background-color: #f5f7fa;
  }
}


.group-content {
  /* 模拟子项缩进，和左侧菜单的层级对齐 */
  margin-left: 24px;
  padding: 10px 0 10px 10px; /* 🔧 增加上下内边距，让分组内容更有呼吸感 */
  border-left: 1px solid #ccc; /* 增加分组边框，提升视觉层级 */
}

/* 统一分割线样式 */
.group-divider,
.user-defined-divider,
.section-divider {
  height: 0.5px;
  background-color: #ccc;
  margin: 20px 0;
  width: 100%;
  border: none;
  flex-shrink: 0;
  display: block;
  clear: both;
}

/* 优化后的箭头图标样式 */
.group-arrow {
  transition: transform 0.3s ease;
  font-size: 12px;
  color: #000;
  margin-right: 8px; /* 固定箭头与文字间距 */
}

.is-open {
  transform: rotate(90deg);
}

/* 交互态优化 */
.el-select:hover, .el-input-number:hover, .el-input:hover {
  border-color: #409eff;
}

.el-select:focus, .el-input-number:focus, .el-input:focus {
  border-color: #409eff;
  box-shadow: 0 0 0 2px #666;
}

/* 响应式调整 - 小屏幕适配 */
@media (max-width: 768px) {
  .form-label {
    width: 120px; /* 小屏幕下缩小标签宽度 */
  }
}

/* 配置修改弹框样式 */
.chip-request-dialog {
  --el-dialog-border-radius: 12px;
}

.chip-request-dialog .el-dialog {
  border-radius: 12px;
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

.chip-request-dialog .el-dialog__header {
  display: none;
}

.chip-request-dialog .el-dialog__body {
  padding: 0;
}

.request-dialog-content {
  padding: 32px 24px 24px;
  text-align: center;
  background: linear-gradient(135deg, #f8faff 0%, #ffffff 100%);
}

.request-dialog-header {
  margin-bottom: 24px;
}

.request-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 64px;
  height: 64px;
  background: linear-gradient(135deg, #409eff 0%, #fff 100%);
  border-radius: 50%;
  margin-bottom: 16px;
  box-shadow: 0 4px 16px rgba(64, 158, 255, 0.3);
}

.request-icon .el-icon {
  font-size: 28px;
  color: white;
}

.rotating-icon {
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.request-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  line-height: 1.4;
}

.request-dialog-body {
  margin-bottom: 24px;
}

.request-info {
  background: #f4f7ff;
  border: 1px solid #e1e8ff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
}

.info-item {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 8px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.info-value {
  font-size: 14px;
  color: #409eff;
  font-weight: 600;
  background: #ecf5ff;
  padding: 2px 8px;
  border-radius: 4px;
}

.request-message {
  font-size: 14px;
  color: #909399;
  line-height: 1.6;
  padding: 0 8px;
}

.request-progress {
  margin-top: 20px;
}

.request-progress .el-progress {
  margin-bottom: 0;
}

.request-progress .el-progress__text {
  display: none;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .chip-request-dialog .el-dialog {
    width: 90% !important;
    margin: 0 auto;
  }

  .request-dialog-content {
    padding: 24px 16px 16px;
  }

  .request-icon {
    width: 56px;
    height: 56px;
  }

  .request-icon .el-icon {
    font-size: 24px;
  }

  .request-title {
    font-size: 18px;
  }
}

</style>