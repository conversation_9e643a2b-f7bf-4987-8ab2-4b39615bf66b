<template>
    <div style="display: flex; flex-direction: column; height: calc(104.9vh - 250px);">
        <div class="tool-bar-container" style="display: flex;">
            <el-button icon="Plus" type="primary" @click="handleAdd">新增</el-button>
            <div style="margin-left: auto; display: flex; gap: 10px;">
                <el-tooltip class="box-item" effect="dark" content="重置已添加的筛选条件" placement="top-start">
                    <el-button text bg @click="handleReset">重置</el-button>
                </el-tooltip>
                <filterButton @click="onFilterStatusChange" :count="filterCount" />
                <el-button icon="Refresh" text bg @click="handleRefresh">刷新</el-button>
            </div>
        </div>

        <div class="filter-container" v-if="showFilterContainer">
            <el-input v-model="form.file_name" placeholder="请输入名称" @keyup.enter="onFilter" clearable>
                <template #append>
                    <el-button icon="Search" @click="onFilter"></el-button>
                </template>
            </el-input>
            <el-input v-model="form.version" placeholder="请输入版本" @keyup.enter="onFilter" clearable>
                <template #append>
                    <el-button icon="Search" @click="onFilter"></el-button>
                </template>
            </el-input>
        </div>

        <el-table :data="tableData" stripe border style="width: 100%;flex: 1;" class="table-container">
            <el-table-column prop="project_name" label="所属项目" min-width="300" align="center">
                <template #default="{ row }">
                    {{ row.project_name }}
                </template>
            </el-table-column>
            <el-table-column prop="file_name" label="名称" min-width="300" align="center">
                 <template #default="{ row }">
                    <el-link type="primary" :underline="false" @click="handleDownload(row)">{{ row.file_name }}</el-link>
                </template>
            </el-table-column>
            <el-table-column prop="version" label="版本" min-width="200" align="center">
            </el-table-column>
            <el-table-column prop="description" label="描述" min-width="300" align="center">
            </el-table-column>

            <el-table-column label="操作" min-width="130" fixed="right" align="center">
                <template #default="{ row }">
                    <div style="display: flex; justify-content: center; gap: 10px;">
                        <el-button type="primary" size="small" @click="handleEdit(row)">编辑</el-button>
                        <el-button type="danger" size="small" @click="handleDelete(row)">删除</el-button>
                    </div>
                </template>
            </el-table-column>

        </el-table>
    </div>

    <div class="pagination-container">
        <el-pagination :page-sizes="[1, 10, 15, 20, 25, 50, 100]" layout="prev, pager, next, jumper, total, sizes"
            v-model:current-page="form.page" v-model:page-size="form.pagesize" :total="total" background
            @change="onPageChange" />
    </div>

    <el-dialog v-if="dialogAddVisible" v-model="dialogAddVisible" title="添加DBC文件" width="800"
        :close-on-click-modal="false">
        <Add @confirm="onAddSuccess" @cancel="onAddCancel" />
    </el-dialog>

    <el-dialog v-if="dialogEditVisible" v-model="dialogEditVisible" title="编辑测试产品" width="800"
        :close-on-click-modal="false">
        <Edit @confirm="onEditSuccess" @cancel="onEditCancel" :r_id="r_id" />
    </el-dialog>

    <el-drawer v-model="drawerDetailVisible" :with-header="false" size="70%" :destroy-on-close="true">
        <Detail :r_id="r_id" />
    </el-drawer>

</template>


<script setup>
import { ref, reactive, onMounted, watch } from 'vue';
import http from '@/utils/http/http.js';
import Add from './add.vue';
import Edit from './edit.vue';
import filterButton from '@/components/filterButton.vue';
import { ElMessageBox } from 'element-plus';
import Detail from './detail.vue';
import { useProjectStore } from '@/stores/project.js';

const tableData = ref([]);

const r_id = ref(0);

const drawerDetailVisible = ref(false);
const dialogAddVisible = ref(false);
const filterCount = ref(0);
const dialogEditVisible = ref(false);
const projectStore = useProjectStore();

let form = reactive({
    page: 1,
    pagesize: 15,
});

let total = ref(0);

let showFilterContainer = ref(false);

function onFilterStatusChange() {
    showFilterContainer.value = !showFilterContainer.value;
};

function handleDownload(row) {
    const fullUrl = `${import.meta.env.VITE_BASE_URL}/can_dbc/${row.id}/download`;
    window.open(fullUrl, '_blank');
}

function update_table() {
    http.get('/can_dbc', { params: form }).then(res => {
        tableData.value = res.data.data.results;
        total.value = res.data.data.count;
    });

    filterCount.value = Object.keys(form).filter(key => !['page', 'pagesize', 'project_number'].includes(key)).reduce((count, key) => {
        if (form[key] == '' || form[key] == undefined || form[key] == null || form[key].length == 0) {
            return count;
        } else {
            return count + 1;
        }
    }, 0)
};

function handleReset() {
    const projectNumber = form.project_number;
    Object.keys(form).forEach(key => {
        delete form[key];
    });
    form.page = 1;
    form.pagesize = 10;
    form.project_number = projectNumber;
    update_table();
};

function onPageChange() {
    update_table();
};

function onFilter() {
    form.page = 1;
    form.pagesize = 10;
    update_table();
};

function handleAdd() {
    dialogAddVisible.value = true;
};

function handleEdit(row) {
    r_id.value = row.id;
    dialogEditVisible.value = true;
};

function onDetail(row) {
    r_id.value = row.id;
    drawerDetailVisible.value = true;
};

function handleDelete(row) {
    ElMessageBox.confirm(
        '确定删除吗?',
        '提示',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }
    ).then(() => {
        http.delete(`/can_dbc/${row.id}`).then(res => {
            ElMessage({
                message: '删除成功.',
                type: 'success',
            });
            update_table();
        }).catch(err => {
            ElMessageBox.alert(err.response.data.msg, '删除失败', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'error',
            });
        });
    }).catch(() => {
        ElMessage({
            type: 'info',
            message: '已取消删除'
        });
    });
};

function onAddSuccess() {
    dialogAddVisible.value = false;
    update_table();
};

function onAddCancel() {
    dialogAddVisible.value = false;
};

function onEditSuccess() {
    dialogEditVisible.value = false;
    update_table();
};

function onEditCancel() {
    dialogEditVisible.value = false;
};

function handleRefresh() {
    update_table();
};

watch(() => projectStore.project_info, () => {
    form.project_number = projectStore.project_info.projectCode;
    update_table();
});

onMounted(() => {
    form.project_number = projectStore.project_info.projectCode;
    update_table();
});



</script>


<style scoped>
.search-container {
    width: 100%;
    display: flex;
    justify-content: center;
    justify-items: center;
    height: 40px;
}

.search-input {
    width: 50%;
    max-width: 500px;
}

.search-button {
    border: 1px solid #dcdfe6;
}

.filter-container {
    width: 100%;
    display: flex;
    justify-content: flex-start;

    margin-bottom: 10px;

    .el-input {
        width: 50%;
        max-width: 300px;
        margin-right: 10px;
    }
}
</style>