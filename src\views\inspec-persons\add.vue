<template>
    <el-divider />
    <div class="add-container">
        <el-form :model="form" label-width="auto" :rules="rules" status-icon ref="formRef">

            <el-form-item label="轮班人员" prop="user_email">
                <Organizaiton v-model="form.user_email" ref="userRef" />
            </el-form-item>


            <div class="submit-button-container">
                <el-button type="default" @click="onCancel">取消</el-button>
                <el-button type="primary" @click="onSubmit">提交</el-button>
            </div>

        </el-form>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import Organizaiton from '@/components/Organization/index.vue';
import http from '@/utils/http/http.js';

const formRef = ref(null);
const userRef = ref(null);

const form = ref({
    user_email: '',
    user_name: '',
});

const rules = ref({
    user_email: [
        { required: true, message: '请选择轮班人员', trigger: 'blur' },
    ],
});


const emit = defineEmits(['confirm', 'cancel'])

const onSubmit = () => {
    formRef.value.validate(async (valid) => {
        if (valid) {
            let data = {
                ...form.value,
            };

            data.user_name = userRef.value.getNode(form.value.user_email).data.realName;

            http.post('/inspecs/persons', data).then(res => {
                emit('confirm');
            }).catch(err => {
                ElMessageBox.alert(err.response.data.msg, '提交失败', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'error',
                })
            });
        };
    });
};

const onCancel = () => {
    emit('cancel');
};

onMounted(() => {

   
});

</script>


<style lang="scss" scoped>
.add-container {
    padding: 0 20px;
}

.submit-button-container {
    display: flex;
    justify-content: flex-end;
}
</style>