<template>
  <div class="execution-container">
    <!-- 左侧执行记录区域 -->
    <div class="left-panel">

      <div class="execution-list">
        <!-- 表格头部 -->
        <div class="table-header">
          <div class="table-row header-row">
            <div class="table-cell cell-index">序号</div>
            <div class="table-cell cell-jobname">流程名称</div>
            <div class="table-cell cell-trigger">触发者</div>
            <div class="table-cell cell-time">触发时间</div>
            <div class="table-cell cell-result">执行状态</div>
            <div class="table-cell cell-button">查看日志</div>
          </div>
        </div>
        
        <!-- 表格内容 -->
        <div class="table-body">
            <div 
            v-for="(record, index) in executionRecords" 
            :key="record.id"
            class="table-row data-row"
            :class="{ 
              'active': selectedRecord?.id === record.id,
              [`status-${record.status}`]: true 
            }"
          >
            <div class="table-cell cell-index">{{ index + 1 }}</div>
              <div class="table-cell cell-jobname" :title="record.job_name">{{ record.job_name }}</div>
            <div class="table-cell cell-trigger">{{ record.created_by || '未知用户' }}</div>
            <div class="table-cell cell-time">{{ formatTime(record.start_time) }}</div>
            <div class="table-cell cell-result">
              <el-tag size="small" effect="plain" :type="tagType(record.status)">
                {{ record.status }}
              </el-tag>
            </div>
            <div class="table-cell cell-button">
              <el-button type="primary" size="small" @click="handleViewJenkinsLog(record)">查看</el-button>
            </div>
          </div>
        </div>
        
        <div v-if="executionRecords.length === 0" class="empty-state">
          <i class="el-icon-document"></i>
          <p>暂无执行记录</p>
        </div>
        
        <!-- 分页器 -->
        <div class="pagination-container">
          <el-pagination
            v-if="totalRecords > 0"
            background
            layout="prev, pager, next, jumper, total, sizes"
            :total="totalRecords"
            :page-size="pageSize"
            :current-page="currentPage"
            :page-sizes="[20, 50, 100]"
            @current-change="handlePageChange"
            @size-change="handleSizeChange"
            class="pagination"
          />
        </div>
      </div>
    </div>

    <!-- 右侧区域 -->
    <div class="right-panel">
      <!-- 顶部流水线状态 -->
      <div class="pipeline-status">
        
        <div v-if="displayStages.length > 0" class="pipeline-container">
          <div class="pipeline-flow" ref="pipelineFlow">
            <div class="flow-node start-node">
              <div class="node-content">
                <span>开始</span>
              </div>
            </div>
            
            <!-- 箭头和阶段节点 -->
            <template v-for="(stage, index) in displayStages" :key="stage.id">
              <div class="flow-arrow"></div>
              <div class="flow-node stage-node" :class="`status-${stage.status}`">
                <div class="node-content">
                  <span>{{ stage.name }}</span>
                </div>
              </div>
            </template>
            
            <!-- 结束节点 -->
            <div class="flow-arrow"></div>
            <div class="flow-node end-node">
              <div class="node-content">
                <span>结束</span>
              </div>
            </div>
          </div>
          
          <!-- 流水线滚动条 -->
          <div class="pipeline-scrollbar" ref="scrollbarTrack">
            <div class="scrollbar-thumb" ref="scrollbarThumb" :style="scrollbarThumbStyle"></div>
          </div>
        </div>
        
        <div v-else class="no-stages">
          <p>{{ loading ? '正在加载流水线配置...' : '暂无流水线配置' }}</p>
        </div>
      </div>

      <!-- 底部日志区域 -->
      <div class="logs-section">
        <div class="logs-header">
          <h4>执行日志</h4>
          <div class="log-controls">
            <el-button @click="clearLogs" size="small">清空</el-button>
            <el-checkbox v-model="autoScroll">自动滚动</el-checkbox>
          </div>
        </div>
        

        <div class="operation-logs-section" v-custom-loading="jenkinsLoading">
          <div class="log-header">操作日志</div>
          <div class="log-container">
            <div v-for="(log, index) in operationLogs" :key="index" class="log-item">
              {{ log }}
            </div>
            <div v-if="operationLogs.length === 0" class="logs-placeholder">
              <p>暂无操作日志</p>
            </div>
          </div>
        </div>
        
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, computed, watch } from 'vue'
import { useRoute } from 'vue-router'
import { ElNotification } from 'element-plus'
import http from '@/utils/http/http.js'
import { useProjectStore } from '@/stores/project.js'
import { add } from 'lodash'

const route = useRoute()
const projectStore = useProjectStore()
const loading = ref(false)
const projectConfig = ref({})
const projectStages = ref([])
const jenkinsLoading = ref(false)
// 根据当前路由判断模式：优先取 query.mode；

// 若无 mode，但带 auto=1/true 则按“执行模式”；否则在 /cicd/execution 下默认 view
const getPageMode = () => {
  const qMode = String(route.query.mode || '').toLowerCase()
  if (qMode) return qMode
  const path = String(route.path || '')
  const auto = String(route.query.auto || '').toLowerCase()
  if (auto === '1' || auto === 'true') return ''
  return path.includes('/cicd/execution') ? 'view' : ''
}

// 视图层渲染的阶段：显示所有已配置的阶段
const displayStages = computed(() => {
  return projectStages.value || []
})

const lastProjectId = ref(null)

const logs = ref([])
const executionRecords = ref([])
const selectedRecord = ref(null)

// UI状态
const autoScroll = ref(true)
const logsContainer = ref(null)

// 流水线滚动相关
const pipelineFlow = ref(null)
const scrollbarTrack = ref(null)
const scrollbarThumb = ref(null)
const scrollbarThumbStyle = ref({
  width: '50px',
  left: '0px',
})

// 轮询控制
const isPollingLogs = ref(false)
const pollLogsTimer = ref(null)

// 操作日志相关（从workflow迁移）
const operationLogs = ref([])
const currentPage = ref(1)
const pageSize = ref(20)
const totalRecords = ref(0)
// 将中文状态映射为 Element Plus tag 的类型
const tagType = (text) => {
  const v = String(text || '').trim()
  if (v === '成功') return 'success'   // 绿色
  if (v === '失败') return 'danger'    // 红色
  if (v === '运行中') return 'warning' // 橙色
  if (v === '等待中') return 'info'     // 灰色
  return 'info'
}

// 解析流水线阶段
const parseProjectStages = (rawConfig) => {
  if (!rawConfig || !rawConfig.nodes) {
    return []
  }
  
  const stages = rawConfig.nodes
    .filter(node => {
      // 过滤条件：有 label 且 executionOrder 存在（包括 0）
      const hasLabel = node.label && node.label.trim() !== ''
      const hasOrder = node.executionOrder !== undefined && node.executionOrder !== null
      return hasLabel && hasOrder
    })
    .sort((a, b) => {
      const orderA = Number(a.executionOrder) || 0
      const orderB = Number(b.executionOrder) || 0
      return orderA - orderB
    })
    .map((node, index) => ({
      id: index,
      name: node.label.replace(/\s*\(已配置\)/, ''),
      status: 'pending',
      executionOrder: node.executionOrder,
      type: node.type
    }))
  
  return stages
}

const refreshExecutionList = async () => {
  loading.value = true
  addLog('📊 开始加载项目配置和执行记录')
  try { 
    const row_id = route.query.editId || 'latest'  
    const response = await http.get(`/auto_jenkins/projects/`,
     {params: { editId: row_id }}
    )
    
    if (!response.data.success) {
      throw new Error(response.data.message || '获取配置失败')
    }
    
    lastProjectId.value = response.data.data.id  //记一下当前的状态
    const loadConfig = response.data.data
    projectConfig.value = loadConfig
    
    if (loadConfig?.frontend_raw_config) {
      try {
        let rawConfig = typeof loadConfig.frontend_raw_config === 'string'
          ? JSON.parse(loadConfig.frontend_raw_config)
          : loadConfig.frontend_raw_config
        projectStages.value = parseProjectStages(rawConfig)
      } catch (e) {
        console.error('解析stages失败:', e)
        projectStages.value = []
      }
    }
    
    // 拉取执行记录：view 模式下不传过滤参数 → 后端返回全量；其他模式按 git_url/branch 过滤
    const mode = getPageMode()
    const recordsParams = mode === 'view'
      ? {}
      : (loadConfig?.git_url
          ? { git_url: loadConfig.git_url, branch: loadConfig.branch || 'main' }
          : {})

    // 若选择了全局项目，则按“项目名称(编号)”过滤构建记录
    const selected = projectStore.project_info
    if (selected?.name && selected?.projectCode) {
      const belongProject = `${selected.name}(${selected.projectCode})`
      recordsParams.belong_project = belongProject
    }

    if (mode !== 'view' && loadConfig?.git_url) {
      addLog(`🔗 Git URL: ${loadConfig.git_url}`)
      addLog(`🌿 分支: ${loadConfig.branch || 'main'}`)
    }

    // 添加分页参数
    recordsParams.page = currentPage.value
    recordsParams.page_size = pageSize.value

    const recordsResponse = await http.post('/auto_jenkins/builds/list/', recordsParams)
    if (recordsResponse.data.success) {
      executionRecords.value = recordsResponse.data.data || []
      totalRecords.value = recordsResponse.data.total || 0
      addLog(`📋 获取到 ${executionRecords.value.length} 条历史执行记录，共 ${totalRecords.value} 条`)
      
      const auto = String(route.query.auto || '').toLowerCase()
      const shouldAuto = auto === '1' || auto === 'true'
      if (shouldAuto && mode !== 'view') {
        // 先移除 URL 中的 auto，避免本标签页后续刷新再次自动执行
        try {
          const url = new URL(window.location.href)
          url.searchParams.delete('auto')
          window.history.replaceState({}, '', url.toString())
        } catch (_) {}

        // 执行新的构建任务（仅非 view 模式）
        await executeNewBuild(loadConfig)
      } else if (!shouldAuto) {
        addLog('ℹ️ 已加载配置与历史记录,未自动执行。如需自动执行，请通过流程列表点击“执行”或在URL中携带 auto=1 参数')
      }
    } else {
      executionRecords.value = []
    }
  } catch (error) {
    console.error('❌ refreshExecutionList 失败:', error)
    addLog(`❌ 加载配置失败: ${error.message}`)
    ElNotification({
      title: '错误',
      message: `加载失败: ${error.message}`,
      type: 'error',
      duration: 3000
    })
  } finally {
    loading.value = false
    addLog('✅ 配置加载完成')
    nextTick(() => {
      initScrollbar()
    })
  }
}

// 7. 开始轮询
const startPollingTaskStatus = (taskId) => {
  if (isPollingLogs.value) {
    clearInterval(pollLogsTimer.value)
  }
  
  isPollingLogs.value = true
  addLog(`🔄 开始监控任务状态，任务ID: ${taskId}`)
  
  const pollStatus = async () => {
    try {
      const response = await http.get(`/auto_jenkins/tasks/${taskId}/status/`, {
        timeout: 30000
      })
      
      if (response.data.success) {
        const taskData = response.data.data
        
        if (taskData.latest_logs && taskData.latest_logs.length > 0) {
          logs.value = taskData.latest_logs
          
          // 添加到操作日志（从workflow迁移的效果）
          taskData.latest_logs.forEach(log => {
            addLog('📝 ' + log)
          })
          
          if (autoScroll.value) {
            nextTick(() => scrollToBottom())
          }
        }
        
        const currentRecord = executionRecords.value.find(r => r.task_id === taskId)
        if (currentRecord) {
          currentRecord.status = taskData.status
        }
        
        if (['completed', 'failed', 'stopped','success'].includes(taskData.status)) {
          addLog(`🏁 任务执行完成，状态: ${taskData.status}`)
          stopPollingTaskStatus()
          setTimeout(() => {
            refreshExecutionRecordsOnly()
          }, 2000)
        }
      } else {
        console.warn('⚠️ 获取任务状态失败:', response.data.message)
      }
    } catch (error) {
      console.error('⚠️ 轮询状态异常:', error)
    }
  }
  
  pollStatus()
  pollLogsTimer.value = setInterval(pollStatus, 5000)
}

const stopPollingTaskStatus = () => {
  if (pollLogsTimer.value) {
    clearInterval(pollLogsTimer.value)
    pollLogsTimer.value = null
  }
  isPollingLogs.value = false
  addLog('⏹️ 停止监控任务状态')
}

// 分页事件处理
const handlePageChange = (page) => {
  currentPage.value = page
  refreshExecutionRecordsOnly()
}

const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1 // 重置到第一页
  refreshExecutionRecordsOnly()
}

// 13. 只更新执行记录，不触发新构建
const refreshExecutionRecordsOnly = async () => {
  try {
    const loadConfig = projectConfig.value
    const mode = getPageMode()
    const params = mode === 'view'
      ? {}
      : (loadConfig?.git_url ? { git_url: loadConfig.git_url, branch: loadConfig.branch || 'main' } : {})

    // 追加全局项目过滤（如果已选择）
    const selected = projectStore.project_info
    if (selected?.name && selected?.projectCode) {
      const belongProject = `${selected.name}(${selected.projectCode})`
      params.belong_project = belongProject
    }

    // 添加分页参数
    params.page = currentPage.value
    params.page_size = pageSize.value

    const records = await http.post('/auto_jenkins/builds/list/', params)
    if (records.data.success) {
      executionRecords.value = records.data.data || []
      totalRecords.value = records.data.total || 0
    }
  } catch (error) {
    console.error('更新执行记录失败:', error)
  }
}

// 监听全局项目切换，刷新左侧执行记录
watch(() => projectStore.project_info, () => {
  refreshExecutionRecordsOnly()
}, { deep: true })



const handleViewJenkinsLog = async (record) => {
  jenkinsLoading.value = true
  try {
    clearLogs()
    addLog('🔍 正在获取执行日志...')

    // 1. 获取Jenkins日志
    const logResp = await http.get(
      `/auto_jenkins/builds/${record.task_id}/jenkins-logs/`,
      {},
      { timeout: 60000 }
    )

    if (logResp?.data?.success) {
      const data = logResp.data.data || {}
      const lines = Array.isArray(data.jenkins_logs) ? data.jenkins_logs : []
      operationLogs.value = lines
      selectedRecord.value = record
      addLog('✅ 日志加载完成')
    }
    if (lastProjectId.value === record.project_id) {
        addLog("✅ 当前没有切换项目,无需重绘节点")
    }
    else {
      const projectResp = await http.get(`/auto_jenkins/projects/`, {
      params: { editId: record.project_id }  // 根据实际字段调整
    })

    // 3. 重新解析和绘制stages
    if (projectResp?.data?.success) {
      const loadConfig = projectResp.data.data
      if (loadConfig?.frontend_raw_config) {
        const rawConfig = typeof loadConfig.frontend_raw_config === 'string'
          ? JSON.parse(loadConfig.frontend_raw_config)
          : loadConfig.frontend_raw_config
        
        projectStages.value = parseProjectStages(rawConfig)
        lastProjectId.value = record.project_id   //刷新一下缓存 
        addLog('🎨 流水线状态已更新')
      }
    }
    }
  } catch (error) {
    // 错误处理...
  } finally {
    jenkinsLoading.value = false
  }
}


// 执行新的构建任务（从workflow迁移）
const executeNewBuild = async (config) => {
  try {
    addLog('🚀 开始执行新的构建任务...')
    addLog(`📦 项目: ${config.project_name}`)
    addLog(`🔗 Git URL: ${config.git_url}`)
    addLog(`🌿 分支: ${config.branch || 'main'}`)
    
    // 组装构建参数
    const buildParameters = {}
    if (config.pipeline_config && config.pipeline_config.environment) {
      Object.assign(buildParameters, config.pipeline_config.environment)
    }
    
    addLog(`⚙️ 构建参数: ${JSON.stringify(buildParameters)}`)
    
    // 触发构建
    const buildResponse = await http.post(`/auto_jenkins/projects/${config.project_name}/build/`, {
      parameters: buildParameters
    }, {
      timeout: 30000
    })
    
    if (buildResponse.data.success) {
      const taskId = buildResponse.data.data.task_id
      addLog(`✅ 构建已触发，任务ID: ${taskId}`)
      
      // 立即开始轮询新任务
      startPollingTaskStatus(taskId)
      

      setTimeout(() => {  
        refreshExecutionRecordsOnly()   
      }, 1500)
      
    } else {
      throw new Error(buildResponse.data.message || '触发构建失败')
    }
    
  } catch (error) {
    addLog(`❌ 构建执行失败: ${error.message}`)
    console.error('构建执行失败:', error)
    ElNotification({
      title: '错误',
      message: `构建执行失败: ${error.message}`,
      type: 'error',
      duration: 3000
    })
  }
}


const scrollToBottom = () => {
  if (logsContainer.value) {
    logsContainer.value.scrollTop = logsContainer.value.scrollHeight
  }
}

// 流水线自定义滚动条逻辑
const updateScrollbar = () => {
  const container = pipelineFlow.value
  const track = scrollbarTrack.value
  const thumb = scrollbarThumb.value
  
  if (!container || !track || !thumb) return
  
  const scrollWidth = container.scrollWidth
  const clientWidth = container.clientWidth
  const trackWidth = track.offsetWidth
  
  if (scrollWidth <= clientWidth) {
    scrollbarThumbStyle.value.width = '0px'
    return
  }
  
  const thumbWidth = Math.max(20, (clientWidth / scrollWidth) * trackWidth)
  const maxThumbLeft = trackWidth - thumbWidth
  const scrollRatio = container.scrollLeft / (scrollWidth - clientWidth)
  const thumbLeft = Math.max(0, Math.min(maxThumbLeft, scrollRatio * maxThumbLeft))
  
  // 使用requestAnimationFrame优化性能
  requestAnimationFrame(() => {
    scrollbarThumbStyle.value.width = `${thumbWidth}px`
    scrollbarThumbStyle.value.left = `${thumbLeft}px`
  })
}

const initScrollbar = () => {
  const container = pipelineFlow.value
  const track = scrollbarTrack.value
  const thumb = scrollbarThumb.value

  if (!container || !track || !thumb) return
  
  // 节流优化滚动事件
  let scrollTimeout = null
  const throttledUpdateScrollbar = () => {
    if (scrollTimeout) return
    scrollTimeout = requestAnimationFrame(() => {
      updateScrollbar()
      scrollTimeout = null
    })
  }
  
  container.addEventListener('scroll', throttledUpdateScrollbar)
  
  let isDragging = false
  let startX = 0
  let scrollLeftStart = 0
  let thumbStartX = 0

  const onMouseDown = (e) => {
    e.preventDefault()
    e.stopPropagation()
    
    isDragging = true
    startX = e.clientX
    scrollLeftStart = container.scrollLeft
    thumbStartX = thumb.offsetLeft
    
    document.body.style.cursor = 'grabbing'
    document.body.style.userSelect = 'none'
    thumb.style.transition = 'none'

    document.addEventListener('mousemove', onMouseMove, { passive: false })
    document.addEventListener('mouseup', onMouseUp)
  }

  const onMouseMove = (e) => {
    if (!isDragging) return
    e.preventDefault()
    
    const dx = e.clientX - startX
    const trackWidth = track.offsetWidth
    const thumbWidth = thumb.offsetWidth
    const scrollableWidth = container.scrollWidth - container.clientWidth
    
    if (scrollableWidth <= 0) return
    
    // 直接计算新的thumb位置
    const newThumbLeft = Math.max(0, Math.min(trackWidth - thumbWidth, thumbStartX + dx))
    const scrollRatio = newThumbLeft / (trackWidth - thumbWidth)
    const newScrollLeft = scrollRatio * scrollableWidth
    
    // 直接设置滚动位置，避免循环更新
    container.scrollLeft = newScrollLeft
  }

  const onMouseUp = () => {
    if (!isDragging) return
    
    isDragging = false
    document.body.style.cursor = ''
    document.body.style.userSelect = ''
    thumb.style.transition = 'background-color 0.15s ease-out'

    document.removeEventListener('mousemove', onMouseMove)
    document.removeEventListener('mouseup', onMouseUp)
  }

  thumb.addEventListener('mousedown', onMouseDown)

  // 点击轨道滚动
  track.addEventListener('click', (e) => {
    if (e.target === thumb) return

    const rect = track.getBoundingClientRect()
    const clickX = e.clientX - rect.left
    const thumbWidth = thumb.offsetWidth
    const trackWidth = track.offsetWidth
    const scrollableWidth = container.scrollWidth - container.clientWidth
    
    if (scrollableWidth <= 0) return
    
    const newLeft = Math.max(0, Math.min(trackWidth - thumbWidth, clickX - thumbWidth / 2))
    const scrollRatio = newLeft / (trackWidth - thumbWidth)
    const targetScrollLeft = scrollRatio * scrollableWidth
    
    // 平滑滚动到目标位置
    container.scrollTo({
      left: targetScrollLeft,
      behavior: 'smooth'
    })
  })

  // 初始更新
  updateScrollbar()
}


const formatTime = (timeString) => {
  if (!timeString) return '--'
  return new Date(timeString).toLocaleString()
}

const clearLogs = () => {
  logs.value = []
  operationLogs.value = []
}

// 添加日志函数（从workflow迁移）
const addLog = (log) => {
  const now = new Date()
  const hours = now.getHours().toString().padStart(2, '0')
  const minutes = now.getMinutes().toString().padStart(2, '0')
  const seconds = now.getSeconds().toString().padStart(2, '0')
  const timeString = `${hours}:${minutes}:${seconds}`
  operationLogs.value.unshift(`${timeString} - ${log}`)
}

// 生命周期
onMounted(() => {
  addLog('🎯 页面加载完成，开始初始化执行环境')
  refreshExecutionList()
})


// 组件卸载后
onUnmounted(() => {
  // 停止轮询任务状态
  if (isPollingLogs.value) {
    stopPollingTaskStatus()
  }
  
  // 清理定时器
  if (pollLogsTimer.value) {
    clearInterval(pollLogsTimer.value)
    pollLogsTimer.value = null
  }
  
  addLog('🔌 页面卸载，已停止所有轮询和定时器')
})
</script>

<style scoped>
.execution-container {
  display: flex;
  background-color: #f5f5f5;
  overflow: hidden;
}

.execution-container::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
}

.left-panel {
  width: 600px;
  margin-right: 10px ;
  background-color: white;
  border-right: 1px solid #e6e6e6;
  display: flex;
  flex-direction: column;
}

.execution-header {
  padding: 20px;
  border-bottom: 1px solid #e6e6e6;
}

.execution-header h3 {
  margin: 0;
  color: #333;
}

.execution-list {
  margin-right: 30px;
  flex: 0 0 auto;
  max-height: 1200px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.table-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e6e6e6;
  /* 让表头与内容在出现垂直滚动条时更对齐 */
  padding-right: 12px;
}

.table-body {
  flex: 1;
  overflow-y: auto;
  scrollbar-gutter: stable both-edges;
}

.table-row {
  display: flex;
  border-bottom: 1px solid #f0f0f0;
}

.header-row {
  font-weight: 600;
  color: #666;
}

/* 表头文字单行显示，宽度不足用省略号 */
.header-row .table-cell {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.data-row {
  cursor: pointer;
  transition: background-color 0.2s;
}

.data-row:hover {
  background-color: #f8f9fa;
}

.data-row.active {
  background-color: #e3f2fd;
  border-left: 3px solid #2196F3;
}

.table-cell {
  color: #606266;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  min-width: 0; /* 允许在flex容器中收缩，配合省略号生效 */
}

.cell-index { width: 80px; }
.cell-trigger { width: 220px; }
.cell-jobname {
  width: 160px;
  flex: 0 0 160px; /* 固定列宽，超长内容向下换行，不水平撑开 */
  min-width: 0;
  overflow: hidden;
  white-space: normal;
  word-break: break-all;
}
.cell-time { width: 160px; }
.cell-result { width: 150px; }
.cell-button { width: 120px; justify-content: center; }

/* 使用 el-tag 展示状态，无需额外文字着色规则 */

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #999;
}

.pagination-container {
  display: flex;
  justify-content: center;
  padding: 20px 0;
  border-top: 1px solid #f0f0f0;
  background-color: #fafafa;
}

.pagination {
  --el-pagination-bg-color: white;
  --el-pagination-text-color: #606266;
  --el-pagination-border-radius: 4px;
}

.right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: white;
  overflow: hidden;
}

.right-panel::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
}

.pipeline-status {
  height: 200px;
  border-bottom: 1px solid #e6e6e6;
  margin-bottom: 10px;
}

.record-info {
  display: flex;
  gap: 10px;
  font-size: 15px;
  color: #666;
}

.task-id {
  color: #2196F3;
  font-weight: 500;
}

.pipeline-container {
  margin-left: 21px;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  background-color: white;
  overflow: hidden;
  position: relative;
}

.pipeline-container::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
}

.pipeline-flow {
  display: flex;
  align-items: center;
  padding: 20px 20px 10px 20px;
  overflow-x: auto;
  overflow-y: hidden;
  min-height: 100px;
  gap: 15px;
  scroll-behavior: smooth;
}

/* 完全隐藏原生滚动条 */
.pipeline-flow::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
}

.pipeline-flow {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

/* 流水线自定义滚动条 */
.pipeline-scrollbar {
  margin: 5px 20px 20px 20px;
  height: 6px;
  background-color: #e4e7ed;
  border-radius: 3px;
  cursor: pointer;
  position: relative;
}

.scrollbar-thumb {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background-color: #c0c4cc;
  border-radius: 3px;
  cursor: grab;
  transition: background-color 0.15s ease-out;
}

.scrollbar-thumb:hover {
  background-color: #909399;
}

.scrollbar-thumb:active {
  cursor: grabbing;
  background-color: #606266;
}


.flow-node {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  min-width: 100px;
  height: 60px;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
  background-color: white;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  font-size: 14px;
  font-weight: 500;
}

.start-node, .end-node {
  min-width: 80px;
  height: 50px;
  border-radius: 25px;
  background-color: #67c23a;
  border-color: #67c23a;
  color: white;
}

.stage-node {
  min-width: 120px;
  height: 60px;
  border-color: #e4e7ed;
  color: #606266;
  padding: 0 12px;
  text-align: center;
}

.stage-node.status-pending {
  border-color: #e4e7ed;
  background-color: #ffffff;
  color: #909399;
}

.stage-node.status-running {
  border-color: #409eff;
  background-color: #ecf5ff;
  color: #409eff;
  animation: pulse 2s infinite;
}

.stage-node.status-completed {
  border-color: #67c23a;
  background-color: #f0f9ff;
  color: #67c23a;
}

.stage-node.status-failed {
  border-color: #f56c6c;
  background-color: #fef0f0;
  color: #f56c6c;
}

.flow-arrow {
  width: 40px;
  height: 2px;
  background-color: #c0c4cc;
  position: relative;
  flex-shrink: 0;
  margin: 0;
}

.flow-arrow::after {
  content: '';
  position: absolute;
  right: -8px;
  top: -6px;
  width: 0;
  height: 0;
  border-left: 12px solid #c0c4cc;
  border-top: 7px solid transparent;
  border-bottom: 7px solid transparent;
}

.node-content {
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  padding: 0 8px;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(64, 158, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0);
  }
}

.no-stages {
  text-align: center;
  color: #999;
  padding: 20px;
}

.logs-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 10px;
  margin-left: 10px;
  margin-right: 10px;
}

.logs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.logs-header h4 {
  margin: 0;
  color: #333;
}

.log-controls {
  display: flex;
  gap: 10px;
  align-items: center;
}

.logs-content {
  flex: 1;
  color: #d4d4d4;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.4;
  padding: 15px;
  border-radius: 4px;
  overflow-y: auto;
  min-height: 200px;
}

.logs-content::-webkit-scrollbar {
  width: 6px;
}

.logs-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.logs-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.logs-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.log-lines {
  height: 100%;
}

.log-line {
  padding: 2px 0;
  white-space: pre-wrap;
  word-break: break-all;
}

.logs-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #888;
}
/* 全局隐藏横向滚动条 */
* {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

*::-webkit-scrollbar:horizontal {
  display: none;
  height: 0;
}

body, html {
  overflow-x: hidden;
}

/* 操作日志样式（从workflow迁移） */
.operation-logs-section {
  flex: 0 0 925px; /* 增加固定高度 */
  margin-bottom: 10px;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.operation-logs-section .log-header {
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  border-bottom: 1px solid #f0f0f0;
  background-color: #f8f9fa;
}

.operation-logs-section .log-container {
  flex: 1;
  overflow-y: auto;
  max-height: 900px;
  padding: 8px;
  font-size: 14px;
  background-color: #fafafa;
}

.operation-logs-section .log-item {
  padding: 2px 8px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  line-height: 1.4;
  white-space: pre-wrap;
  word-break: break-all;
  overflow-wrap: break-word;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 1px;
  color: #374151;
}

.operation-logs-section .log-item:last-child {
  border-bottom: none;
}

.operation-logs-section .logs-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #999;
  font-style: italic;
}

.operation-logs-section .log-container {
  flex: 1;
  overflow-y: auto;
  max-height: 900px;
  padding: 8px;
  font-size: 14px;
  background-color: #fafafa;
  
  /* 显示滚动条 */
  scrollbar-width: thin; /* Firefox */
  scrollbar-color: #c1c1c1 #f1f1f1; /* Firefox */
}

/* Webkit浏览器（Chrome, Safari）的滚动条样式 */
.operation-logs-section .log-container::-webkit-scrollbar {
  width: 8px;
}

.operation-logs-section .log-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.operation-logs-section .log-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.operation-logs-section .log-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
