<template>
    <el-divider />
    <div class="add-container">
        <el-form :model="form" label-width="auto" :rules="rules" status-icon ref="formRef">

            <el-form-item label="项目名称" prop="name">
                <el-input v-model="form.name"></el-input>
            </el-form-item>

            <el-form-item label="项目编号" prop="number">
                <el-input v-model="form.number"></el-input>
            </el-form-item>

            <el-form-item label="消息推送人员" prop="related_people">
                <Organizaiton v-model="form.related_people" :multiple="true" :cache-data="cacheData"
                    ref="relatedPeopleRef" />
            </el-form-item>

            <el-form-item label="消息推送-开始时间">
                <el-time-picker v-model="form.msg_effective_time_start" :default-value="new Date(0, 0, 0, 9, 0, 0)"
                    placeholder="消息推送-开始时间" />
            </el-form-item>

            <el-form-item label="消息推送-结束时间">
                <el-time-picker v-model="form.msg_effective_time_end" :default-value="new Date(0, 0, 0, 22, 0, 0)"
                    placeholder="消息推送-结束时间" />
            </el-form-item>


            <div class="submit-button-container">
                <el-button type="default" @click="onCancel">取消</el-button>
                <el-button type="primary" @click="onAffirm">提交</el-button>
            </div>

        </el-form>
    </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';

import http from '@/utils/http/http.js';

import dayjs from 'dayjs';

import Organizaiton from '@/components/Organization/index.vue';

const props = defineProps({
    r_id: {
        type: Number,
        required: true,
    },
});

const emit = defineEmits(['affirm', 'cancel'])

const formRef = ref(null);

const cacheData = ref([]);

const relatedPeopleRef = ref(null);

const form = ref({
    name: '',
    number: '',
    related_people: [],
    msg_effective_time_start: '',
    msg_effective_time_end: '',
});

const rules = ref({
    name: [
        { required: true, message: '请输入项目名称', trigger: 'blur' },
    ],
    number: [
        { required: true, message: '请输入项目编号', trigger: 'blur' },
    ],
    related_people: [
        { required: true, message: '请选择消息推送人员', trigger: 'blur' },
    ],
});

const onAffirm = () => {
    let data = {};
    Object.assign(data, form.value);
    if (data.msg_effective_time_start) {
        data.msg_effective_time_start = dayjs(data.msg_effective_time_start).format('HH:mm:ss');
    } else {
        delete data.msg_effective_time_start;
    };
    if (data.msg_effective_time_end) {
        data.msg_effective_time_end = dayjs(data.msg_effective_time_end).format('HH:mm:ss');
    } else {
        delete data.msg_effective_time_end;
    };
    let relatedPeople = form.value.related_people.map(item => {
        item = relatedPeopleRef.value.getNode(item);
        return {
            name: item?.data?.label,
            email: item?.data?.value,
        }
    });
    data.related_people = JSON.stringify(relatedPeople);
    formRef.value.validate(async (valid) => {
        if (valid) {
            http.put(`/projects/${props.r_id}`, data).then(res => {
                emit('affirm');
            }).catch(err => {
                ElMessageBox.alert(err.response.data.msg, '编辑失败', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'error',
                })
            });
        };
    });
};

function onCancel() {
    emit('cancel');
};

onMounted(() => {
    if (props.r_id) {
        http.get(`/projects/${props.r_id}`).then(res => {
            form.value.name = res.data.data.name;
            form.value.number = res.data.data.number;
            if (form.value.msg_effective_time_start) {
                form.value.msg_effective_time_start = dayjs(res.data.data.msg_effective_time_start, "HH:mm:ss").toDate();
            };
            if (form.value.msg_effective_time_end) {
                form.value.msg_effective_time_end = dayjs(res.data.data.msg_effective_time_end, "HH:mm:ss").toDate();
            };
            let related_people = JSON.parse(res.data.data.related_people);
            cacheData.value = related_people.map(item => {
                return {
                    label: item.name,
                    value: item.email,
                };
            });
            form.value.related_people = related_people.map(item => {
                return item.email;
            });
        });
    };
});

</script>


<style lang="scss" scoped>
.add-container {
    padding: 0 20px;
}

.submit-button-container {
    display: flex;
    justify-content: flex-end;
}
</style>