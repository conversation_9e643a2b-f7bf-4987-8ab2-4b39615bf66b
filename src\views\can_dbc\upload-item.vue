<template>

    <el-upload ref="uploadRef" :auto-upload="false" :limit="1" :file-list="fileList" :before-upload="beforeUpload"
        :on-change="handleFileChange" :on-remove="handleFileRemove" :on-exceed="handleExceed" accept=".dbc" style="width: 100%;">
        <el-button type="primary">点击上传</el-button>
        <template #tip>
            <div class="el-upload__tip">
                只能上传一个文件(.dbc)。
            </div>
        </template>
        
    </el-upload>

</template>

<script setup>
import { ref } from "vue";

const model = defineModel();

const uploadRef = ref(null);
const fileList = ref([]);

const beforeUpload = (file) => { };

const handleFileChange = (file, fileList) => {
    if (file.status === 'ready') {
        // 新文件覆盖旧文件
        if (fileList.length > 1) {
            // 移除旧文件，只保留最新的文件
            fileList.value = [file];
        } else {
            fileList.value = fileList;
        }

        // 保存当前文件引用
        model.value = file.raw;
    }
};

const handleFileRemove = (file, fileList) => {
    // 清空当前文件引用
    model.value = null;
};

const handleExceed = (files, fileList) => {
    uploadRef.value?.clearFiles();
    const file = files[0];
    uploadRef.value?.handleStart(file);
};

</script>


<style lang="scss" scope>

</style>