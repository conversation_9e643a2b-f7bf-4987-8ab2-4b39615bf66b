<template>
    <el-affix :offset="60">
        <div class="top-tool-container">

            <span style="font-size: 21px; font-weight: bolder;">
                <span v-if="op_type == 'add'">新建测试用例</span>
                <span v-else-if="op_type == 'edit'">编辑测试用例</span>
                <span v-else-if="op_type == 'p_add'">新建公共用例</span>
                <span v-else-if="op_type == 'p_edit'">编辑公共用例</span>
                <span v-else>新建测试用例</span>
            </span>

            <div class="submit-button-container">
                <el-button type="default" @click="router.back" plain>返回</el-button>
                <el-button type="default" @click="handleRequirement" plain v-if="requirement.id">要关联的需求</el-button>
                <el-button type="default" @click="onExportToPublic" plain
                    v-if="op_type == 'add' || op_type == 'edit'">导出到公共用例</el-button>
                <el-button type="default" @click="onImportFromPublic" plain
                    v-if="op_type == 'add' || op_type == 'edit'">从公共用例导入</el-button>
                <!-- <el-button type="primary" @click="onSave(true)" plain
                    v-if="op_type == 'add' || op_type == 'edit' || op_type == 'copy'">保存并发布</el-button> -->
                <el-button type="primary" @click="onSave()">保存</el-button>
            </div>
        </div>
    </el-affix>

    <div style="display: flex;flex-direction: row;">
        <div style="flex: 1;">
            <el-form :model="form" label-width="auto" :rules="rules" status-icon ref="formRef">

                <el-collapse v-model="activeNames">
                    <el-collapse-item title="基本信息" name="1" color="#303133">

                        <div class="cp-item-container">
                            <el-row :gutter="20" style="max-width: 1400px;">
                                <el-col :span="10">
                                    <el-form-item label="用例名称" prop="name">
                                        <el-input v-model="form.name" placeholder="请输入用例名称"></el-input>
                                    </el-form-item>
                                    <el-form-item label="前置条件" prop="preconditions">
                                        <el-input type="textarea" :rows="3" v-model="form.preconditions"
                                            placeholder="请输入前置条件" resize="none"></el-input>
                                    </el-form-item>

                                    <el-form-item label="测试步骤" prop="steps">
                                        <el-input type="textarea" :rows="3" v-model="form.steps" placeholder="请输入测试步骤"
                                            resize="none"></el-input>
                                    </el-form-item>

                                    <el-form-item label="期望结果" prop="expected">
                                        <el-input type="textarea" :rows="3" v-model="form.expected"
                                            placeholder="请输入期望结果" resize="none"></el-input>
                                    </el-form-item>

                                    <el-form-item label="备注">
                                        <el-input type="textarea" :rows="3" v-model="form.remark" placeholder="请输入备注"
                                            resize="none"></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="14">
                                    <el-row :gutter="10">
                                        <el-col :span="24">
                                            <el-form-item
                                                v-if="op_type == 'add' || op_type == 'edit' || op_type == 'copy'"
                                                label="所属项目" prop="project_number" ref="projectRef">
                                                <el-select v-model="form.project_number" placeholder="请选择所属项目"
                                                    filterable :disabled="op_type == 'edit'">
                                                    <el-option v-for="item in projects"
                                                        :label="`${item.name}(${item.projectCode})`"
                                                        :value="item.projectCode"></el-option>
                                                </el-select>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                    <el-row :gutter="10">
                                        <el-col :span="12">
                                            <el-form-item label="所属模块" prop="module">
                                                <el-tree-select v-model="form.module" :data="modules"
                                                    :props="{ label: 'name', value: 'm', disabled: 'deprecated' }"
                                                    ref="moduleRef" placeholder="请选择所属模块" clearable check-strictly
                                                    :render-after-expand="false" node-key="m" show-checkbox
                                                    :disabled="op_type == 'edit'">
                                                </el-tree-select>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="12">
                                            <el-form-item label="用例类型" prop="type">
                                                <el-select v-model="form.type" placeholder="请选择用例类型">
                                                    <el-option label="耐久测试" value="DURABLE_TEST"></el-option>
                                                    <el-option label="性能测试" value="PERFORMANCE_TEST"></el-option>
                                                    <el-option label="功能测试" value="FUNCTION_TEST"></el-option>
                                                    <el-option label="协议栈测试" value="PROTOCOL_STACK_TEST"></el-option>
                                                </el-select>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                    <el-row :gutter="10">
                                        <el-col :span="12">
                                            <el-form-item label="用例活动类型" prop="action_type">
                                                <el-select v-model="form.action_type" placeholder="用例活动类型"
                                                    :disabled="op_type == 'edit'">
                                                    <el-option v-for="item in action_types" :label="item.name"
                                                        :value="item.number"></el-option>
                                                </el-select>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="12">
                                            <el-form-item label="优先级" prop="priority">
                                                <el-select v-model="form.priority" placeholder="请输入优先级">
                                                    <el-option label="高" value="HIGH"></el-option>
                                                    <el-option label="中" value="MIDDLE"></el-option>
                                                    <el-option label="低" value="LOW"></el-option>
                                                </el-select>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                    <el-row :gutter="10">
                                        <el-col :span="12">
                                            <el-form-item label="用例来源" prop="source">
                                                <el-select v-model="form.source" placeholder="请输入用例来源">
                                                    <el-option label="用例库沿用" value="TASK_CHANGE"></el-option>
                                                    <el-option label="需求分析" value="TASK_AFFIRM"></el-option>
                                                    <el-option label="需求变更" value="NEW_PROJECT"></el-option>
                                                    <el-option label="横向扩展" value="HORIZONTAL_SCALING"></el-option>
                                                </el-select>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="12">
                                            <el-form-item label="用例生成方法" prop="generation_method">
                                                <el-select v-model="form.generation_method" placeholder="请选择用例生成方法">
                                                    <el-option label="边界值法" value="BOUNDARY_VALUE_METHOD"></el-option>
                                                    <el-option label="因果法" value="FRUIT_GRAPH_METHOD"></el-option>
                                                    <el-option label="判定表驱法" value="DECISION_TABLE_DRIVE"></el-option>
                                                    <el-option label="功能图法" value="FUNCTION_DIAGRAM_METHOD"></el-option>
                                                    <el-option label="场景法" value="SCENE_METHOD"></el-option>
                                                    <el-option label="等价类" value="EQUIVALENCE_CLASS"></el-option>
                                                    <el-option label="现场经验分析"
                                                        value="FIELD_EXPERIENCE_ANALYSIS"></el-option>
                                                    <el-option label="外部和内部接口分析法"
                                                        value="EXTERNAL_AND_INTERNAL_INTERFACE_ANALYSIS"></el-option>
                                                    <el-option label="流程分析法" value="PROCESS_ANALYSIS"></el-option>
                                                    <el-option label="反向分析" value="BACKWARD_ANALYSIS"></el-option>
                                                    <el-option label="正向分析" value="FORWARD_ANALYSIS"></el-option>
                                                    <el-option label="环境条件和操作用例分析"
                                                        value="ENVIRONMENTAL_CONDITIONS_AND_OPERATIONAL_USE_CASE_ANALYSIS"></el-option>
                                                    <el-option label="错误猜错法" value="MISGUESS"></el-option>
                                                    <el-option label="序列和来源的分析"
                                                        value="SEQUENCE_AND_SOURCE_ANALYSIS"></el-option>
                                                    <el-option label="相依性的常见极限条件"
                                                        value="COMMON_LIMIT_CONDITIONS_4_DEPENDENCE"></el-option>
                                                    <el-option label="用例的运行条件分析"
                                                        value="ANALYSIS_OPERATING_CONDITIONS_USE_CASES"></el-option>
                                                    <el-option label="基于需求分析" value="DEMAND_ANALYSIS"></el-option>
                                                </el-select>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>

                                    <el-row :gutter="10">
                                        <el-col :span="12">
                                            <el-form-item label="执行方式" prop="execute_mode">
                                                <el-select v-model="form.execute_mode" placeholder="请选择执行方式">
                                                    <el-option label="自动化测试" value="AUTOMATED_EXECUTION"></el-option>
                                                    <el-option label="手动测试" value="MANUAL_EXECUTION"></el-option>
                                                    <el-option label="半自动化测试"
                                                        value="SEMI_AUTOMATED_EXECUTION"></el-option>
                                                </el-select>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="12">
                                            <el-form-item label="功能安全属性">
                                                <el-select v-model="form.function_safe_attrib" placeholder="请选择功能安全属性">
                                                    <el-option label="ASIL A"
                                                        value="TEST_CASE_FUNCTIONAL_SAFETY_ATTRIBUTE_AA"></el-option>
                                                    <el-option label="ASIL D"
                                                        value="TEST_CASE_FUNCTIONAL_SAFETY_ATTRIBUTE_AD"></el-option>
                                                    <el-option label="ASIL QM(D)"
                                                        value="TEST_CASE_FUNCTIONAL_SAFETY_ATTRIBUTE_AQD"></el-option>
                                                    <el-option label="ASIL QM(C)"
                                                        value="TEST_CASE_FUNCTIONAL_SAFETY_ATTRIBUTE_AQC"></el-option>
                                                    <el-option label="N/A"
                                                        value="TEST_CASE_FUNCTIONAL_SAFETY_ATTRIBUTE_NA"></el-option>
                                                    <el-option label="ASIL QM(B)"
                                                        value="TEST_CASE_FUNCTIONAL_SAFETY_ATTRIBUTE_AQB"></el-option>
                                                    <el-option label="ASIL QM(A)"
                                                        value="TEST_CASE_FUNCTIONAL_SAFETY_ATTRIBUTE_AQA"></el-option>
                                                    <el-option label="ASIL B"
                                                        value="TEST_CASE_FUNCTIONAL_SAFETY_ATTRIBUTE_AB"></el-option>
                                                </el-select>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>

                                    <el-row :gutter="10">
                                        <el-col :span="12">
                                            <el-form-item label="测试方法" prop="test_method">
                                                <el-select v-model="form.test_method" placeholder="请输入测试方法" multiple>
                                                    <el-option label="压力测试" value="PRESSURE_TEST"></el-option>
                                                    <el-option label="资源使用情况测试"
                                                        value="RESOURCE_USAGE_TESTING"></el-option>
                                                    <el-option label="互动/沟通测试"
                                                        value="INTERACTION_COMMUNICATION_TESTING"></el-option>
                                                    <el-option label="接口一致性检查"
                                                        value="INTERFACE_CONSISTENCY_CHECK"></el-option>
                                                    <el-option label="根据现场经验进行的测试"
                                                        value="TESTS_BASED_ON_FIELD_EXPERIENCE"></el-option>
                                                    <el-option label="错误猜测测试" value="FALSE_GUESS_TEST"></el-option>
                                                    <el-option label="性能测试" value="PERFORMANCE_TEST"></el-option>
                                                    <el-option label="故障注入测试" value="FAULT_INJECTION_TEST"></el-option>
                                                    <el-option label="背靠背测试" value="BACK_TO_BACK_TESTING"></el-option>
                                                    <el-option label="基于接口测试" value="INTERFACE_TEST"></el-option>
                                                    <el-option label="基于需求测试" value="DEMAND_TEST"></el-option>
                                                </el-select>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="12">
                                            <el-form-item label="标签">
                                                <el-select v-model="form.tag" placeholder="请选择标签" multiple>
                                                    <el-option v-for="item in test_case_tags" :label="item.name"
                                                        :value="item.number"></el-option>
                                                </el-select>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>

                                    <el-row :gutter="10">
                                        <el-col :span="12">
                                            <el-form-item label="执行标准">
                                                <el-select v-model="form.es_source" placeholder="海微标准" clearable>
                                                    <el-option label="海微标准" value=""></el-option>
                                                    <el-option label="蔚来标准" value="nio"></el-option>
                                                    <el-option label="岚图标准" value="voyah"></el-option>
                                                </el-select>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="12">
                                            <el-form-item label="执行标准ID">
                                                <el-select v-model="form.es_id" placeholder="无标准ID" filterable
                                                    clearable>
                                                    <el-option label="无标准ID" value=""></el-option>
                                                    <el-option v-for="item in es_ids" :label="item.id"
                                                        :value="item.id"></el-option>
                                                </el-select>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                    <el-row :gutter="10"
                                        v-if="op_type == 'add' || op_type == 'edit' || op_type == 'copy'">
                                        <el-col :span="12">
                                            <el-form-item label="产品类型" prop="product_type_id">
                                                <el-select v-model="form.product_type_id" placeholder="请选择产品类型"
                                                    clearable>
                                                    <el-option v-for="item in product_types" :label="item.name"
                                                        :value="item.id"></el-option>
                                                </el-select>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="12">
                                            <el-form-item label="保留用例关联关系" prop="related_requirements">
                                                <el-select v-model="form.related_requirements">
                                                    <el-option label="是" value="1"></el-option>
                                                    <el-option label="否" value="0"></el-option>
                                                </el-select>
                                            </el-form-item>
                                        </el-col>

                                    </el-row>
                                    <el-form-item label="已关联需求" prop="requirement_number">
                                        <el-select v-model="form.requirement_number" placeholder="">
                                            <el-option v-for="r in relatedRequirements" :label="r.requirement_number"
                                                :value="r.requirement_id"></el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-col>

                            </el-row>
                        </div>

                    </el-collapse-item>
                    <el-collapse-item title="执行信息" name="2">

                        <div class="cp-item-container">
                            <el-row :gutter="20" style="max-width: 1400px;">
                                <el-col :span="6">
                                    <el-form-item label="用例执行次数" prop="cycle">
                                        <el-input-number v-model="form.cycle" placeholder="请输入用例执行次数" class="full-width"
                                            :min="0" :max="999999999"></el-input-number>
                                    </el-form-item>
                                </el-col>
                            </el-row>

                            <div class="tool-bar-container">
                                <el-button icon="Plus" type="primary" @click="handleAdd">新增测试步骤</el-button>
                                <el-button icon="FolderChecked" type="primary" @click="handleSaveSteps"
                                    :disabled="op_type != 'edit'">保存测试步骤</el-button>
                            </div>
                            <div class="table-container">
                                <el-table ref="stepTableRef" :data="stepData" stripe border style="width: 100%"
                                    row-key="id">
                                    <el-table-column label="序号" width="100" align="center">
                                        <template #default="{ row, $index }">
                                            {{ $index + 1 }}
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="步骤类型" width="120" align="center">
                                        <template #default="{ row }">
                                            <span v-if="row.type == 'MANUAL'">手动执行</span>
                                            <span v-if="row.type == 'CAN'">CAN报文</span>
                                            <span v-else-if="row.type == 'LIN'">LIN报文</span>
                                            <span v-else-if="row.type == 'I2C'">I2C指令</span>
                                            <span v-else-if="row.type == 'CUSTOM_CMD'">自定义指令</span>
                                        </template>
                                    </el-table-column>
                                    <el-table-column v-if="form.execute_mode != 'MANUAL_EXECUTION'" label="步骤参数"
                                        min-width="150" align="center">
                                        <template #default="{ row }">
                                            <ShowStep :type="row.type" :params="row.params" />
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="desc" label="步骤描述" min-width="150" align="center">
                                    </el-table-column>
                                    <el-table-column prop="expectation" label="期望结果" min-width="150" align="center">
                                    </el-table-column>
                                    <el-table-column label="操作" min-width="120" fixed="right" align="center"
                                        class="op-col">
                                        <template #default="{ row }">
                                            <div style="display: flex; justify-content: center;">
                                                <el-button type="primary" size="small"
                                                    @click="handleEditStep(row)">编辑</el-button>
                                                <el-button type="primary" size="small"
                                                    @click="handleAddU(row)">向上添加</el-button>
                                                <el-button type="primary" size="small"
                                                    @click="handleAddD(row)">向下添加</el-button>

                                                <el-dropdown style="margin-left: 10px;">
                                                    <el-button class="el-tooltip__trigger" type="primary"
                                                        size="small">更多</el-button>
                                                    <template #dropdown>
                                                        <el-dropdown-menu>
                                                            <el-dropdown-item><el-button type="primary" size="small"
                                                                    @click="handleCopyStep(row)">复制</el-button></el-dropdown-item>
                                                            <el-dropdown-item><el-button type="danger" size="small"
                                                                    @click="handleDelete(row)">删除</el-button></el-dropdown-item>
                                                        </el-dropdown-menu>
                                                    </template>
                                                </el-dropdown>

                                            </div>
                                        </template>
                                    </el-table-column>

                                </el-table>
                            </div>
                        </div>

                    </el-collapse-item>
                    <el-collapse-item title="关联需求" name="3" v-if="op_type == 'edit'">
                        <div style="display: flex; align-items: center; margin-bottom: 10px; justify-content: end;">
                            <el-button plain type="primary" icon="Plus"
                                @click="handleRelateRequirement">关联需求</el-button>
                        </div>
                        <el-table :data="relatedRequirements" stripe border style="width: 100%">
                            <el-table-column label="需求ID" min-width="400" align="center">
                                <template #default="{ row }">
                                    <el-link type="primary" @click="onRequirementDetail(row)" :underline="false">{{
                                        row.requirement_number }}</el-link>
                                </template>
                            </el-table-column>
                            <el-table-column label="版本" min-width="400" align="center">
                                <template #default="{ row }">
                                    {{ row.requirement_version }}
                                </template>
                            </el-table-column>
                            <el-table-column label="操作" width="100" fixed="right">
                                <template #default="{ row }">
                                    <el-button type="danger" @click="onRemove(row)" size="small">移除</el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                    </el-collapse-item>
                </el-collapse>

            </el-form>
        </div>
        <div>
        </div>
    </div>

    <el-dialog v-if="dialogAddStepVisible" v-model="dialogAddStepVisible" title="添加测试步骤" width="800"
        :close-on-click-modal="false">
        <AddStep :type="emType" :index="insertIndex" @confirm="onAddStepConfirm" @cancel="onAddStepCancel" />
    </el-dialog>

    <el-dialog v-if="dialogEditStepVisible" v-model="dialogEditStepVisible" title="编辑测试步骤" width="800"
        :close-on-click-modal="false">
        <EditStep :type="emType" :k="editStepId" :v="editStepValue" @confirm="onEditStepConfirm"
            @cancel="onEditStepCancel" />
    </el-dialog>

    <el-dialog v-if="dialogCopyStepVisible" v-model="dialogCopyStepVisible" title="添加测试步骤" width="800"
        :close-on-click-modal="false">
        <EditStep :type="emType" :k="editStepId" :v="editStepValue" @confirm="onCopyStepConfirm"
            @cancel="onCopyStepCancel" />
    </el-dialog>

    <el-dialog v-if="dialogImportCaseVisible" v-model="dialogImportCaseVisible" title="公共用例" width="1000"
        :close-on-click-modal="false">
        <PublicTestCases @selected="onImportCaseConfirm" />
    </el-dialog>

    <el-dialog v-if="dialogRelateRequirement" v-model="dialogRelateRequirement" title="关联需求" width="1000"
        :close-on-click-modal="false">
        <requirementList :relatedRequirementIds="relatedRequirementIds" :project_id="cur_project_id"
            :test_case="cur_test_case" @confirm="onRelateRequirementConfirm"
            @cancel="dialogRelateRequirement = false" />
    </el-dialog>

    <el-drawer v-model="showRequirement" :with-header="false" size="50%" :destroy-on-close="true">
        <Requirement :requirement="requirement" :requirementId="requirementId" />
    </el-drawer>

</template>

<script setup>
import { ref, onMounted, watch, provide, computed } from 'vue';
import http from '@/utils/http/http.js';
import Sortable from 'sortablejs';

import AddStep from './step/addStep.vue';
import EditStep from './step/editStep.vue';
import ShowStep from './step/showStep.vue';
import PublicTestCases from './publicTestCases.vue';
import Requirement from './requirement.vue';
import requirementList from './requirement-list.vue';

import { useRoute, useRouter } from 'vue-router';

const activeNames = ref(['1', '2', '3']);

const op_type = ref('add');
const emType = ref('');
const insertIndex = ref(-1);
const cur_project_id = ref('');
const cur_test_case = ref({});

const route = useRoute();

if (route.query.type) {
    op_type.value = route.query.type;
};

const router = useRouter();

const formRef = ref(null);

const moduleRef = ref(null);

const editStepId = ref(0);

const project_disabled = ref(false);

const editStepValue = ref({});

const stepTableRef = ref(null);

const projectRef = ref(null);

const dialogAddStepVisible = ref(false);

const dialogEditStepVisible = ref(false);

const dialogCopyStepVisible = ref(false);

const dialogImportCaseVisible = ref(false);

const dialogRelateRequirement = ref(false);

const action_types = ref([]);
const test_case_tags = ref([]);
const modules = ref([]);
const es_ids = ref([]);
const projects = ref([]);
const product_types = ref([]);
const pre_product_types = ref([]);
const requirement = ref({});
const showRequirement = ref(false);
const relatedRequirements = ref([]);
const requirementId = ref("");
const relatedRequirementIds = ref([]);

const handleRequirement = () => {
    requirementId.value = "";
    showRequirement.value = !showRequirement.value;
};

const form = ref({
    name: '',
    preconditions: '',
    steps: '',
    expected: '',
    remark: '',
    module: '',
    priority: 'HIGH',
    related_requirements: '1',
    source: 'NEW_PROJECT',
    type: '',
    action_type: '',
    generation_method: 'DEMAND_ANALYSIS',
    test_method: ["DEMAND_TEST"],
    execute_mode: 'AUTOMATED_EXECUTION',
    function_safe_attrib: '',
    version: '',
    tag: [],
    cycle: 1,
    project_number: '',
    es_source: '',
    es_id: '',
    product_type_id: '',
    public_test_case_id: null,
    requirement_number: '',
});

const project_number = computed(() => {
    return form.value.project_number;
});

provide('project_number', project_number);

const rules = ref({
    name: [
        { required: true, message: '请输入用例名称', trigger: 'blur' },
    ],
    preconditions: [
        { required: true, message: '请输入前置条件', trigger: 'blur' },
        { validator: (rule, value) => value.trim() !== '', message: '输入不能为空', trigger: 'blur' },
    ],
    module: [
        { required: true, message: '请输入所属模块', trigger: 'blur' },
    ],
    priority: [
        { required: true, message: '请输入优先级', trigger: 'blur' },
    ],
    // related_requirements: [
    //     { required: true, message: '请输入关联需求', trigger: 'blur' },
    // ],
    source: [
        { required: true, message: '请输入用例来源', trigger: 'blur' },
    ],
    type: [
        { required: true, message: '请输入用例类型', trigger: 'blur' },
    ],
    action_type: [
        { required: true, message: '请输入用例活动类型', trigger: 'blur' },
    ],
    generation_method: [
        { required: true, message: '请输入用例生成方法', trigger: 'blur' },
    ],
    test_method: [
        { required: true, message: '请输入测试方法', trigger: 'blur' },
    ],
    execute_mode: [
        { required: true, message: '请输入执行方式', trigger: 'blur' },
    ],
    cycle: [
        { required: true, message: '请输入用例执行次数', trigger: 'blur' },
    ],
    project_number: [
        { required: true, message: '请输入所属项目', trigger: 'blur' },
    ],
    product_type_id: [
        { required: true, message: '请选择产品类型', trigger: 'blur' },
    ],
    steps: [
        { required: true, message: '请输入测试步骤', trigger: 'blur' },
    ],
    expected: [
        { required: true, message: '请输入期望结果', trigger: 'blur' },
    ],
});

const stepData = ref([]);

const emit = defineEmits(['confirm', 'cancel']);

const handleAdd = () => {
    emType.value = form.value.execute_mode;
    insertIndex.value = -1;
    dialogAddStepVisible.value = true;
};

const handleAddU = (row) => {
    emType.value = form.value.execute_mode;
    insertIndex.value = stepData.value.indexOf(row);
    dialogAddStepVisible.value = true;
};

const handleAddD = (row) => {
    emType.value = form.value.execute_mode;
    insertIndex.value = stepData.value.indexOf(row) + 1;
    dialogAddStepVisible.value = true;
};

function parseModuleValue(node) {
    if (!node)
        return '';
    if (node.level == 1) {
        return node.data.number;
    } else {
        return parseModuleValue(node.parent) + ', ' + node.data.number;
    }
}

function onExportToPublic() {
    formRef.value.validate(async (valid) => {
        if (valid) {
            const p = projects.value.find(item => item.projectCode == form.value.project_number);

            let data = {
                ...form.value,
            };

            data.project_name = p?.name;
            data.project_id = p?.id;

            const node = moduleRef.value.getNode(form.value.module);
            data.module = parseModuleValue(node);

            data.test_steps = stepData.value.map(item => {
                return {
                    ...item,
                    params: JSON.stringify(item.params),
                };
            });

            http.post('/test_cases/public', data).then(res => {
                ElMessage({
                    message: '导出成功.',
                    type: 'success',
                });
                emit('confirm');
                // router.push({ path: '/test_cases2' });

            }).catch(err => {
                ElMessageBox.alert(err.response.data.msg, '提交失败', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'error',
                })
            });
        };
    });
}

const onSaveCreate = (sync = false) => {
    formRef.value.validate(async (valid) => {
        if (valid) {
            const p = projects.value.find(item => item.projectCode == form.value.project_number);

            let data = {
                ...form.value,
            };

            data.project_name = p?.name;
            data.project_id = p?.id;

            const node = moduleRef.value.getNode(form.value.module);
            data.module = parseModuleValue(node);

            data.test_steps = stepData.value.map(item => {
                return {
                    ...item,
                    params: JSON.stringify(item.params),
                };
            });

            // if (data.test_steps.length == 0) {
            //     ElMessageBox.alert('请添加测试步骤', '提交失败', {
            //         confirmButtonText: '确定',
            //         cancelButtonText: '取消',
            //         type: 'error',
            //     });
            //     return;
            // };

            if (requirement.value.id) {
                data.requirement_id = requirement.value.id;
                data.requirement_version = requirement.value.version;
                data.requirement_number = requirement.value.num;
            };

            http.post('/test_cases', data).then(res => {
                if (sync) {
                    http.post('/test_cases/sync', { id: res.data.data.id }).then(res => {
                        ElMessage({
                            message: '保存并同步成功.',
                            type: 'success',
                        });
                        emit('confirm');
                        router.push({ path: '/test_cases2' });
                    }).catch(err => {
                        ElMessageBox.alert(err.response.data.msg, '提交失败', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'error',
                        })
                    });
                } else {
                    ElMessage({
                        message: '保存成功.',
                        type: 'success',
                    });
                    emit('confirm');
                    router.push({ path: '/test_cases2' });
                };

            }).catch(err => {
                ElMessageBox.alert(err.response.data.msg, '保存失败', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'error',
                })
            });
        };
    });
};

const onSaveEdit = (sync = false) => {
    formRef.value.validate(async (valid) => {
        if (valid) {
            const p = projects.value.find(item => item.projectCode == form.value.project_number);

            let data = {
                ...form.value,
            };

            data.project_name = p?.name;
            data.project_id = p?.id;

            const node = moduleRef.value.getNode(form.value.module);
            data.module = parseModuleValue(node);

            data.test_steps = stepData.value.map(item => {
                return {
                    ...item,
                    params: JSON.stringify(item.params),
                };
            });

            // if (data.test_steps.length == 0) {
            //     ElMessageBox.alert('请添加测试步骤', '提交失败', {
            //         confirmButtonText: '确定',
            //         cancelButtonText: '取消',
            //         type: 'error',
            //     });
            //     return;
            // };

            http.put(`/test_cases/${route.query.id}`, data).then(res => {
                if (sync) {
                    http.post('/test_cases/sync', { id: route.query.id }).then(res => {
                        ElMessage({
                            message: '保存并同步成功.',
                            type: 'success',
                        });
                        emit('confirm');
                        router.push({ path: '/test_cases2' });
                    }).catch(err => {
                        ElMessageBox.alert(err.response.data.msg, '提交失败', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'error',
                        })
                    });
                } else {

                    ElMessage({
                        message: '保存成功.',
                        type: 'success',
                    });
                    emit('confirm');
                    router.push({ path: '/test_cases2' });
                };

            }).catch(err => {
                ElMessageBox.alert(err.response.data.msg, '提交失败', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'error',
                })
            });
        };
    });
};

const onSaveEditPublic = () => {
    formRef.value.validate(async (valid) => {
        if (valid) {
            const p = projects.value.find(item => item.projectCode == form.value.project_number);

            let data = {
                ...form.value,
            };

            data.project_name = p?.name;
            data.project_id = p?.id;

            const node = moduleRef.value.getNode(form.value.module);
            data.module = parseModuleValue(node);

            data.test_steps = stepData.value.map(item => {
                return {
                    ...item,
                    params: JSON.stringify(item.params),
                };
            });

            http.put(`/test_cases/public/${route.query.id}`, data).then(res => {
                ElMessage({
                    message: '保存成功.',
                    type: 'success',
                });
                emit('confirm');
                router.push({ path: '/public_test_cases' });

            }).catch(err => {
                ElMessageBox.alert(err.response.data.msg, '提交失败', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'error',
                })
            });
        };
    });
};

const onSavePCreate = () => {
    formRef.value.validate(async (valid) => {
        if (valid) {
            const p = projects.value.find(item => item.projectCode == form.value.project_number);

            let data = {
                ...form.value,
            };

            data.project_name = p?.name;
            data.project_id = p?.id;

            const node = moduleRef.value.getNode(form.value.module);
            data.module = parseModuleValue(node);

            data.test_steps = stepData.value.map(item => {
                return {
                    ...item,
                    params: JSON.stringify(item.params),
                };
            });

            http.post('/test_cases/public', data).then(res => {

                router.push({ path: '/public_test_cases' });

            }).catch(err => {
                ElMessageBox.alert(err.response.data.msg, '提交失败', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'error',
                })
            });
        };
    });

};

const onSave = (sync = false) => {
    if (op_type.value == 'edit') {
        onSaveEdit(sync);
    } else if (op_type.value == 'p_edit') {
        onSaveEditPublic(sync);
    } else if (op_type.value == 'copy') {
        onSaveCreate(sync);
    } else if (op_type.value == 'p_add') {
        onSavePCreate(sync);
    } else if (op_type.value == 'p_copy') {
        onSavePCreate(sync);
    } else {
        onSaveCreate(sync);
    }

};

const handleSaveSteps = () => {
    if (op_type.value != 'edit') {
        return;
    }

    let data = {};

    data.test_steps = stepData.value.map(item => {
        return {
            ...item,
            params: JSON.stringify(item.params),
        };
    });

    http.put(`/test_cases/${route.query.id}/update_steps`, data).then(res => {
        ElMessage({
            message: '保存成功.',
            type: 'success',
        });
    }).catch(err => {
        ElMessageBox.alert(err.response.data.msg, '提交失败', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'error',
        })
    });
};

function onImportFromPublic() {
    dialogImportCaseVisible.value = true;
}

function handleEditStep(row) {
    emType.value = form.value.execute_mode;

    editStepId.value = stepData.value.indexOf(row);

    let data = JSON.parse(JSON.stringify(row));
    editStepValue.value = data;

    dialogEditStepVisible.value = true;
};

function handleCopyStep(row) {
    emType.value = form.value.execute_mode;

    editStepId.value = stepData.value.indexOf(row);

    let data = JSON.parse(JSON.stringify(row));
    editStepValue.value = data;

    dialogCopyStepVisible.value = true;
};

function handleDelete(row) {
    ElMessageBox.confirm(
        '确定删除吗?',
        '提示',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }
    ).then(() => {
        const index = stepData.value.indexOf(row);
        stepData.value.splice(index, 1);
    }).catch(() => {
        ElMessage({
            type: 'info',
            message: '已取消删除'
        });
    });
};

function onAddStepConfirm(step, index) {
    dialogAddStepVisible.value = false;
    step.id = Date.now();
    console.log("index", index);
    if (index == -1) {
        stepData.value.push(step);
    } else {
        stepData.value.splice(index, 0, step);
    }
};

function onAddStepCancel() {
    dialogAddStepVisible.value = false;
};

function onEditStepConfirm(step) {
    stepData.value[step.k] = step;
    dialogEditStepVisible.value = false;
};

function onEditStepCancel() {
    dialogEditStepVisible.value = false;
};

function onCopyStepConfirm(step) {
    step.id = Date.now();
    stepData.value.splice(step.k + 1, 0, step);
    dialogCopyStepVisible.value = false;
};

function onCopyStepCancel() {
    dialogCopyStepVisible.value = false;
};

function onImportCaseConfirm(pc_id) {
    dialogImportCaseVisible.value = false;

    http.get(`/test_cases/public/${pc_id}`).then(res => {
        let data = res.data.data;
        form.value.name = data.name;
        form.value.preconditions = data.preconditions;
        form.value.remark = data.remark;
        form.value.module = [data.module, data.module_2level, data.module_3level].filter(item => item).join('-');
        form.value.priority = data.priority;
        // 将布尔值转换为字符串
        form.value.related_requirements = data.related_requirements ? '1' : '0';
        form.value.source = data.source;
        form.value.type = data.type;
        form.value.action_type = data.action_type;
        form.value.generation_method = data.generation_method;
        form.value.test_method = data.test_method;
        form.value.execute_mode = data.execute_mode;
        form.value.function_safe_attrib = data.function_safe_attrib;
        form.value.tag = data.tag;
        form.value.cycle = data.cycle;
        form.value.es_source = data.es_source;
        form.value.es_id = data.es_id;
        form.value.public_test_case_id = data.id;
        form.value.steps = data.steps;
        form.value.expected = data.expected;
        form.value.requirement_number = data.requirements?.[0]?.requirement_number;

        data.test_steps.forEach(item => {
            item.params = JSON.parse(item.params);
        });
        stepData.value = data.test_steps;

    }).catch(err => {
        console.log(err);
    });

};

function updateOnEditTestCase() {
    project_disabled.value = true;

    const id = route.query.id;
    http.get(`/test_cases/${id}`).then(res => {
        let data = res.data.data;
        form.value.name = data.name;
        form.value.preconditions = data.preconditions;
        form.value.remark = data.remark;
        form.value.priority = data.priority;
        // 将布尔值转换为字符串
        form.value.related_requirements = data.related_requirements ? '1' : '0';
        form.value.source = data.source;
        form.value.type = data.type;
        form.value.action_type = data.action_type;
        form.value.generation_method = data.generation_method;
        form.value.test_method = data.test_method;
        form.value.execute_mode = data.execute_mode;
        form.value.function_safe_attrib = data.function_safe_attrib;
        form.value.tag = data.tag;
        form.value.cycle = data.cycle;
        form.value.project_number = data.project_number;
        form.value.es_source = data.es_source;
        form.value.es_id = data.es_id;
        form.value.product_type_id = data.product_type_id;
        form.value.public_test_case_id = data.public_test_case_id;
        form.value.steps = data.steps;
        form.value.expected = data.expected;

        form.value.module = [data.module, data.module_2level, data.module_3level].filter(item => item).join('-');

        form.value.requirement_number = data.requirements?.[0]?.requirement_number;

        stepData.value = data.test_steps;

        moduleRef.value.setCurrentKey(form.value.module);

        relatedRequirements.value = data.requirements;
        cur_project_id.value = data.project_id;
        cur_test_case.value = data;
        relatedRequirementIds.value = data.requirements.map(item => item.requirement_id);

    }).catch(err => {
        console.log(err);
    });
};

function updateOnEditPublicTestCase() {

    const id = route.query.id;
    http.get(`/test_cases/public/${id}`).then(res => {
        let data = res.data.data;

        form.value.name = data.name;
        form.value.preconditions = data.preconditions;
        form.value.remark = data.remark;
        form.value.module = [data.module, data.module_2level, data.module_3level].filter(item => item).join('-');
        form.value.priority = data.priority;
        // 将布尔值转换为字符串
        form.value.related_requirements = data.related_requirements ? '1' : '0';
        form.value.source = data.source;
        form.value.type = data.type;
        form.value.action_type = data.action_type;
        form.value.generation_method = data.generation_method;
        form.value.test_method = data.test_method;
        form.value.execute_mode = data.execute_mode;
        form.value.function_safe_attrib = data.function_safe_attrib;
        form.value.tag = data.tag;
        form.value.cycle = data.cycle;
        form.value.project_number = data.project_number;
        form.value.es_source = data.es_source;
        form.value.es_id = data.es_id;
        form.value.steps = data.steps;
        form.value.expected = data.expected;
        form.value.requirement_number = data.requirements?.[0]?.requirement_number;

        data.test_steps.forEach(item => {
            item.params = JSON.parse(item.params);
        });
        stepData.value = data.test_steps;

    }).catch(err => {
        console.log(err);
    });
};

watch(() => form.value.es_source, (val) => {
    if (val == 'nio') {
        http.get('/es_test_cases/nio', { params: { pagesize: 100000 } }).then(res => {
            let data = res.data.data.results;
            es_ids.value = data;
        }).catch(err => {
            console.log(err);
        });
    } else if (val == 'voyah') {
        http.get('/es_test_cases/voyah', { params: { pagesize: 100000 } }).then(res => {
            let data = res.data.data.results;
            es_ids.value = data;
        }).catch(err => {
            console.log(err);
        });
    } else {
        es_ids.value = [];
    }

}, { immediate: true });

watch([() => form.value.project_number, pre_product_types], () => {

    if (!form.value.project_number) {
        product_types.value = [];
        return;
    }

    http.get(`/projects/detail/by_number`, { params: { number: form.value.project_number } }).then(res => {

        let pt = res.data.data?.configs?.product_types || [];
        product_types.value = pre_product_types.value.filter(item => pt.includes(item.id));

    });
})

watch(() => form.value.project_number, (val) => {
    if (!val) {
        if (["p_add", "p_copy", "p_edit"].includes(op_type.value)) {
            http.get('/functions').then(res => {
                let data = res.data.data.results;
                data.forEach(item => {
                    item.m = item.number;
                    if (item.children) {
                        item.children.forEach(item2 => {
                            item2.m = item.number + '-' + item2.number;
                            if (item2.children) {
                                item2.children.forEach(item3 => {
                                    item3.m = item.number + '-' + item2.number + '-' + item3.number;
                                });
                            }
                        });
                    }
                });
                modules.value = data;
            }).catch(err => {
                console.log(err);
            });
        } else {
            modules.value = [];
        }

        return;
    }

    http.get('/projects/modules', { params: { project_number: val } }).then(res => {
        let data = res.data.data.results;
        data.forEach(item => {
            item.m = item.number;
            if (item.children) {
                item.children.forEach(item2 => {
                    item2.m = item.number + '-' + item2.number;
                    if (item2.children) {
                        item2.children.forEach(item3 => {
                            item3.m = item.number + '-' + item2.number + '-' + item3.number;
                        });
                    }
                });
            }
        });
        modules.value = data;
    }).catch(err => {
        console.log(err);
    });

}, { immediate: true });

function onRequirementDetail(row) {
    requirementId.value = row.requirement_id;
    showRequirement.value = true;
};

function handleRelateRequirement() {
    dialogRelateRequirement.value = true;
};

function onRemove(row) {
    http.post("/test_cases/remove_requirements", {
        test_case_id: cur_test_case.value.id,
        test_case_version: cur_test_case.value.version,
        requirements: [{
            id: row.requirement_id,
            version: row.requirement_version,
            number: row.requirement_number,
        }],
    }).then(res => {
        updateOnEditTestCase();
    }).catch(err => {
        ElMessageBox.alert(
            err.response.data.msg,
            '移除失败',
            {
                confirmButtonText: '确定',
                type: 'warning',
            }
        )
    });
}

const onRelateRequirementConfirm = () => {
    dialogRelateRequirement.value = false;

    updateOnEditTestCase();
};

onMounted(() => {
    const sortable = new Sortable(stepTableRef.value.$el.querySelector('tbody'), {
        handle: '.el-table__row',
        animation: 150,
        onEnd: ({ newIndex, oldIndex }) => {
            const movedItem = stepData.value.splice(oldIndex, 1)[0];
            stepData.value.splice(newIndex, 0, movedItem);
        },
    });

    http.get('/test_m/test_case_types', { params: { pagesize: 1000 } }).then(res => {
        let data = res.data.data.results;
        action_types.value = data;
    }).catch(err => {
        console.log(err);
    });

    http.get('/test_case_tags', { params: { pagesize: 100000 } }).then(res => {
        let data = res.data.data.results;
        test_case_tags.value = data;
    }).catch(err => {
        console.log(err);
    });

    http.get('/product_types', { params: { pagesize: 100000 } }).then(res => {
        let data = res.data.data.results;
        pre_product_types.value = data;

    }).catch(err => {
        console.log(err);
    });

    http.get('/projects/p/all').then(res => {
        let data = res.data.data.results;
        projects.value = data;
    }).catch(err => {
        console.log(err);
    });

    route.query.project_number && (form.value.project_number = route.query.project_number);

    if (route.query.type == 'edit' && route.query.id) {
        updateOnEditTestCase();
    } else if (route.query.type == 'p_edit' && route.query.id) {
        updateOnEditPublicTestCase();
    } else if (route.query.type == 'copy' && route.query.id) {
        updateOnEditTestCase();
    } else if (route.query.type == 'p_copy' && route.query.id) {
        onImportCaseConfirm(route.query.id);
    }

    if (route.query.requirement_id) {
        http.get(`/requirements/${route.query.requirement_id}`).then(res => {
            requirement.value = res.data.data;
            console.log("requirement", requirement.value);
        }).catch(err => {
            console.log(err);
        });
    }
});

</script>


<style lang="scss" scoped>
.add-container {
    padding: 0 20px;
}

.tool-bar-container {
    margin-top: 20px;
}

.submit-button-container {}

.full-width {
    width: 100%;
}

:deep(.el-collapse-item__arrow) {
    order: -1;
    margin: 0 8px 0 0;
}

:deep(.el-collapse-item__header) {
    font-size: 16px;
}

.cp-item-container {
    padding-top: 20px;

}

.top-tool-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: white;
    height: 60px;
}

::v-deep .el-collapse-item__header {
    font-weight: bold;
    font-size: 15px;
}

:deep(.el-tooltip__trigger:focus-visible) {
    outline: unset;
}
</style>