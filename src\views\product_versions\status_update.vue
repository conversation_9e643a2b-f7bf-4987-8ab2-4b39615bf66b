<template>
    <div>

        <div class="status-update">
            <el-form label-width="auto" class="form" :rules="formRules" ref="formRef" :model="form">
                <el-form-item label="当前状态:">
                    <span>{{ statusMap[status] || status }}</span>
                    <el-input style="display: none;" />
                </el-form-item>
                <el-form-item label="目标状态:">
                    <el-select v-if="versionType === 'HARDWARE'" v-model="targetStatus" placeholder="请选择状态">
                        <template v-if="status == 'SUBMITTED'">
                            <el-option label="测试中" value="TESTING"></el-option>
                            <el-option label="已废弃" value="DEPRECATED"></el-option>
                        </template>
                        <template v-if="status == 'TESTING'">
                            <el-option label="冒烟测试通过" value="SMOKE_TEST_PASSED"></el-option>
                            <el-option label="测试不通过" value="FAILED"></el-option>
                            <el-option label="已废弃" value="DEPRECATED"></el-option>
                        </template>
                        <template v-else-if="status == 'SMOKE_TEST_PASSED' || status == 'SPECIAL_VERSION_RELEASED'">
                            <el-option label="测试通过" value="PASSED"></el-option>
                            <el-option label="测试不通过" value="FAILED"></el-option>
                            <el-option label="已废弃" value="DEPRECATED"></el-option>
                        </template>
                        <template v-else-if="status == 'PASSED'">
                            <el-option label="已发布" value="RELEASED"></el-option>
                            <el-option label="已废弃" value="DEPRECATED"></el-option>
                        </template>
                        <template v-else-if="status == 'RELEASED'">
                            <el-option label="已量产" value="PRODUCTION"></el-option>
                            <el-option label="已废弃" value="DEPRECATED"></el-option>
                        </template>
                    </el-select>
                    <el-select v-else v-model="targetStatus" placeholder="请选择状态">
                        <template v-if="status == 'TESTING'">
                            <el-option label="冒烟测试通过" value="SMOKE_TEST_PASSED"></el-option>
                            <el-option label="测试不通过" value="FAILED"></el-option>
                            <el-option label="已废弃" value="DEPRECATED"></el-option>
                        </template>
                        <template v-else-if="status == 'SMOKE_TEST_PASSED' || status == 'SPECIAL_VERSION_RELEASED'">
                            <el-option label="测试通过" value="PASSED"></el-option>
                            <el-option label="测试不通过" value="FAILED"></el-option>
                            <el-option label="已废弃" value="DEPRECATED"></el-option>
                        </template>
                        <template v-else-if="status == 'PASSED'">
                            <el-option label="已发布" value="RELEASED"></el-option>
                            <el-option label="已废弃" value="DEPRECATED"></el-option>
                        </template>
                        <template v-else-if="status == 'RELEASED'">
                            <el-option label="已量产" value="PRODUCTION"></el-option>
                            <el-option label="已废弃" value="DEPRECATED"></el-option>
                        </template>
                        <template
                            v-else-if="status == 'SUBMITTED' || status == 'TESTING' || status == 'SMOKE_TEST_PASSED' || status == 'SPECIAL_VERSION_RELEASED'">
                            <el-option label="已废弃" value="DEPRECATED"></el-option>
                        </template>
                    </el-select>
                </el-form-item>
                <!-- 备注输入框只在 targetStatus 为 DEPRECATED 时显示 -->
                <el-form-item label="备注:" prop="remark">
                    <el-input type="textarea" v-model="form.remark" placeholder="请输入备注" :rows="3"></el-input>
                </el-form-item>
                <template v-if="versionType !== 'HARDWARE'">
                    <el-form-item label="版本包地址:" v-if="targetStatus === 'RELEASED'" prop="package_url">
                        <el-input type="textarea" v-model="form.package_url"
                            placeholder="示例：jenkins_release/NLRCN10/MCU/P0402552_AE(028_007_1135)/Version"
                            :rows="3"></el-input>
                    </el-form-item>
                    <el-form-item label="版本包地址:" v-else-if="targetStatus === 'PRODUCTION'" prop="package_url">
                        <el-input type="textarea" v-model="form.package_url"
                            placeholder="示例：HW_Version_Release/NLRCN10/Latest_Version/P0402552_AE(028_007_1135)"
                            :rows="3"></el-input>
                    </el-form-item>
                    <el-form-item label="目标地址:" v-if="targetStatus === 'RELEASED'" prop="target_url">
                        <el-input type="textarea" v-model="form.target_url"
                            placeholder="示例：HW_Version_Release/NLRCN10/Latest_Version" :rows="3"></el-input>
                    </el-form-item>
                    <el-form-item label="目标地址:" v-else-if="targetStatus === 'PRODUCTION'" prop="target_url">
                        <el-input type="textarea" v-model="form.target_url"
                            placeholder="示例：HW_Production_Software_Version/NLRCN10/Latest_Version" :rows="3"></el-input>
                    </el-form-item>
                </template>
                <!-- <el-form-item label="附件地址:" v-if="versionType === 'HARDWARE' && (targetStatus === 'PASSED' || targetStatus === 'FAILED')" prop="attachment_url">
                    <el-input type="textarea" v-model="form.attachment_url"
                        placeholder="" :rows="3"></el-input>
                </el-form-item> -->
            </el-form>
        </div>

        <div style="display: flex; justify-content: end;">
            <el-button @click="onCancel">取消</el-button>
            <el-button type="primary" @click="onConfirm" :loading="loading">确认</el-button>
        </div>
    </div>

</template>


<script setup>
import http from '@/utils/http/http.js';
import { ElMessageBox } from 'element-plus';
import { reactive, ref, computed, defineProps, defineEmits, watch } from 'vue';

const props = defineProps({
    versionInfo: {
        type: Object,
        required: true,
    },
});
const loading = ref(false);
const versionType = computed(() => props.versionInfo.type || '');
const formRef = ref(null);

const statusMap = {
    'SUBMITTED': '已提测',
    'TESTING': '测试中',
    'SMOKE_TEST_PASSED': '冒烟测试通过',
    'SPECIAL_VERSION_RELEASED': '特殊版本已发布',
    'PASSED': '测试通过',
    'FAILED': '测试不通过',
    'RELEASED': '已发布',
    'PRODUCTION': '已量产',
    'DEPRECATED': '已废弃',
};

const status = computed(() => props.versionInfo.status);
const form = reactive({
    project_number: props.versionInfo.project_number || '',
    version_names: [props.versionInfo.version_name || ''], // 初始化为数组
    remark: '',
    package_url: '',
    target_url: '',
    // attachment_url: '',
});
const formRules = reactive({
    package_url: [
        { required: true, message: '版本包地址不能为空', trigger: 'blur' }
    ],
    target_url: [
        { required: true, message: '目标地址不能为空', trigger: 'blur' }
    ],
    remark: [
        { required: true, message: '备注不能为空', trigger: 'blur' }
    ],
    targetStatus: [
        { required: true, message: '目标状态不能为空', trigger: 'change' }
    ],
    // attachment_url: [
    //     { required: true, message: '附件地址不能为空', trigger: 'blur' }
    // ],
});

const targetStatus = ref('');

const emit = defineEmits(['confirm', 'cancel']);

function onCancel() {
    emit('cancel');
};

watch(targetStatus, (newVal) => {
    if (newVal !== 'RELEASED') {
        form.package_url = '';
    }
});

function submitForm() {
    loading.value = true;
    const remark_prefix = `${statusMap[targetStatus.value]}：`;
    http.post('/product_versions/status_update', {
        id: props.versionInfo.id,
        status: targetStatus.value,
        project_number: form.project_number,
        version_names: form.version_names,
        remark: remark_prefix + form.remark,
        package_url: form.package_url || "",
        target_url: form.target_url || "",
        // attachment_url: form.attachment_url || "",

    }, { timeout: 600000 }).then(res => {
        ElMessage.success('状态更新成功');
        emit('confirm');
    }).catch(err => {
        ElMessageBox.alert(err.response?.data?.msg || '请求失败', '错误', {
            confirmButtonText: '确定',
            type: 'error',
        });
    }).finally(() => {
        loading.value = false;
    });
}

function onConfirm() {
    formRef.value.validate((valid) => {
        if (valid) {
            submitForm();
        }
    });
}



</script>

<style scoped>
.status-update {
    display: flex;
    justify-content: center;
    align-items: center;
}

.form {
    padding: 20px;

    width: 100%;
}
</style>