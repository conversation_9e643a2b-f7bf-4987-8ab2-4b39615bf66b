<template>
    <el-divider />
    <div class="add-container">
        <el-form :model="form" label-width="auto" :rules="rules" status-icon ref="formRef">

            <el-form-item label="项目名称" prop="name">
                <el-input v-model="form.name" :readonly="props.name != ''"></el-input>
            </el-form-item>

            <el-form-item label="项目编号" prop="number">
                <el-input v-model="form.number" :readonly="props.number != ''"></el-input>
            </el-form-item>

            <el-form-item label="消息推送人员" prop="related_people">
                <Organizaiton v-model="form.related_people" :multiple="true" :cache-data="cacheData" ref="relatedPeopleRef" />
            </el-form-item>

            <el-form-item label="消息推送-开始时间">
                <el-time-picker v-model="form.msg_effective_time_start" :default-value="new Date(0, 0, 0, 9, 0, 0)"
                    placeholder="消息推送-开始时间" />
            </el-form-item>

            <el-form-item label="消息推送-结束时间">
                <el-time-picker v-model="form.msg_effective_time_end" :default-value="new Date(0, 0, 0, 22, 0, 0)"
                    placeholder="消息推送-结束时间" />
            </el-form-item>


            <div class="submit-button-container">
                <el-button type="default" @click="onCancel">取消</el-button>
                <el-button type="primary" @click="onSubmit">提交</el-button>
            </div>

        </el-form>
    </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';

import http from '@/utils/http/http.js';

import Organizaiton from '@/components/Organization/index.vue';

import dayjs from 'dayjs';

const props = defineProps({
    name: {
        type: String,
        default: '',
    },
    number: {
        type: String,
        default: '',
    },
    related_people: {
        type: Array,
        default: [],
    },
    msg_data: {
        type: Object,
        default: null,
    },
});
const formRef = ref(null);

const cacheData = ref(props.related_people.map(item => {
    return {
        label: item.name,
        value: item.email,
        data: {
            label: item.name,
            value: item.email,
            avatar: item.avatar,
            openId: item.openId,
        }
    };
}));

const relatedPeopleRef = ref(null);

const form = ref({
    name: props.name,
    number: props.number,
    related_people: props.related_people?.map(item => item.email) || [],
    msg_effective_time_start: dayjs("09:00:00", "HH:mm:ss").toDate(),
    msg_effective_time_end: dayjs("22:00:00", "HH:mm:ss").toDate(),
});

const rules = ref({
    name: [
        { required: true, message: '请输入项目名称', trigger: 'blur' },
    ],
    number: [
        { required: true, message: '请输入项目编号', trigger: 'blur' },
    ],
    related_people: [
        { required: true, message: '请选择消息推送人员', trigger: 'blur' },
    ],
});

const emit = defineEmits(['affirm', 'cancel']);

const onSubmit = () => {
    formRef.value.validate(async (valid) => {
        if (valid) {
            let data = {};
            Object.assign(data, form.value);
            if (data.msg_effective_time_start) {
                data.msg_effective_time_start = dayjs(data.msg_effective_time_start).format('HH:mm:ss');
            } else {
                delete data.msg_effective_time_start;
            };
            if (data.msg_effective_time_end) {
                data.msg_effective_time_end = dayjs(data.msg_effective_time_end).format('HH:mm:ss');
            } else {
                delete data.msg_effective_time_end;
            };
            let relatedPeople = form.value.related_people.map(item => {
                item = relatedPeopleRef.value.getNode(item);
                return {
                    name: item.data?.label,
                    email: item.data?.value,
                    avatar: item.data?.avatar,
                    openId: item.data?.openId,
                }
            });
            data.related_people = JSON.stringify(relatedPeople);
            http.post('/projects', data).then(res => {
                ElMessage({
                    message: '提交成功.',
                    type: 'success',
                });
                emit('affirm');
            }).catch(err => {
                ElMessageBox.alert(err.response.data.msg, '提交失败', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'error',
                })
            });
        };
    });
};

function onCancel() {
    emit('cancel');
};

onMounted(() => {
    const msg_data = props.msg_data;
    if (msg_data) {
        if (msg_data.msg_effective_time_start) {
            form.value.msg_effective_time_start = dayjs(msg_data.msg_effective_time_start, "HH:mm:ss").toDate();
        };
        if (msg_data.msg_effective_time_end) {
            form.value.msg_effective_time_end = dayjs(msg_data.msg_effective_time_end, "HH:mm:ss").toDate();
        };
        let related_people = msg_data.related_people;
        cacheData.value = related_people.map(item => {
            return {
                label: item.name,
                value: item.email,
                data: {
                    label: item.name,
                    value: item.email,
                    avatar: item.avatar,
                    openId: item.openId,
                }
            };
        });
        form.value.related_people = related_people.map(item => {
            return item.email;
        });
    };
});

</script>

<style lang="scss" scoped>
.add-container {
    padding: 0 20px;
}

.submit-button-container {
    display: flex;
    justify-content: flex-end;
}
</style>