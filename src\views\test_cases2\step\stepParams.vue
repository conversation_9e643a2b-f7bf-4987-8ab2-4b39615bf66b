<template>

    <AddCanParams v-if="step_type == 'CAN'" v-model="model" ref="paramsRef" />
    <AddCanDbcParams v-else-if="step_type == 'CAN_DBC'" v-model="model" ref="paramsRef" />
    <AddI2cParams v-else-if="step_type == 'I2C'" v-model="model" ref="paramsRef" />
    <AddLinParams v-else-if="step_type == 'LIN'" v-model="model" ref="paramsRef" />
    <AddCustomCmdParams v-else-if="step_type == 'CUSTOM_CMD'" v-model="model" ref="paramsRef" />
    
</template>


<script setup>

import { ref, computed } from 'vue';

import AddCanParams from './can/add.vue';
import AddI2cParams from './i2c/add.vue';
import AddLinParams from './lin/add.vue';
import AddCustomCmdParams from './customCmd/add.vue';
import AddCanDbcParams from './can_dbc/add.vue';

const model = defineModel();

const paramsRef = ref(null);

const props = defineProps({
    type: {
        type: String,
        required: true,
    },
});

const step_type = computed(() => {
    return props.type;
});

function validate(callback) {
    paramsRef.value.validate(callback);
}

defineExpose({
    validate,
});

</script>


<style lang="scss" scoped>
.full-width {
    width: 100%;
}

.half-width {
    width: 50%;
}
</style>