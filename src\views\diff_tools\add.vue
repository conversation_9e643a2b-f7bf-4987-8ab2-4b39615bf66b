<template>
    <el-divider />
    <div class="add-container">
        <el-form :model="form" label-width="auto" :rules="rules" status-icon ref="formRef">

            <el-form-item label="项目" prop="project_number">
                <ProjectsComponent ref="projectsRef" v-model="form.project_number" :includePrefix="false"
                    :includeAll="false" />
            </el-form-item>

            <el-form-item label="输入文件类型" prop="input_file_type">
                <el-select v-model="form.input_file_type" placeholder="请选择输入文件类型">
                    <el-option label="bin" value="bin"></el-option>
                    <el-option label="hex" value="hex"></el-option>
                    <el-option label="zip" value="zip"></el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="基准软件包版本号" prop="old_package_version">
                <el-input v-model="form.old_package_version" placeholder="请输入基准软件包版本号"></el-input>
            </el-form-item>
            <el-form-item label="基准软件包" prop="old_package">
                <el-upload class="upload-demo" action="" :auto-upload="false" :file-list="oldPackageList"
                    :on-change="handleFileChange('old_package')" :before-upload="beforeUpload" accept=".zip, .bin, .hex">
                    <el-button slot="trigger" type="primary" plain>选择基准软件包</el-button>
                    <div slot="tip" class="el-upload__tip">只能上传一个文件(.zip, .bin, .hex)</div>
                </el-upload>
                <el-input style="display: none;"></el-input>
            </el-form-item>

            <el-form-item label="目标软件包版本号" prop="new_package_version">
                <el-input v-model="form.new_package_version" placeholder="请输入目标软件包版本号"></el-input>
            </el-form-item>
            <el-form-item label="目标软件包" prop="new_package">
                <el-upload class="upload-demo" action="" :auto-upload="false" :file-list="newPackageList"
                    :on-change="handleFileChange('new_package')" :before-upload="beforeUpload" accept=".zip, .bin, .hex">
                    <el-button slot="trigger" type="primary" plain>选择目标软件包</el-button>
                    <div slot="tip" class="el-upload__tip">只能上传一个文件(.zip, .bin, .hex)</div>
                </el-upload>
                <el-input style="display: none;"></el-input>
            </el-form-item>

            <el-form-item label="输出文件名" prop="diff_package_name">
                <el-input v-model="form.diff_package_name" placeholder="请输入输出文件名"></el-input>
            </el-form-item>

            <el-form-item label="版本号" prop="diff_package_version">
                <el-input v-model="form.diff_package_version" placeholder="请输入版本号"></el-input>
            </el-form-item>

            <div class="submit-button-container">
                <el-button type="default" @click="onCancel">取消</el-button>
                <el-button type="primary" @click="onSubmit" :loading="loading">差分</el-button>
            </div>

        </el-form>
    </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import ProjectsComponent from '@/components/projects.vue';
import http from '@/utils/http/http.js';

const projectsRef = ref(null);
const formRef = ref(null);
const loading = ref(false);
const form = ref({
    project_number: 'ILTCF07',
    input_file_type: '',
    old_package_version: '',
    old_package: null,
    new_package_version: '',
    new_package: null,
    diff_package_name: '',
    diff_package_version: '',
});
const oldPackageList = ref([]);
const newPackageList = ref([]);

const rules = ref({
    project_number: [{ required: true, message: '请选择项目', trigger: 'change' }],
    input_file_type: [{ required: true, message: '请选择输入文件类型', trigger: 'change' }],
    old_package: [{ required: true, message: '请选择旧文件', trigger: 'change' }],
    new_package: [{ required: true, message: '请选择新文件', trigger: 'change' }],
    diff_package_name: [{ required: true, message: '请输入输出文件名', trigger: 'change' }],
    diff_package_version: [{ required: true, message: '请输入版本号', trigger: 'change' }],
    old_package_version: [{ required: true, message: '请输入旧文件版本号', trigger: 'change' }],
    new_package_version: [{ required: true, message: '请输入新文件版本号', trigger: 'change' }],
});

const handleFileChange = (field) => (file) => {
    form.value[field] = file.raw;
    if (field === 'old_package') {
        oldPackageList.value = [file];
    } else if (field === 'new_package') {
        newPackageList.value = [file];
    }
};

const beforeUpload = (file) => {
    return false; // 阻止自动上传
};


const onSubmit = () => {
    formRef.value.validate(async (valid) => {
        if (valid) {
            const formData = new FormData();
            let projectInfo = projectsRef.value.getProjectInfo(form.value.project_number);
            formData.append('project_name', projectInfo.name);
            formData.append('project_number', form.value.project_number);
            formData.append('input_file_type', form.value.input_file_type);
            formData.append('old_package_version', form.value.old_package_version);
            formData.append('old_package', form.value.old_package);
            formData.append('new_package_version', form.value.new_package_version);
            formData.append('new_package', form.value.new_package);
            formData.append('diff_package_name', form.value.diff_package_name);
            formData.append('diff_package_version', form.value.diff_package_version);
            loading.value = true;
            http.post('/diff_tool/records', formData, {
                headers: {
                    'Content-Type': 'multipart/form-data'
                },
                timeout: 120000,
            }).then(res => {
                ElMessage({
                    message: '添加成功.',
                    type: 'success',
                });
                emit('confirm');
            }).catch(err => {
                ElMessageBox.alert(err.response.data.msg, '提交失败', {
                    confirmButtonText: '确定',
                    type: 'error',
                })
            }).finally(() => {
                loading.value = false;
            });
        };
    });
};

const emit = defineEmits(['confirm', 'cancel']);

function onCancel() {
    emit('cancel');
};


onMounted(() => {

});


</script>


<style lang="scss" scoped>
.add-container {
    padding: 0 20px;
}

.submit-button-container {
    display: flex;
    justify-content: flex-end;
}

.upload-demo .el-upload__tip {
    margin-left: 10px;
}
</style>