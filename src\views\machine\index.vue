<template>
<div style="display: flex; flex-direction: column; height: calc(104.9vh - 322px);">
    <div class="tool-bar-container" style="display: flex;">
        <el-button icon="Plus" type="primary" @click="handleAdd">新增</el-button>

        <div style="margin-left: auto; display: flex; gap: 10px;">
            <el-tooltip class="box-item" effect="dark" content="重置已添加的筛选条件" placement="top-start">
                <el-button text bg @click="handleReset">重置</el-button>
            </el-tooltip>
            <filterButton @click="onFilterStatusChange" :count="filterCount" />
            <el-button icon="Refresh" text bg @click="handleRefresh">刷新</el-button>
        </div>
    </div>

    <div class="filter-container" v-if="showFilterContainer">
        <el-input v-model="form.name_re" placeholder="请输入名称" @keyup.enter="onFilter" clearable>
            <template #append>
                <el-button icon="Search" @click="onFilter"></el-button>
            </template>
        </el-input>
        <el-input v-model="form.number_re" placeholder="请输入机台编号" @keyup.enter="onFilter" clearable>
            <template #append>
                <el-button icon="Search" @click="onFilter"></el-button>
            </template>
        </el-input>
    </div>

    <el-table :data="tableData" stripe border style="width: 100%;flex: 1;" class="table-container">

        <!-- 名称列 -->
        <el-table-column prop="name" label="机台名称" min-width="200" align="center"></el-table-column>

        <!-- 机台编号列 -->
        <el-table-column prop="m_number" label="机台编号" min-width="150" align="center"></el-table-column>

        <!-- 资产编号列 -->
        <el-table-column prop="number" label="资产编号" min-width="250" align="center"></el-table-column>

        <!-- 位置列 -->
        <el-table-column prop="position" label="位置" min-width="200" align="center"></el-table-column>

        <!-- 维护人列 -->
        <el-table-column prop="username" label="维护人" min-width="100" align="center"></el-table-column>

        <!-- 多项目共用列 -->
        <el-table-column label="多项目共用" min-width="100" align="center">
            <template #default="{ row }">
                <el-tag v-if="row.is_share" type="success">是</el-tag>
                <el-tag v-else type="danger">否</el-tag>
            </template>
        </el-table-column>

        <!-- 操作列 -->
        <el-table-column label="操作" min-width="280" fixed="right" align="center">
            <template #default="{ row }">
                <div style="display: flex; justify-content: center; gap: 10px;">
                    <el-button type="primary" size="small" @click="handleDetail(row)">详情</el-button>
                    <el-button type="primary" size="small" @click="handleReserve(row)">预约</el-button>
                    <el-button type="primary" size="small" @click="handleEdit(row)">编辑</el-button>
                    <el-button type="danger" size="small" @click="handleDelete(row)">删除</el-button>
                </div>
            </template>
        </el-table-column>

    </el-table>
</div>

    <div class="pagination-container">
        <el-pagination :page-sizes="[1, 10, 15, 20, 25, 50, 100]" layout="prev, pager, next, jumper, total, sizes"
            :total="total" background @change="onPageChange" v-model:current-page="form.page"
            v-model:page-size="form.pagesize" />
    </div>

    <el-dialog v-if="dialogAddVisible" v-model="dialogAddVisible" title="添加机台" width="800"
        :close-on-click-modal="false">
        <Add @affirm="onAddAffirm" @cancel="onAddCancel" />
    </el-dialog>

    <el-dialog v-if="dialogEditVisible" v-model="dialogEditVisible" title="编辑机台" width="800"
        :close-on-click-modal="false">
        <Edit @affirm="onEditAffirm" @cancel="onEditCancel" :r_id="r_id" />
    </el-dialog>

    <el-dialog v-if="dialogReserveVisible" v-model="dialogReserveVisible" title="预约机台" width="1000"
        :close-on-click-modal="false">
        <Reserve @confirm="onReserveConfirm" @cancel="onReserveCancel" :r_id="r_id" />
    </el-dialog>

</template>


<script setup>

import { ref, reactive, onMounted } from 'vue';
import http from '@/utils/http/http.js';
import Add from './add.vue';
import Edit from './edit.vue';
import Reserve from './reserve.vue';
import { useRouter } from 'vue-router';
import { useAccessStat } from '@/utils/accessStat';

useAccessStat('/machines/list', '测试机台列表');

const router = useRouter();
const filterCount = ref(0);
const tableData = ref([]);

let r_id = ref(0);

const dialogAddVisible = ref(false);

const dialogEditVisible = ref(false);

const dialogReserveVisible = ref(false);

let form = reactive({
    page: 1,
    pagesize: 20,
});

let total = ref(0);
let showFilterContainer = ref(false);

function onFilterStatusChange() {
    showFilterContainer.value = !showFilterContainer.value;
};

function update_table() {
    http.get('/machines', { params: form}).then(res => {
        tableData.value = res.data.data.results;
        total.value = res.data.data.count;
    });

    filterCount.value = Object.keys(form).filter(key => !['page', 'pagesize'].includes(key)).reduce((count, key) => {
        if (form[key] == '' || form[key] == undefined || form[key] == null || form[key].length == 0) {
            return count;
        } else {
            return count + 1;
        }
    }, 0)
};

function handleReset() {
    form = reactive({
        page: 1,
        pagesize: 10
    });
    update_table();
};

function onPageChange() {
    update_table();
};

function onFilter() {
    form.page = 1;
    form.pagesize = 10;
    update_table();
};

function handleDetail(row) {
    router.push({ path: `/machines/${row.id}` });
};

function handleAdd() {
    dialogAddVisible.value = true;
};

function handleEdit(row) {
    r_id.value = row.id;
    dialogEditVisible.value = true;
};

function handleReserve(row) {
    r_id.value = row.id;
    dialogReserveVisible.value = true;
};

function handleDelete(row) {
    ElMessageBox.confirm(
        '确定删除吗?',
        '提示',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }
    ).then(() => {
        http.delete(`/machines/${row.id}`).then(res => {
            ElMessage({
                message: '删除成功.',
                type: 'success',
            });
            update_table();
        }).catch(err => {
            ElMessage({
                type: 'error',
                message: err.response.data.msg
            });
        });
    }).catch(() => {
        ElMessage({
            type: 'info',
            message: '已取消删除'
        });
    });
};

function onAddAffirm() {
    dialogAddVisible.value = false;
    update_table();
};

function onAddCancel() {
    dialogAddVisible.value = false;
};

function onEditAffirm() {
    dialogEditVisible.value = false;
    update_table();
};

function onEditCancel() {
    dialogEditVisible.value = false;
};

function onReserveConfirm() {
    dialogReserveVisible.value = false;
};

function onReserveCancel() {
    dialogReserveVisible.value = false;
};

function handleRefresh() {
    update_table();
};

onMounted(() => {
    update_table();
});

</script>


<style scoped>
.search-container {
    width: 100%;
    display: flex;
    justify-content: center;
    justify-items: center;
    height: 40px;
}

.search-input {
    width: 50%;
    max-width: 500px;
}

.search-button {
    border: 1px solid #dcdfe6;
}

.filter-container {
    width: 100%;
    display: flex;
    justify-content: flex-start;

    margin-bottom: 10px;

    .el-input {
        width: 50%;
        max-width: 300px;
        margin-right: 10px;
    }
}

.pagination-container {
  margin-top: auto; /* 自动顶到底部 */
  align-self: flex-start; /* 左对齐 */
  padding-top: 15px;
  width: 100%;
}
</style>