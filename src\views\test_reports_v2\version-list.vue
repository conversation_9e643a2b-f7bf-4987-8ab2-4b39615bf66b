<template>
    <div class="version-list">
        <!-- 添加按钮 -->
        <div class="list-header">
            <el-button type="primary" size="small" @click="addVersion">
                <el-icon>
                    <Plus />
                </el-icon>
                添加版本
            </el-button>
            <span class="count-info">共 {{ model.length }} 项</span>
        </div>

        <!-- 版本列表 -->
        <div class="version-items">
            <div v-for="(version, index) in model" :key="version.id" class="version-item">
                <div class="item-header">
                    <span>版本 {{ index + 1 }}</span>
                    <el-button type="danger" size="small" text @click="removeVersion(index)"
                        :disabled="model.length === 1">
                        <el-icon>
                            <Delete />
                        </el-icon>
                    </el-button>
                </div>
                <VersionItem v-model="model[index]" />
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue';
import { Plus, Delete } from '@element-plus/icons-vue';
import VersionItem from './version-item.vue';

const model = defineModel({
    type: Array,
    default: () => []
});

// 生成唯一ID
const generateId = () => Date.now() + Math.random().toString(36).substr(2, 9);

// 创建新版本
const createVersion = () => ({
    id: generateId(),
    soft_type: '',
    soft_version: '',
    soft_version_info: '',
    is_update: '',
    soft_path: '',
    soft_revision: '',
    soft_risk: ''
});

// 添加版本
const addVersion = () => {
    model.value.push(createVersion());
};

// 删除版本
const removeVersion = (index) => {
    if (model.value.length > 1) {
        model.value.splice(index, 1);
    }
};

onMounted(() => {
    // 初始化时如果没有版本，则添加一个默认版本
    if (model.value.length === 0) {
        addVersion();
    }
});

</script>

<style lang="scss" scoped>
.version-list {
    .list-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;

        .count-info {
            color: #666;
            font-size: 14px;
        }
    }

    .version-items {
        .version-item {
            border: 1px solid #e4e7ed;
            border-radius: 4px;
            padding: 12px;
            margin-bottom: 12px;

            .item-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 8px;
                font-weight: 500;
            }
        }
    }
}
</style>