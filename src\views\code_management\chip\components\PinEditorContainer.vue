<template>
    <div class="pin-editor-panel" :class="{ 'collapsed': isCollapsed }">
      <!-- 面板内容 -->
      <div class="panel-content" v-show="!isCollapsed">
        <PinEditor
          ref="pinEditorRef"
          :pin-data="pinData"
          :pin-info="pinInfo"
          :key="pinData.pinId"
          :disabled="false"
          :projectCode="projectCode"
          :projectName="projectName"
          :gitlab="gitlab"
          :projectBranch="projectBranch"
          :branchStatus="branchStatus"
          :chipName="chipName"
          @field-change="handleFieldChange"
          @module-change="handleModuleChange"
          @module-config-change="handleModuleConfigChange"
        />
      </div>
    </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, onMounted, watch } from 'vue';
import { EditPen, ArrowDown } from '@element-plus/icons-vue';
import PinEditor from '@/views/code_management/chip/components/DynamicPinEditor.vue';

// 定义 props
const props = defineProps({
  pinData: {
    type: Object,
    required: true,
    default: () => ({
      pinId: '',
      pinName: '',
      pinType: '',
      desc: '',
      status: '可用',
      electricalType: 'TTL',
      dynamicConfig: {},
      functionList: []
    })
  },
  availableIoTypes: {
    type: Array,
    required: true,
    default: () => []
  },
  repositoryInfo: {
    type: Object,
    required: true,
    default: () => ({
      gitlab: '',
      project_branch: '',
      workspace: '',
      branch_status: ''
    })
  },
  pinInfo: {
    type: Array,
    default: () => []
  },
  collapsed: {
    type: Boolean,
    default: false
  },
  showHeader: {
    type: Boolean,
    default: false
  },
  // 项目信息（用于请求）
  projectCode: {
    type: String,
    default: ''
  },
  projectName: {
    type: String,
    default: ''
  },
  gitlab: {
    type: String,
    default: ''
  },
  projectBranch: {
    type: String,
    default: ''
  },
  branchStatus: {
    type: String,
    default: ''
  },
  chipName: {
    type: String,
    default: ''
  }
});

// 定义 emits
const emit = defineEmits([
  'update:collapsed',
  'pin-save',
  'pin-reset',
  'pin-clear',
  'pin-change',
  'module-change',
  'module-config-change',
  'field-change'
]);

// 本地状态
const isCollapsed = ref(props.collapsed);
const pinEditorRef = ref(null);

// 切换折叠状态
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value;
  emit('update:collapsed', isCollapsed.value);
};

// 事件处理函数
const handlePinSave = (pinData) => {
  emit('pin-save', pinData);
};

const handlePinReset = (pinData) => {
  emit('pin-reset', pinData);
};

const handlePinClear = (pinData) => {
  emit('pin-clear', pinData);
};

const handlePinChange = (pinData) => {
  emit('pin-change', pinData);
};

const handleModuleChange = (moduleData) => {
  console.log('PinEditorContainer - 功能类型变化:', moduleData);
  emit('module-change', moduleData);
};

// 处理请求模块配置
const handleFieldChange = (fieldData) => {
  console.log('PinEditorContainer - 收到字段变化事件:', fieldData);
  // 直接传递对象格式的数据给父组件
  emit('field-change', fieldData);
};



const handleModuleConfigChange = (configChangeData) => {
  console.log('PinEditorContainer - 模块配置变化:', configChangeData);
  emit('module-config-change', configChangeData);
};

// onMounted(() => {
//   console.log('moduleData:', moduleData);
// }); 


// 暴露方法给父组件
defineExpose({
  pinEditorRef,
  toggleCollapse
});
</script>

<style scoped>
.pin-editor-panel {
  display: flex;
  flex-direction: column;
  position: relative;
  height: 100%;
  width: 100%;
}



.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  cursor: pointer;
  user-select: none;
  /* transition: background-color 0.2s ease; */
}


.panel-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
}

.collapse-icon {
  font-size: 16px;
  color: #666;
  transition: transform 0.3s ease;
}

.collapse-icon.rotated {
  transform: rotate(-180deg);
}

.panel-content {
  flex: 1; /* 填满剩余空间 */
  padding: 0;
  transition: all 0.3s ease;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 折叠状态下隐藏内容 */
.pin-editor-panel.collapsed .panel-content {
  max-height: 0;
  opacity: 0;
  transform: translateY(-10px);
}

/* 展开状态下显示内容 */
.pin-editor-panel:not(.collapsed) .panel-content {
  max-height: none;
  opacity: 1;
  transform: translateY(0);
}

/* 确保PinEditor组件在面板中正确显示 */
/* .panel-content :deep(.pin-editor) {
  border: none;
  box-shadow: none;
  border-radius: 0;
} */
</style>
