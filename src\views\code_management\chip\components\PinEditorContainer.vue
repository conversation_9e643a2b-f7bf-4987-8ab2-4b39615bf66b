<template>
  <div class="pin-editor-panel">
    <!-- 面板头部 -->
    <div class="panel-header">
      <h3 class="panel-title">
        <el-icon style="color: #ccc; font-size: 14px; margin-right: 5px;"><EditPen /></el-icon>
        引脚编辑器
      </h3>
    </div>

    <!-- 面板内容 -->
    <div class="panel-content" >
      <el-form :model="formData" label-position="left" label-width="140px" class="form-left-align">
        <!-- 基本信息 -->
         <div class="basic-info-section">
          <div class="section-header">
            <el-divider content-position="left">
              <span class="section-title">
                <el-icon style="color:#409eff"><InfoFilled /></el-icon>
                基本信息
              </span>
            </el-divider>
          </div>

          <div class="config-section">
            <!-- 动态生成基本信息表单项 -->
            <template v-for="field in pinInfoFields" :key="field.key">
              <el-form-item :label="field.display" size="small">
                <!-- 文本输入框 - 用于字符串和数字 -->
                <el-input
                  v-if="!Array.isArray(field.value)"
                  v-model="formData[field.key]"
                  :disabled="field.key === 'pin_id' || field.key === 'pin_name' || isReadOnlyMode"
                  :placeholder="field.placeholder !== undefined && field.placeholder !== null ? field.placeholder : `请输入${field.display}`"
                  size="small"
                  @input="handleFormChange"
                >
                  <!-- 为不可编辑字段添加提示 -->
                  <template v-if="field.key === 'pin_id'" #suffix>
                    <el-tooltip content="引脚编号不可编辑" placement="top">
                      <el-icon style="color: #909399;"><Lock /></el-icon>
                    </el-tooltip>
                  </template>
                  <template v-if="field.key === 'pin_name'" #suffix>
                    <el-tooltip content="引脚名称不可编辑" placement="top">
                      <el-icon style="color: #909399;"><Lock /></el-icon>
                    </el-tooltip>
                  </template>
                </el-input>

                <!-- 下拉选择框 - 用于数组值 -->
                <el-select
                  v-else
                  v-model="formData[field.key]"
                  :disabled="isReadOnlyMode"
                  :placeholder="field.placeholder !== undefined && field.placeholder !== null ? field.placeholder : `请选择${field.display}`"
                  size="small"
                  @change="(value) => handleSelectChange(field.key, value)"
                >
                  <!-- 为功能类型字段添加空的默认选项 -->
                  <el-option
                    v-if="field.key === 'module'"
                    label="请选择功能类型"
                    value=""
                    disabled
                  ></el-option>

                  <el-option
                    v-for="option in field.value"
                    :key="option"
                    :label="option"
                    :value="option"
                  ></el-option>
                </el-select>
              </el-form-item>
            </template>
          </div>
        </div>
        <!-- 模块配置字段 -->
        <div v-if="shouldShowModuleConfig" style="margin-top: 60px;" class = "module-config-section">
          <div class="section-header">
            <el-divider content-position="left">
              <span class="section-title">
                <el-icon  style="color:#409eff"><InfoFilled /></el-icon>
                模块配置
              </span>
            </el-divider>
          </div>

          <div class="config-section ">

          <!-- 🎯 引脚选择器（仅在SPI、IIC、UART时显示） -->
          <div v-if="shouldShowPinSelection" class="pin-selection-section">
            <el-form-item label="相关引脚选择" class="pin-selection-form-item" size="small">
              <div class="pin-selection-container">
                <!-- 标题和统计信息 -->
                <div class="pin-selection-header">
                  <div class="selection-title">
                    <el-icon style="color: #409eff; margin-right: 5px;"><Connection /></el-icon>
                    选择 {{ formData.module }} 功能相关的其他引脚
                  </div>
                  <!-- 🎯 并排显示的统计信息 -->
                  <div class="selection-stats">
                    <span class="available-count">
                      条数: {{ availablePinsForCurrentModule.length }} 
                    </span>
                    <span class="max-selection-hint">
                      最多可选: {{ maxSelectableCount }} 个
                    </span>
                  </div>
                </div>

                <!-- 🎯 选择进度指示器（放在可滑动区域外） -->
                <div class="selection-progress" v-if="selectedPins.length > 0">
                  <span class="progress-text">
                    已选择:  {{ maxSelectableCount }}/{{ selectedPins.length }} 
                  </span>
                  <el-progress
                    :percentage="Math.min((selectedPins.length / maxSelectableCount) * 100, 100)"
                    :stroke-width="6"
                    :show-text="false"
                    :color="selectedPins.length >= maxSelectableCount ? '#f56c6c' : '#409eff'"
                  />
                </div>

                <!-- 引脚选择区域 -->
                <div v-if="availablePinsForCurrentModule.length > 0" class="pin-checkbox-list">
                  <el-checkbox-group v-model="selectedPins" @change="handlePinSelectionChange">
                    <div class="pin-checkbox-grid">
                      <el-checkbox
                        v-for="pin in availablePinsForCurrentModule"
                        :key="pin.value"
                        :value="pin.value"
                        :disabled="!selectedPins.includes(pin.value) && selectedPins.length >= maxSelectableCount"
                        class="pin-checkbox-item"
                      >
                        <span class="pin-label">{{ pin.label }}</span>
                      </el-checkbox>
                    </div>
                  </el-checkbox-group>
                </div>

                <div v-else class="no-pins-available">
                  <el-icon style="color: #909399; margin-right: 5px;"><InfoFilled /></el-icon>
                  <span>暂无可选的 {{ formData.module }} 引脚</span>
                </div>
              </div>
            </el-form-item>
          </div>

          <template v-for="field in localModuleConfig" :key="field.key">
            <el-form-item :label="field.label" size="small">
              <!-- 下拉选择框 -->
              <el-select
                v-if="field.type === 'select'"
                :model-value="getFieldValue(field)"
                :disabled="isReadOnlyMode || (['SPI', 'UART', 'IIC'].includes(formData.module) && field.label === '类型') || isSoftwareSpiLocked(field)"
                :placeholder="field.placeholder !== undefined && field.placeholder !== null && field.placeholder !== ''? field.placeholder : `请选择${field.label}`"
                clearable
                size="small"
                @update:model-value="(value) => handleFieldValueChange(field.key, value)"
              >
                <!-- 为"类型"字段和锁定的软件SPI字段添加后缀提示 -->
               <template
                 v-if="(['SPI', 'UART', 'IIC'].includes(formData.module) && field.label === '类型') || isSoftwareSpiLocked(field)"
                  #suffix>

                  <el-tooltip
                    :content="isSoftwareSpiLocked(field) ? 'SPI类型选择硬件SPI后，软件SPI类型自动锁定为三线SPI' : '类型字段不可编辑'"
                    placement="top">
                    <el-icon style="color: #909399;"><Lock /></el-icon>
                  </el-tooltip>
                </template>
                <el-option
                  v-for="option in field.options"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                ></el-option>
              </el-select>

              <!-- 文本输入框 -->
              <el-input
                v-else-if="field.type === 'text'"
                :model-value="getFieldValue(field)"
                :disabled="isReadOnlyMode"
                :placeholder="field.placeholder || `请输入${field.label}`"
                size="small"
                @update:model-value="(value) => handleFieldValueChange(field.key, value)"
              ></el-input>

              <!-- 数字输入框 -->
              <el-tooltip
                v-else-if="field.type === 'number'"
                :content="field.placeholder !== undefined && field.placeholder !== null ? String(field.placeholder) : `请输入${field.label}`"
                placement="top"
                :disabled="false"
              >
                <el-input-number
                  :model-value="getFieldValue(field)"
                  :placeholder="field.placeholder !== undefined && field.placeholder !== null && field.placeholder !== '' ? String(field.placeholder) : `请输入${field.label}`"
                  :disabled="isReadOnlyMode"
                  :min="field.min || 0"
                  :max="field.max || 999999"
                  :step="field.step || 1"
                  :precision="field.precision || 0"
                  size="small"
                  @update:model-value="(value) => handleFieldValueChange(field.key, value)"
                  style="width: 100%"
                />
              </el-tooltip>

              <!-- 开关 -->
              <el-switch
                v-else-if="field.type === 'switch'"
                :model-value="getFieldValue(field)"
                :disabled="isReadOnlyMode"
                @update:model-value="(value) => handleFieldValueChange(field.key, value)"
              ></el-switch>

              <!-- 默认文本输入框 -->
              <el-input
                v-else
                :model-value="getFieldValue(field)"
                :disabled="isReadOnlyMode"
                :placeholder="field.placeholder !== undefined && field.placeholder !== null && field.placeholder !== ''? field.placeholder : `请输入${field.label}`"
                size="small"
                @update:model-value="(value) => handleFieldValueChange(field.key, value)"
              ></el-input>
            </el-form-item>
          </template>

          <!-- 保存配置按钮 -->
          <div class="button-group" style="margin-top: 20px; text-align: right;">
            <el-button
              type="primary"
              :disabled="!hasEditPermission || isReadOnlyMode"
              @click="handleSaveConfig"
            >
              保存配置
            </el-button>
          </div>
          </div>
        </div>
      </el-form>
    </div>
  </div>
    <el-dialog
      v-model="isChipChangeRequesting"
      title=""
      width="480px"
      :close-on-click-modal="false"
      :show-close="false"
      :draggable="false"
      align-center
      class="chip-request-dialog"
    >
      <div class="request-dialog-content">
        <!-- 图标和标题 -->
        <div class="request-dialog-header">
          <div class="request-icon">
            <el-icon class="rotating-icon">
              <Loading />
            </el-icon>
          </div>
          <h3 class="request-title">配置修改中</h3>
        </div>

        <!-- 内容描述 -->
        <div class="request-dialog-body">
          <!-- <div class="request-info">
            <div class="info-item">
              <span class="info-label">修改引脚：</span>
              <span class="info-value">{{ currentRequestInfo?.pinId }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">修改字段：</span>
              <span class="info-value">{{ currentRequestInfo?.fieldLabel }}</span>
            </div>
          </div> -->
          <div class="request-message">
            请等待当前引脚修改完成后，再进行其他引脚配置更改
          </div>
        </div>

        <!-- 进度指示 -->
        <div class="request-progress">
          <el-progress
            :percentage="100"
            :indeterminate="true"
            :duration="3"
            :stroke-color="'#409eff'"
            :show-text="false"
          />
        </div>
      </div>
    </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick, onMounted } from 'vue';
import { ElMessage } from 'element-plus';



import {
  InfoFilled,
  Connection,
  EditPen,
  Lock,
  Loading
} from '@element-plus/icons-vue';
import http from '@/utils/http/http';
import messageManager from '@/utils/messageManager';

// Props
const props = defineProps({
  // 当前编辑的引脚数据
  pinData: {
    type: Object,
    default: () => ({})
  },
  // 引脚信息数据（来自response.data.data.pin_info）
  pinInfo: {
    type: Array,
    default: () => []
  },
  // 是否禁用编辑
  disabled: {
    type: Boolean,
    default: false
  },
  // 模块配置数据（来自requestChipModulConfig）
  moduleConfig: {
    type: Array,
    default: () => []
  },
  // 项目信息（用于请求）
  projectCode: {
    type: String,
    default: ''
  },
  projectName: {
    type: String,
    default: ''
  },
  gitlab: {
    type: String,
    default: ''
  },
  projectBranch: {
    type: String,
    default: ''
  },
  branchStatus: {
    type: String,
    default: ''
  },
  chipName: {
    type: String,
    default: ''
  },
  // 🎯 所有引脚数据（用于显示可选引脚列表）
  allPinsData: {
    type: Array,
    default: () => []
  },
  // 芯片数据（用于API请求）
  chip: {
    type: [String, Array],
    default: ''
  },
  // 工作空间路径
  workspacePath: {
    type: String,
    default: ''
  },
  // 当前高亮的引脚ID
  highlightedPin: {
    type: [String, Number],
    default: null
  },
  // 是否有编辑权限
  hasEditPermission: {
    type: Boolean,
    default: true
  }
});

// Emits
const emit = defineEmits([
  'update:pinData',
  'save',
  'reset',
  'change',
  'module-change',
  'request-module-config',
  'module-config-change',
  'field-change',
  'pin-selection-change', // 🎯 引脚选择变化事件
  'pin-data-loaded', // 引脚数据加载完成事件
  'chip-preview-update', // 芯片预览更新事件
  'pin-table-update', // 引脚表格更新事件
  'update-pin-type', // 🎯 更新单个引脚类型事件
  'updateRelatedPinType', // 新增事件声明
  'refresh-chip-data' // 🎯 刷新芯片数据事件
  // ❌ 已移除：'config-change-request' - 不再使用数据变动监视器
]);


// chip_change 请求状态管理
const isChipChangeRequesting = ref(false);
const currentRequestInfo = ref(null);


// 表单数据
const formData = reactive({});

// 本地模块配置数据
const localModuleConfig = ref([]);

// ❌ 已移除：fieldOriginalValues - 不再使用数据变动监视器

// 🎯 选中的引脚列表（用于多引脚功能类型）
const selectedPins = ref([]);
const preSelectedPinIds = ref([]); // 存储从 PinInfo 解析出的需要默认选中的引脚 ID

// 🎯 引脚类型信息缓存（用于显示引脚类型）
const pinTypeInfoCache = ref({});

// 加载状态
const loading = ref(false);

// 当前编辑的引脚ID
const currentPinId = ref(null);

// 只读模式
const isReadOnlyMode = computed(() => props.disabled);

// 🎯 计算当前功能类型是否需要显示引脚选择
const shouldShowPinSelection = computed(() => {
  const currentModule = formData.module;
  return ['SPI', 'IIC', 'UART'].includes(currentModule);
});

// 🎯 计算是否应该显示模块配置
const shouldShowModuleConfig = computed(() => {
  const currentModule = formData.module;
  const hasModuleConfig = localModuleConfig.value.length > 0;

  // 只有当功能类型不为空且有模块配置数据时才显示
  const shouldShow = currentModule && currentModule !== '' && currentModule !== '未配置' && hasModuleConfig;

  console.log(`🎯 PinEditorContainer - 模块配置显示判断: 功能类型="${currentModule}", 配置数量=${localModuleConfig.value.length}, 是否显示=${shouldShow}`);

  return shouldShow;
});

// 🎯 不同功能类型的最大选择数量配置
const maxSelectionConfig = {
  'IIC': 1,   // IIC 最多选择 1 个引脚
  'SPI': 3,   // SPI 默认最多选择 3 个引脚
  'UART': 1   // UART 最多选择 1 个引脚
};

// 🎯 获取当前功能类型的最大选择数量（考虑SPI特殊情况）
const maxSelectableCount = computed(() => {
  const currentModule = formData.module;

  // SPI 特殊处理：如果软件SPI是三线SPI，最多选择2个引脚
  if (currentModule === 'SPI') {
    // 查找所有可能的软件SPI字段
    const allFields = localModuleConfig.value || [];
    let softwareSpiValue = null;

    allFields.forEach(field => {
      const softwareSpiFields = ['software_spi', '软件SPI', 'soft_spi'];
      const isSoftwareSpiField = softwareSpiFields.some(fieldName =>
        field.key === fieldName || field.label === fieldName || field.label.includes('软件')
      );

      if (isSoftwareSpiField && formData[field.key]) {
        softwareSpiValue = formData[field.key];
      }
    });

    console.log('🔍 SPI最大选择数量检查:', {
      currentModule,
      softwareSpiValue,
      allFormData: formData
    });

    if (softwareSpiValue === '三线SPI' || softwareSpiValue === 'three_wire') {
      console.log('🎯 软件SPI是三线SPI，最大选择数量设为2');
      return 2;
    }
  }

  const defaultCount = maxSelectionConfig[currentModule] || 0;
  console.log('🔍 使用默认最大选择数量:', defaultCount);
  return defaultCount;
});

// 🎯 判断软件SPI字段是否应该锁定
const isSoftwareSpiLocked = (field) => {
  if (formData.module !== 'SPI') return false;

  // 检查是否是软件SPI相关字段（包括软件SPI类型）
  const softwareSpiFields = ['software_spi', '软件SPI', 'soft_spi', '软件SPI类型', 'software_spi_type', '软件spi类型'];
  const isTargetField = softwareSpiFields.some(fieldName =>
    field.key === fieldName ||
    field.label === fieldName ||
    field.label === '软件SPI' ||
    field.label.includes('软件SPI') ||
    field.label.includes('软件spi')
  );

  if (!isTargetField) return false;

  // 检查SPI类型是否选择了硬件SPI选项
  const spiTypeFields = ['SPI类型', 'spi_type', 'spi类型'];
  let spiTypeValue = null;

  spiTypeFields.forEach(fieldName => {
    if (formData[fieldName] !== undefined) {
      spiTypeValue = formData[fieldName];
    }
  });

  const isLocked = spiTypeValue === '硬件SPI' || spiTypeValue === 'hardware_spi' || spiTypeValue === '硬件';

  console.log('🔍 SPI锁定检查:', {
    fieldKey: field.key,
    fieldLabel: field.label,
    isTargetField,
    spiTypeValue,
    isLocked,
    allFormData: formData
  });

  return isLocked;
};

// 🎯 处理字段值变化（包含SPI特殊逻辑）
const handleFieldValueChange = (fieldKey, value) => {
  console.log('🔄 字段值变化:', { fieldKey, value, module: formData.module });

  // 设置字段值
  setFieldValue(fieldKey, value);

  // SPI 特殊处理：SPI类型选择硬件SPI后自动设置软件SPI类型
  if (formData.module === 'SPI') {
    // 检查是否是SPI类型字段
    const spiTypeFields = ['SPI类型', 'spi_type', 'spi类型'];
    const isSpiTypeField = spiTypeFields.some(fieldName =>
      fieldKey === fieldName || fieldKey.includes('SPI类型') || fieldKey.includes('spi类型')
    );

    console.log('🔍 SPI类型字段检查:', { fieldKey, isSpiTypeField, value });

    if (isSpiTypeField && (value === '硬件SPI' || value === 'hardware_spi' || value === '硬件')) {
      console.log('🎯 检测到SPI类型设置为硬件SPI，开始自动设置软件SPI类型');

      // 查找软件SPI类型字段并设置为三线SPI
      const allFields = localModuleConfig.value || [];
      allFields.forEach(field => {
        const softwareSpiTypeFields = ['软件SPI类型', 'software_spi_type', '软件spi类型'];
        const isSoftwareSpiTypeField = softwareSpiTypeFields.some(fieldName =>
          field.key === fieldName ||
          field.label === fieldName ||
          field.label.includes('软件SPI类型') ||
          field.label.includes('软件spi类型')
        );

        if (isSoftwareSpiTypeField) {
          console.log('🔧 清除软件SPI类型历史值，设置为三线SPI:', field.key);
          // 清除历史值并设置为三线SPI
          formData[field.key] = '三线SPI';
        }
      });

      console.log('✅ SPI类型设置为硬件SPI后，软件SPI类型已设置为三线SPI并锁定');
    }
  }
};

// 🎯 获取可选的引脚列表（未配置且支持当前功能类型的引脚 + PinInfo中的相关引脚）
const availablePinsForCurrentModule = computed(() => {
  if (!shouldShowPinSelection.value || !props.allPinsData || !Array.isArray(props.allPinsData)) {
    console.log('🔍 availablePinsForCurrentModule: 条件不满足，返回空数组');
    return [];
  }

  const currentModule = formData.module;
  const currentPinId = formData.pin_id;

  console.log('🔍 availablePinsForCurrentModule 计算中:');
  console.log('  - currentModule:', currentModule);
  console.log('  - currentPinId:', currentPinId);
  console.log('  - preSelectedPinIds:', preSelectedPinIds.value);
  console.log('  - selectedPins:', selectedPins.value);

  return props.allPinsData.filter(pin => {
    // 排除当前正在编辑的引脚
    if (pin.pinId?.toString() === currentPinId?.toString()) {
      return false;
    }

    const pinId = pin.pinId?.toString() || pin.pinNumber?.toString();

    // 🎯 检查是否是从 PinInfo 解析出来的相关引脚
    const isFromPinInfo = preSelectedPinIds.value.includes(pinId);

    // 只显示未配置的引脚（model为空、null或'Null'）或者是从PinInfo解析出来的引脚
    const isUnconfigured = !pin.model || pin.model === 'Null' || pin.model === '';

    // 🎯 如果是从PinInfo解析出来的引脚，直接包含（不管是否已配置）
    if (isFromPinInfo) {
      console.log(`🎯 包含PinInfo相关引脚: ${pinId}`);
      return true;
    }

    // 检查引脚的可选类型中是否包含当前功能类型
    let supportsCurrentModule = false;
    if (pin.availableTypes && Array.isArray(pin.availableTypes)) {
      supportsCurrentModule = pin.availableTypes.includes(currentModule);
    } else if (pin.module && Array.isArray(pin.module)) {
      supportsCurrentModule = pin.module.includes(currentModule);
    }

    return isUnconfigured && supportsCurrentModule;
  }).map(pin => {
    // 🎯 获取引脚类型信息
    const pinId = pin.pinId?.toString() || pin.pinNumber?.toString();
    const typeInfo = pinTypeInfoCache.value[pinId];

    // 🎯 构建显示标签：引脚 6 ( P0_0 ) ( CS )
    const pinNumber = pin.pinNumber || pin.pinId;
    const pinName = pin.pinName || 'PIN' + pinNumber;

    let displayLabel = `引脚 ${pinNumber}(   ${pinName}   )`;

    if (typeInfo) {
      // 如果有类型信息，添加到标签中
      displayLabel += ` (   ${typeInfo}   )`;
    }

    return {
      value: pinId,
      label: displayLabel,
      pinId: pinId,
      pinName: pinName,
      pinNumber: pinNumber,
      typeInfo: typeInfo
    };
  });

  console.log('🔍 availablePinsForCurrentModule 计算结果:');
  console.log('  - 可选引脚数量:', filteredPins.length);
  console.log('  - 可选引脚列表:', filteredPins.map(p => `${p.pinId}(${p.label})`));

  return filteredPins;
});

// 处理pin_info数据，转换为表单字段
const pinInfoFields = computed(() => {
  if (!props.pinInfo || !Array.isArray(props.pinInfo)) {
    console.log('DynamicPinEditor - pinInfo为空或不是数组:', props.pinInfo);
    return [];
  }

  const fields = props.pinInfo.map(field => ({
    key: field.key,
    display: field.display,
    value: field.value,
    placeholder: field.placeholder,
    cachedValue: field.cachedValue // 🎯 传递缓存值
  }));

  console.log('DynamicPinEditor - pinInfoFields计算结果:', fields);
  console.log('DynamicPinEditor - 是否包含module字段:', fields.some(f => f.key === 'module'));

  // 🎯 专门检查module字段的cachedValue
  const moduleField = fields.find(f => f.key === 'module');
  if (moduleField) {
    console.log('🎯 DynamicPinEditor - module字段详情:', moduleField);
    console.log('🎯 DynamicPinEditor - module字段cachedValue:', moduleField.cachedValue);
  }

  return fields;
});

// 初始化表单数据
const initializeFormData = () => {
  // 保存当前的module值（如果存在）
  const currentModuleValue = formData.module;

  // 清空现有数据
  Object.keys(formData).forEach(key => {
    delete formData[key];
  });

  // 从pinData初始化基本数据
  if (props.pinData && typeof props.pinData === 'object') {
    Object.keys(props.pinData).forEach(key => {
      formData[key] = props.pinData[key];
    });

    // 🎯 特别处理功能类型字段
    if (props.pinData.pinType !== undefined) {
      // 如果pinType是"未配置"或空，则设置module为空字符串
      if (props.pinData.pinType === '未配置' || !props.pinData.pinType) {
        formData.module = '';
        console.log('🎯 引脚类型为"未配置"，设置功能类型为空');
      } else {
        formData.module = props.pinData.pinType;
        console.log(`🎯 设置功能类型为: "${props.pinData.pinType}"`);
      }
    }
  }

  // 从pinInfo初始化字段数据
  if (props.pinInfo && Array.isArray(props.pinInfo)) {
    props.pinInfo.forEach(field => {
      if (field.key && field.cachedValue !== undefined) {
        formData[field.key] = field.cachedValue;
        console.log(`🎯 从缓存恢复字段 ${field.key}:`, field.cachedValue);
      } else if (field.key && formData[field.key] === undefined) {
        // 如果没有缓存值，使用默认值
        if (Array.isArray(field.value) && field.value.length > 0) {
          formData[field.key] = '';
        } else {
          formData[field.key] = field.value || '';
        }
      }
    });
  }

  console.log('初始化表单数据:', formData);
};

// 下拉框变化处理
const handleSelectChange = (fieldKey, value) => {
  console.log(`字段 ${fieldKey} 变化为:`, value);

  // 检测是否是功能类型变化
  const isModuleChange = fieldKey === 'module';

  if (isModuleChange) {
    if (value && value !== '') {
      console.log(`🔄 检测到功能类型变化为: ${value}，准备请求模块配置`);
    } else {
      console.log('🧹 检测到功能类型清空，准备清理模块配置');
    }

    // 清空模块配置
    localModuleConfig.value = [];

    // 清空之前的模块配置字段值
    Object.keys(formData).forEach(key => {
      if (!['pin_id', 'pin_name', 'module'].includes(key)) {
        delete formData[key];
      }
    });

    // 发送功能类型变化事件
    const moduleChangeData = {
      pinId: formData.pin_id,
      pinName: formData.pin_name,
      newModule: value,
      moduleType: value,
      formData: { ...formData },
      isModuleChange: true
    };

    console.log('🚀 发送功能类型变化事件:', moduleChangeData);
    emit('module-change', moduleChangeData);

    // 如果功能类型不为空，请求模块配置
    if (value && value !== '') {
      handleRequestModuleConfig(value);
    } else {
      // 如果功能类型为空，清空模块配置
      console.log('🧹 功能类型为空，清空模块配置');
      localModuleConfig.value = [];
    }
  } else {
    // 非功能类型字段变化
    const fieldChangeData = {
      pinId: formData.pin_id,
      fieldKey: fieldKey,
      fieldValue: value,
      isModuleChange: false
    };

    console.log('📝 发送字段变化事件:', fieldChangeData);
    emit('field-change', fieldChangeData);
  }
};

// 表单变化处理
const handleFormChange = () => {
  const changeData = {
    ...formData,
    isModuleChange: false
  };

  console.log('表单数据变化:', changeData);
  emit('change', changeData);
};

// ❌ 已移除：handleSave 和 handleReset 方法
// 原因：现在使用 handleSaveConfig 方法进行配置保存

// 保存配置处理
const handleSaveConfig = async () => {
  try {
    console.log('🔄 开始保存配置');



    // 🎯 收集所有模块配置数据，转换为Key-Value列表格式
    // 确保每个key都有值，如果没有值则默认为空字符串
    const moduleConfigList = [];
    console.log('🔍 开始数据检测，模块配置字段数量:', localModuleConfig.value.length);

    localModuleConfig.value.forEach(field => {
      let fieldValue = formData[field.key];

      // 🎯 数据检测：如果字段值为 undefined、null 或空字符串，则设为空字符串
      if (fieldValue === undefined || fieldValue === null || fieldValue === '') {
        fieldValue = '';
        console.log(`⚠️ 字段 "${field.key}" 没有值，设置为空字符串`);
      } else {
        console.log(`✅ 字段 "${field.key}" 有值:`, fieldValue);
      }

      // 🎯 所有字段都添加到配置列表中，包括空值字段
      moduleConfigList.push({
        key: field.key,
        value: fieldValue
      });
    });

    // 🎯 数据检测总结
    const emptyFields = moduleConfigList.filter(item => item.value === '');
    const filledFields = moduleConfigList.filter(item => item.value !== '');

    console.log('📊 数据检测总结:');
    console.log(`  - 总字段数: ${moduleConfigList.length}`);
    console.log(`  - 有值字段数: ${filledFields.length}`);
    console.log(`  - 空值字段数: ${emptyFields.length}`);
    if (emptyFields.length > 0) {
      console.log(`  - 空值字段列表: [${emptyFields.map(f => f.key).join(', ')}]`);
    }

    // 处理相关引脚选择数据，并添加到模块配置中
    let pinInfoList = [];

    // 首先添加当前引脚的信息
    if (formData.pin_id) {
      const currentPinInfo = {
        pin_id: formData.pin_id,
        type: formData.type
      };
      pinInfoList.push(currentPinInfo);
      console.log('📋 添加当前引脚信息:', currentPinInfo);
    }

    // 然后添加相关引脚选择的信息
    if (shouldShowPinSelection.value && selectedPins.value.length > 0) {
      const relatedPins = selectedPins.value.map(pinId => {
        //从缓存中获取引脚的类型信息
        const typeInfo = pinTypeInfoCache.value[pinId] || '';
        
        return {
          pin_id: pinId,
          type: typeInfo
        };
         // 将相关引脚的功能类型改成当前引脚的功能类型
      //   const newType = formData.module;
      //   // 更新引脚功能类型缓存
      //   pinTypeInfoCache.value[pinId] = newType;
      //   return {
      //     pin_id: pinId,
      //     type: newType
      //   };
      });

      // 合并到 pinInfoList 中
      pinInfoList = pinInfoList.concat(relatedPins);
      console.log('📋 添加相关引脚信息:', relatedPins);
    }

    // 将 pinInfo 添加到 module_config 中（无论是否有相关引脚选择）
    if (pinInfoList.length > 0) {
      moduleConfigList.push({
        key: 'PinInfo',
        value: pinInfoList
      });

      console.log('📋 引脚信息数据:', pinInfoList);
    }

    console.log('📋 准备保存的模块配置数据:', moduleConfigList);

    // 如果没有配置数据，提示用户
    if (moduleConfigList.length === 0) {
      messageManager.warning('没有需要保存的配置数据');
      return;
    }

    // 🎯 检测通道名称是否有值
    const channelNameFields = ['name', 'channel_name', '通道名称', 'channelName'];
    let channelNameValue = null;
    let channelNameFound = false;

    // 在模块配置中查找通道名称字段
    moduleConfigList.forEach(item => {
      if (channelNameFields.includes(item.key)) {
        channelNameValue = item.value;
        channelNameFound = true;
        console.log('🔍 找到通道名称字段:', item.key, '值:', channelNameValue);
      }
    });

    // 如果找到通道名称字段但值为空，进行拦截
    if (channelNameFound && (!channelNameValue || channelNameValue.trim() === '')) {
      console.warn('⚠️ 通道名称为空，拦截保存请求');
      messageManager.warning('请填写通道名称后再保存配置');
      return;
    }

    // 如果没有找到通道名称字段，也检查 formData 中的相关字段
    if (!channelNameFound) {
      channelNameFields.forEach(fieldName => {
        if (formData[fieldName] !== undefined) {
          channelNameValue = formData[fieldName];
          channelNameFound = true;
          console.log('🔍 在 formData 中找到通道名称字段:', fieldName, '值:', channelNameValue);
        }
      });

      if (channelNameFound && (!channelNameValue || channelNameValue.trim() === '')) {
        console.warn('⚠️ 通道名称为空，拦截保存请求');
        messageManager.warning('请填写通道名称后再保存配置');
        return;
      }
    }

    console.log('✅ 通道名称检测通过:', channelNameValue);

    // 🎯 SPI 功能类型特殊检测
    if (formData.module === 'SPI') {
      console.log('🔍 检测到 SPI 功能类型，开始进行引脚数量检测');

      // 查找软件软件SPI类型字段
      const spiTypeFields = ['soft_spi_type', '软件SPI类型'];
      let spiTypeValue = null;
      let spiTypeFound = false;

      // 在模块配置中查找SPI类型字段
      moduleConfigList.forEach(item => {
        if (spiTypeFields.includes(item.key)) {
          spiTypeValue = item.value;
          spiTypeFound = true;
          console.log('🔍 找到软件SPI类型字段:', item.key, '值:', spiTypeValue);
        }
      });

      // 如果没有在模块配置中找到，检查 formData
      if (!spiTypeFound) {
        spiTypeFields.forEach(fieldName => {
          if (formData[fieldName] !== undefined && formData[fieldName] !== null && formData[fieldName] !== '') {
            spiTypeValue = formData[fieldName];
            spiTypeFound = true;
            console.log('🔍 在 formData 中找到软件SPI类型字段:', fieldName, '值:', spiTypeValue);
          }
        });
      }

      if (spiTypeFound && spiTypeValue) {
        const pinInfoCount = pinInfoList.length;
        console.log('📊 SPI检测信息:');
        console.log(`  - 软件SPI类型: ${spiTypeValue}`);
        console.log(`  - PinInfo数量: ${pinInfoCount}`);
        console.log(`  - PinInfo列表:`, pinInfoList.map(p => `${p.pin_id}(${p.type})`));

        // 根据软件SPI类型检测引脚数量
        if (spiTypeValue === '三线SPI') {
          if (pinInfoCount !== 3) {
            console.error(`❌ SPI三线模式引脚数量错误: 期望3个，实际${pinInfoCount}个`);
            messageManager.error(`SPI三线模式需要配置3个引脚，当前配置了${pinInfoCount}个引脚，请检查相关引脚选择`);
            return;
          }
          console.log('✅ SPI三线模式引脚数量检测通过');
        } else if (spiTypeValue === '四线SPI') {
          if (pinInfoCount !== 4) {
            console.error(`❌ SPI四线模式引脚数量错误: 期望4个，实际${pinInfoCount}个`);
            messageManager.error(`SPI四线模式需要配置4个引脚，当前配置了${pinInfoCount}个引脚，请检查相关引脚选择`);
            return;
          }
          console.log('✅ SPI四线模式引脚数量检测通过');
        } else {
          console.warn('⚠️ 未识别的SPI类型:', spiTypeValue);
        }
      } else {
        console.warn('⚠️ SPI功能类型但未找到SPI类型字段，跳过引脚数量检测');
      }
    }

    // 构造请求参数 - 将 module_config 转换为对象格式
    const moduleConfigObject = {};
    moduleConfigList.forEach(item => {
      moduleConfigObject[item.key] = item.value;
    });

    const requestParams = {
      project_code: props.projectCode,
      project_name: props.projectName,
      gitlab: props.gitlab,
      project_branch: props.projectBranch,
      branch_status: props.branchStatus,
      chip_name: props.chipName,
      pin_number: formData.pin_id,
      workspace_path: props.workspacePath,
      function_type: formData.module,
      module_config: moduleConfigObject  // 使用对象格式，key-value一一对应
    };

    console.log('🚀 发送保存配置请求:', requestParams);
    console.log('📋 模块配置列表格式:', JSON.stringify(moduleConfigList, null, 2));
    console.log('📋 模块配置对象格式:', JSON.stringify(moduleConfigObject, null, 2));

    isChipChangeRequesting.value = true;
    // 发送 GET 请求
    const response = await http.get('/code_management/chip_change', {
      params: requestParams
    });

    console.log('✅ 保存配置请求:', response.data);
    isChipChangeRequesting.value = false;
    if (response.data && response.data.status === 1) {
      messageManager.success('配置保存成功');

      // 🎯 保存成功后，更新芯片预览和引脚配置表
      if (pinInfoList.length > 0) {
        console.log('🔄 开始更新芯片预览和引脚配置表:', pinInfoList);

        // 构造更新数据
        const updateData = {
          pinUpdates: pinInfoList.map(pinInfo => ({
            pinId: pinInfo.pin_id,
            pinNumber: pinInfo.pin_id,
            type: pinInfo.type,
            functionType: formData.module,
            channelName: channelNameValue || formData.name || '',
            updateSource: 'pin_editor_save'
          })),
          moduleType: formData.module,
          mainPinId: formData.pin_id,
          channelName: channelNameValue || formData.name || '',
          configData: moduleConfigObject
        };

        console.log('📋 发送芯片预览更新事件:', updateData);
        emit('chip-preview-update', updateData);

        console.log('📋 发送引脚表格更新事件:', updateData);
        emit('pin-table-update', updateData);

        // 🎯 为每个引脚单独发送类型更新事件
        pinInfoList.forEach(pinInfo => {
          console.log(`🎯 更新引脚 ${pinInfo.pin_id} 类型为: ${pinInfo.type}`);
          emit('update-pin-type', {
            pinId: pinInfo.pin_id,
            newType: pinInfo.type,
            functionType: formData.module,
            channelName: channelNameValue || formData.name || ''
          });
        });

        // 🎯 当有相关引脚选择时，重新刷新芯片预览和引脚配置表数据
        console.log('🔄 有相关引脚选择，发送数据刷新请求');
        emit('refresh-chip-data', {
          reason: 'pin_config_saved_with_related_pins',
          affectedPins: pinInfoList.map(p => p.pin_id),
          moduleType: formData.module
        });

      } else {
        // 如果没有 pinInfoList，使用原来的逻辑
        console.log('📋 没有相关引脚信息，使用主引脚更新');
        emit('pin-table-update', {
          pinId: formData.pin_id,
          moduleType: formData.module,
          configData: moduleConfigObject
        });
        emit('chip-preview-update', {
          pinId: formData.pin_id,
          moduleType: formData.module,
          configData: moduleConfigObject
        });
      }
    } else {
      console.error('❌ 保存配置请求失败:', response.data);
      messageManager.error('配置保存失败: ' + (response.data?.message || '未知错误'));
    }

  } catch (error) {
    console.error('❌ 保存配置失败:', error);
    messageManager.error('配置保存失败: ' + error.message);
  }
};

// 🎯 处理引脚选择变化
const handlePinSelectionChange = (selectedValues) => {
  console.log('🎯 引脚选择变化:', selectedValues);
  console.log('🎯 当前功能类型:', formData.module);

  const currentModule = formData.module;
  const maxCount = maxSelectableCount.value;

  // 🎯 检查是否超过最大选择数量
  if (selectedValues.length > maxCount) {
    console.warn(`⚠️ ${currentModule} 功能类型最多只能选择 ${maxCount} 个引脚`);

    // 显示警告消息
    ElMessage.warning(`${currentModule} 功能类型最多只能选择 ${maxCount} 个引脚`);

    // 保持之前的选择状态，移除最新选择的引脚
    const limitedSelection = selectedValues.slice(0, maxCount);
    selectedPins.value = limitedSelection;

    console.log(`🎯 已限制选择数量为 ${maxCount} 个:`, limitedSelection);
    return;
  }

  // 更新选中的引脚列表
  selectedPins.value = selectedValues;

  // 触发引脚选择变化事件，传递给父组件处理
  emit('pin-selection-change', {
    pinId: formData.pin_id,
    moduleType: formData.module,
    selectedPins: selectedValues,
    availablePins: availablePinsForCurrentModule.value,
    maxSelectableCount: maxCount
  });
};

// 获取字段值
const getFieldValue = (field) => {
  const currentValue = formData[field.key];

  // 如果当前值不为空，直接返回（包括数字0）
  if (currentValue !== undefined && currentValue !== null && currentValue !== '') {
    return currentValue;
  }

  // 对于数字类型，0也是有效值
  if (field.type === 'number' && currentValue === 0) {
    return currentValue;
  }

  // 如果当前值为空，检查是否有placeholder可以作为默认值
  if (field.placeholder !== undefined && field.placeholder !== null && field.placeholder !== '') {
    console.log(`🔍 字段 ${field.key} 当前值为空，尝试使用默认值:`, field.placeholder);

    if (field.type === 'number') {
      const numValue = Number(field.placeholder);
      if (!isNaN(numValue)) {
        formData[field.key] = numValue;
        console.log(`✅ 设置数字字段 ${field.key} 默认值:`, numValue);
        return numValue;
      } else {
        console.error(`❌ 数字类型字段 ${field.key} 的placeholder "${field.placeholder}" 不是有效数字`);
        formData[field.key] = 0;
        return 0;
      }
    } else if (field.type === 'select') {
      const validOption = field.options?.find(opt => opt.value === field.placeholder);
      if (validOption) {
        formData[field.key] = field.placeholder;
        console.log(`✅ 设置选择字段 ${field.key} 默认值:`, field.placeholder);
        return field.placeholder;
      }
    } else {
      formData[field.key] = field.placeholder;
      console.log(`✅ 设置文本字段 ${field.key} 默认值:`, field.placeholder);
      return field.placeholder;
    }
  }

  // 根据字段类型返回默认值
  if (field.type === 'number') {
    return 0;
  } else if (field.type === 'switch') {
    return false;
  } else {
    return currentValue || '';
  }
};

// 设置字段值
const setFieldValue = (fieldKey, value) => {
  const field = localModuleConfig.value.find(f => f.key === fieldKey);
  if (field && field.type === 'number') {
    if (value === '' || value === undefined) {
      formData[fieldKey] = null;
    } else {
      const numValue = Number(value);
      formData[fieldKey] = isNaN(numValue) ? null : numValue;
    }
  } else {
    formData[fieldKey] = value;
  }
};

// ❌ 已移除：handleModuleConfigComplete 方法
// 原因：移除数据变动监视器后，此方法不再使用

// 请求芯片模块配置
const requestChipModulConfig = async (moduleType, pinId = null) => {
  try {
    console.log(`🔄 开始请求芯片模块配置 - 功能类型: ${moduleType}, 引脚ID: ${pinId}`);

    // 跳过特殊功能类型
    if (moduleType === 'OTHER' || moduleType === 'CAN') {
      return {
        config: [],
        pinId
      };selectedPins
    }

    // 请求后端接口
    const response = await http.get('/code_management/chip_modul', {
      params: {
        project_code: props.projectCode,
        project_name: props.projectName,
        gitlab: props.gitlab,
        project_branch: props.projectBranch,
        branch_status: props.branchStatus,
        chip_name: props.chipName,
        pin_number: pinId,
        module: moduleType
      }
    });
    console.log(`✅ 请求芯片模块配置成功:`, response.data);

    if (response.data && response.data.status === 1) {
      console.log("引脚信息:", response.data.data.module);

      // 🎯 获取模块配置数据
      let moduleConfig = [];
      if (response.data.data && response.data.data.module) {
        if (Array.isArray(response.data.data.module)) {
          // 🎯 查找并解析 PinInfo 字段（在数组格式中）
          moduleConfig = response.data.data.module;

          // 🎯 查找并解析 PinInfo 字段（在数组格式中）
          if (['SPI', 'IIC', 'UART'].includes(moduleType)) {
            const pinInfoField = moduleConfig.find(field => field.key === 'PinInfo');
            if (pinInfoField && pinInfoField.value) {
              console.log('🎯 发现 PinInfo 字段，开始解析相关引脚:', pinInfoField.value);

              let pinInfoList = pinInfoField.value;
              // 如果是字符串，尝试解析为JSON
              if (typeof pinInfoList === 'string') {
                try {
                  pinInfoList = JSON.parse(pinInfoList);
                } catch (e) {
                  console.warn('⚠️ PinInfo 字段解析失败:', e);
                  pinInfoList = [];
                }
              }

              if (Array.isArray(pinInfoList) && pinInfoList.length > 0) {
                console.log('📋 解析到的 PinInfo 列表:', pinInfoList);

                // 提取相关引脚ID（排除当前引脚）
                const currentPinId = pinId?.toString();
                const relatedPinIds = pinInfoList
                  .filter(pinInfo => pinInfo.pin_id?.toString() !== currentPinId)
                  .map(pinInfo => pinInfo.pin_id?.toString())
                  .filter(id => id); // 过滤掉空值

                console.log(`🎯 当前引脚: ${currentPinId}, 相关引脚: [${relatedPinIds.join(', ')}]`);

                // 🔥 关键：将解析出的 pin_id 同步到 selectedPins（让复选框默认选中）
                selectedPins.value = relatedPinIds;
                preSelectedPinIds.value = relatedPinIds;

                console.log('🔥 设置选中引脚列表:', selectedPins.value);
                console.log('🔥 设置预选引脚列表:', preSelectedPinIds.value);

                // 🎯 缓存引脚类型信息
                pinInfoList.forEach(pinInfo => {
                  if (pinInfo.pin_id && pinInfo.type) {
                    pinTypeInfoCache.value[pinInfo.pin_id.toString()] = pinInfo.type;
                    console.log(`🎯 缓存引脚 ${pinInfo.pin_id} 的类型: ${pinInfo.type}`);
                  }
                });
              }
            }
          }
        } else {
          // 🎯 处理对象格式的模块配置数据
          const moduleData = response.data.data.module;
          console.log('🔍 处理对象格式的模块配置数据:', moduleData);

          // 🎯 查找并解析 PinInfo 字段
          if (moduleData.PinInfo && ['SPI', 'IIC', 'UART'].includes(moduleType)) {
            console.log('🎯 发现 PinInfo 字段，开始解析相关引脚:', moduleData.PinInfo);

            let pinInfoList = moduleData.PinInfo;
            // 如果是字符串，尝试解析为JSON
            if (typeof pinInfoList === 'string') {
              try {
                pinInfoList = JSON.parse(pinInfoList);
              } catch (e) {
                console.warn('⚠️ PinInfo 字段解析失败:', e);
                pinInfoList = [];
              }
            }

            if (Array.isArray(pinInfoList) && pinInfoList.length > 0) {
              console.log('📋 解析到的 PinInfo 列表:', pinInfoList);

              // 提取相关引脚ID（排除当前引脚）
              const currentPinId = pinId?.toString();
              const relatedPinIds = pinInfoList
                .filter(pinInfo => pinInfo.pin_id?.toString() !== currentPinId)
                .map(pinInfo => pinInfo.pin_id?.toString())
                .filter(id => id); // 过滤掉空值

              console.log(`🎯 当前引脚: ${currentPinId}, 相关引脚: [${relatedPinIds.join(', ')}]`);

              // 🔥 关键：将解析出的 pin_id 同步到 selectedPins（让复选框默认选中）
              selectedPins.value = relatedPinIds;
              preSelectedPinIds.value = relatedPinIds;

              console.log('🔥 设置选中引脚列表:', selectedPins.value);
              console.log('🔥 设置预选引脚列表:', preSelectedPinIds.value);

              // 🎯 缓存引脚类型信息
              pinInfoList.forEach(pinInfo => {
                if (pinInfo.pin_id && pinInfo.type) {
                  pinTypeInfoCache.value[pinInfo.pin_id.toString()] = pinInfo.type;
                  console.log(`🎯 缓存引脚 ${pinInfo.pin_id} 的类型: ${pinInfo.type}`);
                }
              });
            }
          }

          // 🎯 将对象转换为数组格式
          moduleConfig = Object.entries(moduleData)
            .filter(([key, value]) => key !== 'PinInfo') // 排除 PinInfo，它不是配置字段
            .map(([key, value]) => ({
              key,
              label: key,
              value,
              type: Array.isArray(value) ? 'select' : 'text'
            }));
        }
      }

      console.log('🎯 最终的模块配置数据:', moduleConfig);

      console.log(`✅ 解析到模块配置数据:`, moduleConfig);
      console.log(`📊 模块配置字段数量: ${moduleConfig.length}`);

      // 详细打印每个配置字段
      if (moduleConfig.length > 0) {
        moduleConfig.forEach((field, index) => {
          console.log(`📋 字段 ${index + 1}:`, {
            key: field.key || field.name,
            label: field.label || field.display || field.name,
            type: field.type,
            options: field.options?.length || 0
          });
        });
      }

      // 返回处理后的配置数据
      return {
        config: moduleConfig,
        pinId
      };
    } else {
      console.error('❌ 请求芯片模块配置失败:', response.data);
      messageManager.error('请求芯片模块配置失败: ' + (response.data?.message || '未知错误'));
      return null;
    }
  } catch (error) {
    console.error('❌ 请求芯片模块配置异常:', error);
    messageManager.error('请求芯片模块配置失败: ' + error.message);
    return null;
  }
};

// 处理请求模块配置
const handleRequestModuleConfig = async (moduleType) => {
  console.log('🎯 收到请求模块配置事件:', moduleType);

  const pinId = formData.pin_id || currentPinId.value;
  if (!pinId) {
    console.warn('⚠️ 当前引脚ID为空，无法请求模块配置');
    return;
  }

  try {
    const result = await requestChipModulConfig(moduleType, pinId);

    if (result && result.config) {
      const moduleConfig = result.config;
      console.log(`✅ 获取到模块配置 (来自API):`, moduleConfig);
      setModuleConfig(moduleConfig, false);
    } else {
      console.warn('⚠️ 未获取到有效的模块配置');
      localModuleConfig.value = [];
    }
  } catch (error) {
    console.error('❌ 处理请求模块配置失败:', error);
    localModuleConfig.value = [];
  }
};

// 获取引脚编辑器数据
const getPinEditorData = async (pinId) => {
  try {
    loading.value = true;
    currentPinId.value = pinId;
    console.log('🔄 开始获取引脚编辑器数据, 引脚ID:', pinId);
    console.log('🔄 开始获取引脚编辑器数据, chip:', props.chip);

    const response = await http.get('/code_management/chip_config', {
      params: {
        pinId: pinId,
        chip: props.chip
      }
    });

    console.log("获取引脚编辑器数据完整响应:", response);
    console.log("获取引脚编辑器数据:", response.data);

    if (response.data && response.data.status === 1) {
      // 处理成功响应
      const editorData = response.data.data || {};
      console.log('引脚编辑器数据处理成功:', editorData);

      // 从table数据中找到对应的引脚信息
      let pinInfo = {};
      if (editorData.table && Array.isArray(editorData.table)) {
        pinInfo = editorData.table.find(pin =>
          pin.pin_id?.toString() === pinId?.toString() ||
          pin.pin?.toString() === pinId?.toString()
        ) || {};
      }

      console.log('找到的引脚信息:', pinInfo);

      // 处理功能类型数据，确保module是数组
      let moduleOptions = [];
      if (pinInfo.module) {
        if (Array.isArray(pinInfo.module)) {
          moduleOptions = pinInfo.module;
        } else {
          moduleOptions = [pinInfo.module];
        }
      }

      // 获取pin_info数据
      let pinInfoData = editorData.pin_info || [];
      console.log('获取到的pin_info数据:', pinInfoData);

      // 确保pinInfoData是数组
      if (!Array.isArray(pinInfoData)) {
        pinInfoData = [];
      }

      // 检查是否已经包含功能类型字段，如果没有则添加
      const hasModuleField = pinInfoData.some(field => field.key === 'module');
      if (!hasModuleField && moduleOptions.length > 0) {
        console.log('添加功能类型字段到pin_info');
        pinInfoData.push({
          key: 'module',
          display: '功能类型',
          value: moduleOptions
        });
      }

      // 🎯 从引脚配置表中获取引脚的功能类型数据（不是具体类型）
      let functionTypeFromTable = '';
      let specificTypeFromTable = '';
      if (props.allPinsData && Array.isArray(props.allPinsData)) {
        const tablePin = props.allPinsData.find(pin =>
          pin.pinId?.toString() === pinId?.toString()
        );
        if (tablePin) {
          // 🎯 获取功能类型（如SPI、UART等）- 确保是功能类型而不是具体类型
          let rawPinType = tablePin.pinType;
          if (rawPinType && rawPinType !== '未配置') {
            // 🎯 检查是否是具体类型，如果是则需要转换为功能类型
            const specificTypes = ['CS', 'CLK', 'MOSI', 'MISO', 'TX', 'RX', 'SDA', 'SCL'];
            if (specificTypes.includes(rawPinType)) {
              // 🎯 如果是具体类型，需要从模块选项中推断功能类型
              if (moduleOptions.length > 0) {
                // 使用第一个可用的功能类型作为默认值
                functionTypeFromTable = moduleOptions[0];
                console.log(`🎯 检测到具体类型 "${rawPinType}"，推断功能类型为: "${functionTypeFromTable}"`);
              } else {
                functionTypeFromTable = '';
              }
            } else {
              // 🎯 如果已经是功能类型，直接使用
              functionTypeFromTable = rawPinType;
            }
          } else {
            functionTypeFromTable = '';
          }

          // 获取具体类型（如CS、CLK等）- 从model字段获取
          specificTypeFromTable = (pinInfo.model && pinInfo.model !== 'Null') ? pinInfo.model : '';
          console.log(`🎯 从引脚配置表获取引脚 ${pinId} 的功能类型: "${tablePin.pinType}" -> "${functionTypeFromTable}"`);
          console.log(`🎯 从API获取引脚 ${pinId} 的具体类型: "${pinInfo.model}" -> "${specificTypeFromTable}"`);
        }
      }

      // 功能类型：优先使用引脚配置表中的数据
      const finalFunctionType = functionTypeFromTable;
      // 具体类型：使用API返回的model字段
      const finalSpecificType = specificTypeFromTable;

      // 构建引脚编辑器数据
      const pinEditorData = {
        ...editorData,
        pinId: pinInfo.pin_id || pinInfo.pin || pinId,
        pinName: pinInfo.pin_name || pinInfo.name || `PIN${pinId}`,
        pinType: finalFunctionType, // 使用功能类型，不是具体类型
        module: finalFunctionType,  // 功能类型字段
        type: finalSpecificType,    // 具体类型字段
        moduleOptions: moduleOptions,
        chipModel: props.chipName,
        originalPinInfo: pinInfo,
        pinInfoData: pinInfoData
      };

      console.log(`🎯 最终设置的功能类型: "${finalFunctionType}"`);
      console.log(`🎯 最终设置的具体类型: "${finalSpecificType}"`);
      console.log('🎯 引脚编辑器数据:', pinEditorData);

      console.log('更新后的引脚编辑器数据:', pinEditorData);

      // 🔍 调试信息：检查数据设置
      console.log('🔍 调试 - formData.module 将被设置为:', finalFunctionType);
      console.log('🔍 调试 - formData.type 将被设置为:', finalSpecificType);

      // 更新表单数据
      Object.keys(formData).forEach(key => {
        delete formData[key];
      });
      Object.assign(formData, pinEditorData);

      // 🔍 验证数据设置结果
      console.log('🔍 验证 - formData.module 实际值:', formData.module);
      console.log('🔍 验证 - formData.type 实际值:', formData.type);
      console.log('🔍 验证 - formData.pinType 实际值:', formData.pinType);

      // 发送数据加载完成事件
      emit('pin-data-loaded', {
        pinId,
        pinData: pinEditorData,
        pinInfo,
        moduleOptions
      });

      // 🎯 解析并设置相关引脚选择
      await parseAndSetRelatedPins(pinInfoData, finalFunctionType);

      // 如果引脚已有功能类型，自动请求模块配置
      if (finalFunctionType && finalFunctionType !== '') {
        console.log(`🎯 引脚 ${pinId} 已配置为 ${finalFunctionType}，自动请求模块配置`);
        await handleRequestModuleConfig(finalFunctionType);
      } else {
        console.log(`🎯 引脚 ${pinId} 没有功能类型，清空模块配置`);
        localModuleConfig.value = [];
      }

    } else {
      console.error('获取引脚编辑器数据失败:', response.data);
      messageManager.error('获取引脚编辑器数据失败: ' + (response.data?.message || '未知错误'));
    }
  } catch (error) {
    console.error('获取引脚编辑器数据异常:', error);
    messageManager.error('获取引脚编辑器数据失败: ' + error.message);
  } finally {
    loading.value = false;
  }
};

// 🎯 解析并设置相关引脚选择
const parseAndSetRelatedPins = async (pinInfoData, functionType) => {
  try {
    console.log('🔍 开始解析相关引脚选择数据:', pinInfoData);

    // 查找 pinInfo 字段
    const pinInfoField = pinInfoData.find(field => field.key === 'PinInfo');
    if (!pinInfoField || !pinInfoField.value) {
      console.log('📝 没有找到 pinInfo 字段，无相关引脚');
      selectedPins.value = [];
      return;
    }

    let pinInfoList = pinInfoField.value;
    if (typeof pinInfoList === 'string') {
      try {
        pinInfoList = JSON.parse(pinInfoList);
      } catch (e) {
        console.warn('⚠️ pinInfo 字段解析失败:', e);
        selectedPins.value = [];
        return;
      }
    }

    if (!Array.isArray(pinInfoList)) {
      console.warn('⚠️ pinInfo 不是数组格式:', pinInfoList);
      selectedPins.value = [];
      return;
    }

    console.log('📋 解析到的 pinInfo 列表:', pinInfoList);

    // 提取相关引脚ID（排除当前引脚）
    const currentPinId = formData.pin_id?.toString();
    const relatedPinIds = pinInfoList
      .filter(pinInfo => pinInfo.pin_id?.toString() !== currentPinId)
      .map(pinInfo => pinInfo.pin_id?.toString())
      .filter(pinId => pinId); // 过滤掉空值

    console.log(`🎯 当前引脚: ${currentPinId}, 相关引脚: [${relatedPinIds.join(', ')}]`);

    // 设置相关引脚选择
    selectedPins.value = relatedPinIds;

    // 🎯 如果有相关引脚且有功能类型，请求其他引脚的类型信息
    // 注意：不要覆盖已经从PinInfo解析出来的正确类型信息
    if (relatedPinIds.length > 0 && functionType) {
      console.log('🔄 请求其他引脚的类型信息（保留PinInfo中的类型信息）');
      await requestPinTypeInfo();
    }

  } catch (error) {
    console.error('❌ 解析相关引脚选择失败:', error);
    selectedPins.value = [];
  }
};

// 🎯 请求引脚类型信息（用于引脚选择区显示）
const requestPinTypeInfo = async () => {
  try {
    console.log('🔄 开始请求引脚类型信息');

    // 获取当前功能类型
    const currentModule = formData.module;
    if (!currentModule) {
      console.warn('⚠️ 当前功能类型为空，无法请求类型信息');
      return null;
    }

    // 先请求一次获取当前功能类型的可用类型选项
    const response = await http.get('/code_management/chip_modul', {
      params: {
        project_code: props.projectCode,
        gitlab: props.gitlab,
        project_branch: props.projectBranch,
        branch_status: props.branchStatus,
        chip_name: props.chipName,
        pin_number: formData.pin_id, // 使用当前引脚作为参考
        module: currentModule
      }
    });

    console.log(`✅ 功能类型 ${currentModule} 的类型信息请求成功:`, response.data);

    if (response.data && response.data.status === 1) {
      const moduleInfo = response.data.data?.module || [];

      // 从 module_info 中找到 type 字段
      const typeField = moduleInfo.find(field => field.key === 'type');

      if (typeField && typeField.options && Array.isArray(typeField.options)) {
        console.log(`📋 功能类型 ${currentModule} 的可用类型选项:`, typeField.options);

        // 获取所有可用引脚
        const availablePins = props.allPinsData?.filter(pin => {
          const pinId = pin.pinId?.toString() || pin.pinNumber?.toString();
          const currentPinId = formData.pin_id;

          // 排除当前正在编辑的引脚
          if (pinId === currentPinId?.toString()) {
            return false;
          }

          // 只处理未配置的引脚
          const isUnconfigured = !pin.model || pin.model === 'Null' || pin.model === '';
          return isUnconfigured;
        }) || [];

        console.log(`🎯 为 ${availablePins.length} 个引脚分配类型信息`);

        const typeInfoMap = {};

        // 🎯 为选中的引脚分配类型，优先处理选中的引脚
        const selectedPinIds = selectedPins.value || [];
        let typeIndex = 0;

        // 首先为选中的引脚分配类型
        selectedPinIds.forEach(pinId => {
          // 跳过已经有类型信息的引脚（从PinInfo解析出来的）
          if (!pinTypeInfoCache.value[pinId]) {
            const typeOption = typeField.options[typeIndex % typeField.options.length];
            const assignedType = typeOption?.value || typeOption?.label;

            if (assignedType) {
              typeInfoMap[pinId] = assignedType;
              typeIndex++;
              console.log(`📋 为选中引脚 ${pinId} 分配类型: ${assignedType}`);
            }
          }
        });

        // 然后为其他可用引脚分配剩余的类型
        availablePins.forEach(pin => {
          const pinId = pin.pinId?.toString() || pin.pinNumber?.toString();

          // 跳过已经处理过的引脚
          if (!typeInfoMap[pinId] && !pinTypeInfoCache.value[pinId]) {
            const typeOption = typeField.options[typeIndex % typeField.options.length];
            const assignedType = typeOption?.value || typeOption?.label;

            if (assignedType) {
              typeInfoMap[pinId] = assignedType;
              typeIndex++;
              console.log(`📋 为可用引脚 ${pinId} 分配类型: ${assignedType}`);
            }
          }
        });

        // 🎯 合并到现有缓存，不覆盖已有的类型信息
        Object.keys(typeInfoMap).forEach(pinId => {
          // 只有当缓存中没有该引脚的类型信息时，才添加新的类型信息
          if (!pinTypeInfoCache.value[pinId]) {
            pinTypeInfoCache.value[pinId] = typeInfoMap[pinId];
          }
        });
        console.log('📋 引脚类型信息缓存合并更新:', pinTypeInfoCache.value);

        return pinTypeInfoCache.value;
      }
    }

    console.log('⚠️ 未找到类型字段或类型选项');
    return pinTypeInfoCache.value;
  } catch (error) {
    console.error('❌ 请求引脚类型信息异常:', error);
    return null;
  }
};

// ❌ 已移除：handleModuleConfigChange 方法
// 原因：移除数据变动监视器后，此方法不再使用

// ❌ 已移除：sendConfigChangeRequest 方法
// 原因：移除数据变动监视器后，此方法不再使用
// 现在只使用 handleSaveConfig 方法进行配置保存

// 设置模块配置数据
const setModuleConfig = (config, fromCache = false, cachedFormData = null) => {
  try {
    console.log('🎯 开始设置模块配置:', config);
    console.log('🔍 配置数据类型:', typeof config);
    console.log('🔍 是否为数组:', Array.isArray(config));
    console.log('🏷️ 配置来源:', fromCache ? '缓存' : 'API');

    if (!config) {
      console.warn('⚠️ 模块配置为空，清空本地配置');
      localModuleConfig.value = [];
      return;
    }

    let processedConfig = [];

    if (Array.isArray(config)) {
      processedConfig = config.map(item => {
        if (typeof item === 'object' && item !== null) {
          return {
            key: item.key || item.name,
            label: item.label || item.display || item.name,
            type: item.type || 'text',
            options: item.options || [],
            placeholder: item.placeholder || item.default_value,
            min: item.min,
            max: item.max,
            step: item.step,
            precision: item.precision
          };
        }
        return item;
      });
    } else if (typeof config === 'object') {
      processedConfig = Object.keys(config).map(key => ({
        key: key,
        label: config[key].label || key,
        type: config[key].type || 'text',
        options: config[key].options || [],
        placeholder: config[key].placeholder || config[key].default_value,
        min: config[key].min,
        max: config[key].max,
        step: config[key].step,
        precision: config[key].precision
      }));
    }

    console.log('🔧 处理后的模块配置:', processedConfig);
    localModuleConfig.value = processedConfig;

    // 设置字段的默认值
    processedConfig.forEach(field => {
      // 如果字段在 formData 中没有值或为空，则设置默认值
      if (formData[field.key] === undefined || formData[field.key] === null || formData[field.key] === '') {
        if (field.placeholder !== undefined && field.placeholder !== null && field.placeholder !== '') {
          if (field.type === 'number') {
            const numValue = Number(field.placeholder);
            if (!isNaN(numValue)) {
              formData[field.key] = numValue;
              console.log(`🎯 设置数字字段 ${field.key} 默认值:`, numValue);
            }
          } else if (field.type === 'select') {
            const validOption = field.options?.find(opt => opt.value === field.placeholder);
            if (validOption) {
              formData[field.key] = field.placeholder;
              console.log(`🎯 设置选择字段 ${field.key} 默认值:`, field.placeholder);
            }
          } else {
            formData[field.key] = field.placeholder;
            console.log(`🎯 设置文本字段 ${field.key} 默认值:`, field.placeholder);
          }
        }
      }
    });

    // 如果有缓存的表单数据，恢复字段值（覆盖默认值）
    if (fromCache && cachedFormData) {
      console.log('🔄 从缓存恢复表单数据:', cachedFormData);
      processedConfig.forEach(field => {
        if (cachedFormData[field.key] !== undefined) {
          formData[field.key] = cachedFormData[field.key];
          console.log(`✅ 恢复字段 ${field.key}:`, cachedFormData[field.key]);
        }
      });
    }

    console.log('✅ 模块配置设置完成，当前localModuleConfig:', localModuleConfig.value);
  } catch (error) {
    console.error('❌ 设置模块配置失败:', error);
    localModuleConfig.value = [];
  }
};

// 监听 pinData 变化
watch(() => props.pinData, (newPinData) => {
  console.log('🔄 pinData 发生变化:', newPinData);
  initializeFormData();
}, { deep: true, immediate: true });

// 监听 pinInfo 变化
watch(() => props.pinInfo, (newPinInfo) => {
  console.log('🔄 pinInfo 发生变化:', newPinInfo);
  initializeFormData();
}, { deep: true });

// 监听表单数据变化，特别是SPI相关字段
watch(() => formData, (newData) => {
  if (newData.module === 'SPI') {
    console.log('🔍 SPI模块数据变化:', newData);
  }
}, { deep: true });

// 🎯 专门监控SPI类型字段的变化
watch(() => {
  // 监控所有可能的SPI类型字段
  const spiTypeFields = ['SPI类型', 'spi_type', 'spi类型'];
  let spiTypeValue = null;

  spiTypeFields.forEach(fieldName => {
    if (formData[fieldName] !== undefined) {
      spiTypeValue = formData[fieldName];
    }
  });

  return spiTypeValue;
}, (newSpiTypeValue, oldSpiTypeValue) => {
  console.log('🔍 SPI类型字段监控:', {
    newValue: newSpiTypeValue,
    oldValue: oldSpiTypeValue,
    module: formData.module
  });

  // 只在SPI模块下处理
  if (formData.module === 'SPI' && newSpiTypeValue === '硬件SPI') {
    console.log('🎯 检测到SPI类型设置为硬件SPI，开始处理软件SPI类型字段');

    // 查找软件SPI类型字段并设置
    const softwareSpiTypeFields = ['软件SPI类型', 'software_spi_type', '软件spi类型'];
    let foundSoftwareField = false;

    // 在模块配置中查找软件SPI类型字段
    localModuleConfig.value.forEach(field => {
      const isSoftwareSpiTypeField = softwareSpiTypeFields.some(fieldName =>
        field.key === fieldName ||
        field.label === fieldName ||
        field.label.includes('软件SPI类型') ||
        field.label.includes('软件spi类型')
      );

      if (isSoftwareSpiTypeField) {
        console.log('🔧 找到软件SPI类型字段:', field.key, field.label);

        // 清除历史值并设置为三线SPI
        formData[field.key] = '三线SPI';
        foundSoftwareField = true;

        console.log('✅ 软件SPI类型已设置为三线SPI:', field.key);
      }
    });

    if (!foundSoftwareField) {
      console.log('⚠️ 未找到软件SPI类型字段');
    }
  }
}, { immediate: false });

// 🎯 监听引脚选择区显示状态，自动请求引脚类型信息
watch(shouldShowPinSelection, async (newValue) => {
  if (newValue) {
    console.log('🎯 引脚选择区显示，开始请求引脚类型信息');
    await requestPinTypeInfo();
  }
}, { immediate: true });

// 监听 moduleConfig 变化
watch(() => props.moduleConfig, (newModuleConfig) => {
  console.log('🔄 moduleConfig 发生变化:', newModuleConfig);
  if (newModuleConfig && Array.isArray(newModuleConfig)) {
    setModuleConfig(newModuleConfig, false);
  }
}, { deep: true });

// 组件挂载时初始化
onMounted(() => {
  console.log('🚀 DynamicPinEditor 组件已挂载');
  initializeFormData();
});

// 暴露方法给父组件
defineExpose({
  // 🎯 主要数据获取方法 - 父组件需要调用
  getPinEditorData,

  // 🎯 数据访问 - 父组件可能需要
  formData,
  loading

  // ❌ 已移除不必要的暴露方法：
  // - initializeFormData: 通过 watch props 自动调用
  // - setModuleConfig: 通过 watch props.moduleConfig 自动调用
  // - handleRequestModuleConfig: 通过功能类型变化自动触发
  // - sendConfigChangeRequest: 已移除数据变动监视器，不再使用
  // - handleModuleConfigChange: 通过事件机制处理
  // - requestPinTypeInfo: 通过 watch shouldShowPinSelection 自动触发
});
</script>

<style scoped>
.pin-editor-panel {
  height: 100%;
  flex: 1; /* 占满父容器的剩余空间 */
  display: flex;
  flex-direction: column;
  background: transparent; /* 移除背景，使用外层容器的背景 */
  /* border-radius: 8px; */ /* 移除边框圆角，使用外层容器的 */
  /* box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1); */ /* 移除阴影，使用外层容器的 */
  overflow: hidden;
  transition: all 0.3s ease;
}

/* 🎨 引脚编辑器悬停动画效果 */
.pin-editor-panel:hover {
  transform: translateY(-2px);
}

.pin-editor-panel:hover .panel-content {
  background: linear-gradient(135deg, #ffffff 0%, #f8faff 100%);
}

.panel-header {
  padding: 5px 20px;
  background: #fff;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.panel-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
  gap: 8px;
}

.panel-content {
  flex: 1;
  padding: 20px;
  overflow: visible;
  min-height: auto; /* 允许内容自动扩展 */
  height: auto; /* 根据内容自适应高度 */
}

.form-left-align {
  width: 100%;
}

.section-header {
  margin-bottom: 20px;
  transition: all 0.3s ease;
}

/* 🎨 分割线悬停动画效果 */
.section-header:hover {
  transform: translateX(8px);
}

.section-header:hover .section-title {
  color: #409eff;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #666;
  display: flex;
  align-items: center;
  gap: 6px;
  background: transparent;
  border: none;
  box-shadow: none;
}

.config-section {
  margin-bottom: 20px;
}

.config-section:last-child {
  margin-bottom: 0;
}

.action-buttons {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #e9ecef;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

:deep(.el-form-item) {
  margin-bottom: 16px;
  transition: all 0.3s ease;
  border-radius: 8px;
  padding: 8px;
}

/* 🎨 表单项悬停动画效果 */
:deep(.el-form-item:hover) {
  background: rgba(64, 158, 255, 0.05);
  transform: translateX(4px);
}

:deep(.el-form-item__label) {
  color: #606266;
  font-weight: 500;
}

:deep(.el-input__inner) {
  border-radius: 4px;
  transition: all 0.3s ease;
}

:deep(.el-input:hover .el-input__inner) {
  border-color: #409eff;
  box-shadow: 0 0 8px rgba(64, 158, 255, 0.2);
  transform: scale(1.02);
}

:deep(.el-select) {
  width: 100%;
  transition: all 0.3s ease;
}

:deep(.el-select:hover .el-input__inner) {
  border-color: #409eff;
  box-shadow: 0 0 8px rgba(64, 158, 255, 0.2);
  transform: scale(1.02);
}

:deep(.el-input-number) {
  transition: all 0.3s ease;
}

:deep(.el-input-number:hover .el-input__inner) {
  border-color: #409eff;
  box-shadow: 0 0 8px rgba(64, 158, 255, 0.2);
  transform: scale(1.02);
}

:deep(.el-divider__text) {
  background-color: #fff;
  padding: 0 16px;
}

:deep(.el-divider--horizontal) {
  margin: 12px 0;
}

/* 设置表单项标签加粗 */
:deep(.el-form-item__label) {
  font-weight: bold;
}

/* 基本信息和模块配置背景色 */
.basic-info-section {
  background-color: #f5f7fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.module-config-section {
  background-color: #f5f7fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

/* 🎨 保存配置按钮样式 */
.button-group {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  align-items: center;
}

/*  引脚选择器样式 */
.pin-selection-section {
  margin-bottom: 20px;
  /* padding: 20px; */
  background-color: #fff;
  border: 1px solid #fff;
  border-radius: 8px;
}

.pin-selection-form-item {
  margin-bottom: 0;
  transition: all 0.3s ease;
}

/* 🎨 引脚选择区域悬停动画效果 */
.pin-selection-form-item:hover {
  transform: translateY(-2px);
}

.pin-selection-form-item:hover .pin-selection-container {
  box-shadow: 0 8px 24px rgba(64, 158, 255, 0.15);
}

.pin-selection-form-item :deep(.el-form-item__label) {
  display: flex;
  align-items: center;
  height: 100%;
  padding-top: 0;
  line-height: normal;
}

.pin-selection-container {
  width: 100%;
  flex: 1;
  transition: all 0.3s ease;
  border-radius: 8px;
}

.pin-selection-header {
  margin-bottom: 15px;
}

.selection-title {
  font-size: 14px;
  font-weight: 600;
  color: #409eff;
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

/* 🎯 并排显示的统计信息 */
.selection-stats {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

/* 🎯 统一样式的统计信息框 */
.available-count,
.max-selection-hint {
  font-size: 12px;
  padding: 6px 12px;
  border-radius: 12px;
  font-weight: 500;
  min-width: 80px;
  text-align: center;
  display: inline-block;
  line-height: 1.2;
}

.available-count {
  color: #409eff;
  background-color: #ecf5ff;
  border: 1px solid #b3d8ff;
}

.max-selection-hint {
  color: #f56c6c;
  background-color: #fef0f0;
  border: 1px solid #fbc4c4;
}

/* 🎯 选择进度指示器（固定在可滑动区域外） */
.selection-progress {
  margin: 15px 0;
  padding: 12px 15px;
  background-color: #f0f9ff;
  border-radius: 8px;
  border: 1px solid #bfdbfe;
  position: relative;
  z-index: 1;
}

.progress-text {
  font-size: 13px;
  color: #409eff;
  font-weight: 600;
  margin-bottom: 10px;
  display: block;
  text-align: center;
}

/* � 引脚选择区域 - 蓝白风格，带下拉滑块 */
.pin-checkbox-list {
  max-height: 200px;
  overflow-y: auto;
  padding: 16px;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  margin-top: 16px;
}



/* 🎨 默认浅灰滚动条样式 */
.pin-checkbox-list::-webkit-scrollbar {
  width: 6px;
}

.pin-checkbox-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.pin-checkbox-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.pin-checkbox-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 🎨 复选框网格布局 - 蓝白风格 */
.pin-checkbox-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 12px;
}

/* 🎨 复选框项目样式 - 简洁风格，只保留选中效果 */
.pin-checkbox-item {
  margin: 0;
  padding: 10px 14px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  background: #ffffff;
  transition: all 0.3s ease;
}

.pin-checkbox-item.is-checked {
  border-color: #409eff;
  background: #ecf5ff;
}

.pin-checkbox-item.is-disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: #f5f7fa;
  border-color: #dcdfe6;
}

/* 🎨 复选框标签样式 - 简洁风格 */
.pin-label {
  font-size: 11px;
  color: #606266;
  margin-left: 8px;
}

.pin-checkbox-item.is-checked .pin-label {
  color: #409eff;
  font-weight: 600;
}

.pin-checkbox-item.is-disabled .pin-label {
  color: #c0c4cc;
}

/* 🎨 无可用引脚提示样式 - 蓝白风格 */
.no-pins-available {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24px;
  color: #909399;
  font-size: 15px;
  font-weight: 600;
  background: linear-gradient(135deg, #f5f7fa 0%, #e4e7ed 100%);
  border-radius: 12px;
  border: 2px dashed rgba(144, 147, 153, 0.3);
}

/* 配置修改弹框样式 */
.chip-request-dialog {
  --el-dialog-border-radius: 12px;
}

.chip-request-dialog .el-dialog {
  border-radius: 12px;
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

.chip-request-dialog .el-dialog__header {
  display: none;
}

.chip-request-dialog .el-dialog__body {
  padding: 0;
}

.request-dialog-content {
  padding: 32px 24px 24px;
  text-align: center;
  background: linear-gradient(135deg, #f8faff 0%, #ffffff 100%);
}

.request-dialog-header {
  margin-bottom: 24px;
}

.request-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 64px;
  height: 64px;
  background: linear-gradient(135deg, #409eff 0%, #fff 100%);
  border-radius: 50%;
  margin-bottom: 16px;
  box-shadow: 0 4px 16px rgba(64, 158, 255, 0.3);
}

.request-icon .el-icon {
  font-size: 28px;
  color: white;
}

.rotating-icon {
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.request-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  line-height: 1.4;
}

.request-dialog-body {
  margin-bottom: 24px;
}

.request-info {
  background: #f4f7ff;
  border: 1px solid #e1e8ff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
}

.info-item {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 8px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.info-value {
  font-size: 14px;
  color: #409eff;
  font-weight: 600;
  background: #ecf5ff;
  padding: 2px 8px;
  border-radius: 4px;
}

.request-message {
  font-size: 14px;
  color: #909399;
  line-height: 1.6;
  padding: 0 8px;
}

.request-progress {
  margin-top: 20px;
}

.request-progress .el-progress {
  margin-bottom: 0;
}

.request-progress .el-progress__text {
  display: none;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .chip-request-dialog .el-dialog {
    width: 90% !important;
    margin: 0 auto;
  }

  .request-dialog-content {
    padding: 24px 16px 16px;
  }

  .request-icon {
    width: 56px;
    height: 56px;
  }

  .request-icon .el-icon {
    font-size: 24px;
  }

  .request-title {
    font-size: 18px;
  }
}


</style>
