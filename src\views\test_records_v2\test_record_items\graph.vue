<template>
    <div v-if="['对比度测试', '9点亮度', '9点色度', '均一性测试', 'FlickerTest'].includes(testFunction)">
        <div style="display: flex;justify-content: center;width: 100%;">
            <h3>{{ gt }}</h3>
        </div>
        <el-table :data="data" stripe style="height: 500px;">
            <el-table-column v-for="k in header" :prop="k" :label="k" align="center"></el-table-column>
        </el-table>
    </div>
    <div v-else>
        <div
            v-if="gt != undefined && show && gt !== 'EnvTemperatureTest' && gt !== 'RecordImageAlgorithm' && gt !== 'Flicker测试'">
            <div style="display: flex;justify-content: center;width: 100%;">
                <h3>{{ gt }}</h3>
            </div>
            <el-row :gutter="10">
                <el-col :span="9">
                    <el-table :data="data" stripe style="height: 500px;">

                        <el-table-column v-for="k in header" :prop="k" :label="k" align="center"></el-table-column>

                    </el-table>
                </el-col>
                <el-col :span="15">

                    <EchartsComponent :options="options" height="500px" />

                </el-col>
            </el-row>

        </div>
        <div v-else-if="gt == 'RecordImageAlgorithm'">
            <div style="display: flex;justify-content: center;width: 100%;">
                <h3>{{ gt }}</h3>
            </div>
            <el-table :data="data" stripe style="height: 500px;">
                <el-table-column v-for="k in header" :label="k" align="center">
                    <template #default="{ row }">
                        <span v-if="k == '状态'">
                            <template v-for="item in row[k]">
                                <el-tag v-if="item == 'NG'" type="danger" style="margin-right: 3px;">
                                    {{ item }}
                                </el-tag>
                                <el-tag v-else-if="item == 'OK'" type="success" style="margin-right: 3px;">
                                    {{ item }}
                                </el-tag>
                            </template>
                        </span>
                        <span v-else-if="k == '路径'">
                            <template v-for="item in row[k]">
                                {{ item }}
                            </template>
                        </span>
                        <span v-else>
                            {{ row[k] }}
                        </span>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <div v-else-if="gt === 'Flicker测试'">
            <div style="display: flex; justify-content: center; width: 100%;">
                <h3>{{ gt }}</h3>
            </div>
            <el-table :data="flickerData" stripe style="height: 500px;">
                <!-- 动态生成列 -->
                <el-table-column v-for="(item, index) in flickerData[0]?.label" :key="index" :label="item"
                    align="center" width="100">
                    <template v-slot="scope">
                        {{ scope.row.value[index] }}
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <div v-else>
            <GraphDifferent :graph_data="props.graph_data" v-if="gt == 'EnvTemperatureTest'" />
        </div>
    </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import EchartsComponent from '@/components/echartsComponent.vue';
import GraphDifferent from './graphDifferent.vue';

const props = defineProps({
    graph_data: {
        type: Object,
        required: true,
    }
})

const show = ref(true);

const gt = computed(() => {
    const testFunction = props.graph_data.test_function;
    switch (testFunction) {
        case "Gamma曲线测试":
            return "Gamma曲线【黄色标准线Gamma2.0为最低标准，红色标准线Gamma2.4为最高标准】";
        case "亮度曲线测试":
            return "亮度曲线";
        case "LightSensorAutoTest":
            return "光感曲线";
        case "FlickerTest":
            return "Flicker测试";
        default:
            return testFunction;
    }
})

const testFunction = computed(() => {
    return props.graph_data.test_function;
});

const header = computed(() => {
    return props.graph_data.header || props.graph_data.head;
});

const data = computed(() => {
    if (props.graph_data.test_function == undefined) {
        return [];
    }

    let header = props.graph_data.header || props.graph_data.head;
    let content = props.graph_data.content;

    return content.map(item => {
        let obj = {};
        header.forEach((key, index) => {
            obj[key] = item[index];
        });
        return obj;
    })

})

const options = computed(() => {

    if (props.graph_data.test_function == "EnvTemperatureTest") {
        let header = props.graph_data.header || props.graph_data.head;
        let content = props.graph_data.content;
        let indices = content.map(item => new Date(item[0]).getTime());

        const series = header.slice(1).map((key, idx) => ({
            name: key,
            type: 'line',
            data: content.map(item => ({
                value: [new Date(item[0]).getTime(), item[idx + 1]]
            })),
            smooth: true,
            showSymbol: false,

        }));

        let allYValues = [];
        series.forEach(serie => {
            serie.data.forEach(point => {
                allYValues.push(point.value[1]);
            });
        });
        const minY = Math.min(...allYValues);
        const maxY = Math.max(...allYValues);

        return {
            legend: {
                data: header.slice(1),
                top: '30' // 调整图例的位置
            },
            grid: {
                left: '5%',
                right: '5%',
                top: '20%',
                bottom: '5%',
                containLabel: true
            },
            xAxis: {
                type: 'time',
                data: indices,
            },
            yAxis: {
                type: 'value',
                min: minY - 1,
                max: maxY + 1,
                axisLabel: {
                    formatter: '{value} ℃' // 在这里设置单位，如 '{value} ℃' 或 '{value} kg'
                },
            },
            series
        }
    }
    else if (props.graph_data.test_function == "Gamma曲线测试") {
        let content = props.graph_data.content;
        let data = content.map(item => item[4])
        let indices = data.map((_, index) => index);

        return {
            grid: {
                left: '5%',
                right: '5%',
                top: '5%',
                bottom: '5%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: indices,
            },
            yAxis: {
                type: 'value'
            },
            series: [
                {
                    data,
                    type: 'line',
                    smooth: true,
                    lineStyle: {
                        color: 'green'
                    },
                    showSymbol: false,
                    markLine: {
                        symbol: 'none',
                        data: [
                            {
                                yAxis: 2.0,
                                lineStyle: {
                                    color: '#fac858',
                                    type: 'dashed',
                                    width: 2
                                }
                            },
                            {
                                yAxis: 2.4,
                                lineStyle: {
                                    color: 'red',
                                    type: 'dashed',
                                    width: 2
                                }
                            }
                        ]
                    }
                }
            ]
        };
    }
    else if (props.graph_data.test_function == "亮度曲线测试") {
        let content = props.graph_data.content;
        let data = content.map(item => item[3]);
        let indices = content.map(item => item[0]);

        return {
            grid: {
                left: '5%',
                right: '5%',
                top: '5%',
                bottom: '5%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: indices,
            },
            yAxis: {
                type: 'value',
                axisLabel: {
                    formatter: '{value} nit' // 在这里设置单位，如 '{value} ℃' 或 '{value} kg'
                }
            },
            series: [
                {
                    data,
                    type: 'line',
                    smooth: true,
                    lineStyle: {
                        color: 'green'
                    },
                    showSymbol: false,
                }
            ]
        }
    }
    else if (props.graph_data.test_function == "LightSensorAutoTest") {
        let header = props.graph_data.header;
        let content = props.graph_data.content;
        let indices = content.map(item => item[0]);

        const series = header.slice(1).map((key, idx) => ({
            name: key,
            type: 'line',
            data: content.map(item => item[idx + 1]),
            smooth: true,
            showSymbol: false,


        }));

        return {
            legend: {
                data: header.slice(1),
                top: '30' // 调整图例的位置
            },
            grid: {
                left: '5%',
                right: '5%',
                top: '20%',
                bottom: '5%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: indices,
            },
            yAxis: {
                type: 'value',
                axisLabel: {
                    formatter: '{value} Lux' // 在这里设置单位，如 '{value} ℃' 或 '{value} kg'
                }
            },
            series
        }
    }
    else {
        show.value = false;
        return {
            xAxis: {
                data: ['A', 'B', 'C', 'D', 'E']
            },
            yAxis: {},
            series: [
                {
                    data: [10, 22, 28, 23, 19],
                    type: 'line',
                    smooth: true
                }
            ]
        };
    }

});

const flickerData = computed(() => {
    if (props.graph_data.test_function !== 'Flicker测试') {
        return [];
    }

    let header = props.graph_data.header || props.graph_data.head;
    let content = props.graph_data.content.map(Number); // 将字符串转换为数字

    // 重新组织数据为两行形式
    let labels = header;
    let values = content;

    return [
        { label: labels, value: values }
    ];
});

</script>

<style lang="scss" scoped></style>
