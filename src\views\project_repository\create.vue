<template>
  <el-dialog 
    v-model="currentVisible"
    title="新建仓库" 
    width="800px"
    :close-on-click-modal="false"
    :append-to-body="true"  
    @close="handleCancel"
  >
    <!-- 公共表单部分 -->
    <el-form 
      :model="formData" 
      label-width="120px" 
      ref="createFormRef"
      class="custom-form"
    >
      <!-- 工程组 -->
      <el-form-item label="工程组" prop="engineeringGroup" class="form-item" required>
        <el-select 
          v-model="formData.engineeringGroup" 
          placeholder="请选择工程组" 
          allow-create
          filterable 
          clearable 
          style="width: 100%"
          @change="handleGroupChange"
        >
          <el-option v-for="group in teamOptions" :key="group" :label="group" :value="group" />
        </el-select>
      </el-form-item>

      <!-- 工程名称 -->
      <el-form-item label="工程名称" prop="projectName" class="form-item" required>
        <el-select
          v-model="formData.projectName"
          placeholder="请选择或输入工程名称"
          filterable
          allow-create
          clearable
          style="width: 100%"
          @change="handleProjectNameChange"
        >
          <el-option
            v-for="project in projectOptions"
            :key="project"
            :label="project"
            :value="project"
          />
        </el-select>
      </el-form-item>

      <!-- 工程描述 -->
      <el-form-item label="工程描述" prop="projectDesc" class="form-item">
        <el-input 
          v-model="formData.projectDesc" 
          type="text" 
          placeholder="请输入工程描述"
        />
      </el-form-item>

      <!-- 页签切换 -->
      <el-form-item label="创建方式" class="form-item">
        <el-radio-group  v-model="activeTab" @tab-click="handleTabChange" class="custom-radio-tabs">
          <el-radio-button label="sdk">SDK</el-radio-button>
          <el-radio-button label="custom">自定义</el-radio-button>
        </el-radio-group >
      </el-form-item>

      <!-- SDK创建内容 -->
      <template v-if="activeTab === 'sdk'">
        <el-form-item label="SDK地址" class="form-item" required>
          <el-input v-model="formData.sdkGitLab" disabled />
        </el-form-item>
        
        <!-- <el-form-item label="SDK分支" class="form-item">
          <el-select 
            v-model="formData.defaultBranch" 
            placeholder="请选择分支" 
            filterable 
            clearable 
            style="width: 100%"
            :disabled="!!formData.sdkVersion"
          >
            <el-option v-for="branch in branchOptions" :key="branch" :label="branch" :value="branch" />
          </el-select>
        </el-form-item> -->

        <el-form-item label="SDK版本" class="form-item">
          <el-select 
            v-model="formData.sdkVersion" 
            placeholder="请选择SDK版本" 
            filterable 
            clearable 
            style="width: 100%"
            :disabled="!!formData.defaultBranch"
          >
            <el-option v-for="version in sdkVersionOptions" :key="version" :label="version" :value="version" />
          </el-select>
        </el-form-item>
      </template>
    </el-form>

    <!-- 弹窗底部按钮 -->
    <template #footer>
      <el-button @click="handleCancel" class="cancel-btn">取消</el-button>
      <el-button type="primary" @click="handleCreateSubmit" class="create-btn">
        创建
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, defineProps, defineEmits, watch, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import http from '@/utils/http/http';


const props = defineProps({
  modelValue: { type: Boolean, default: false, required: true },
  project_code: { type: String, required: true },
  project_name: { type: String, required: true },
});

const emit = defineEmits(['update:modelValue', 'success', 'close', 'refresh', 'sdk-created','update:formLoading']);

// 状态
const currentVisible = ref(false);
const activeTab = ref('sdk');
const formLoading = ref(false);
const createFormRef = ref(null);

const dialogInitVisible = ref(false);
const functionalModules = ref([]);
const pickChips = ref([]);
const chipList = ref([]);

const teamOptions = ref([]);
const projectOptions = ref([]);
const branchOptions = ref(['main','master','dev']);
const sdkVersionOptions = ref([]);

const formData = reactive({
  engineeringGroup: '',
  projectName: '',
  projectDesc: '',
  sdkGitLab: 'http://*********/mcu-team/sdk_code/hiwaysdk_2.0',
  defaultBranch: '',
  sdkVersion: '',
  selectedModules: [],
});

// 消息
let currentMessage = null;

// 初始化表单数据
const initFormData = async () => {
  if (!teamOptions.value.length) {
    try {
      const res = await http.get('/code_management/get_team');
      if (res.data.status === 1) {
        teamOptions.value = res.data.subgroup_paths;
        currentMessage?.close();
        currentMessage = ElMessage.success('工程组数据加载完成');
      } else {
        currentMessage?.close();
        currentMessage = ElMessage.error('工程组数据加载失败');
      }
    } catch(e) {
      console.error(e);
      currentMessage?.close();
      currentMessage = ElMessage.error('工程组数据加载失败');
    }
  }
};

// 监听弹窗显示
watch(() => props.modelValue, async val => {
  currentVisible.value = val;
  if (val) await initFormData();
  else resetForm();
}, { immediate: true });

// 表单变化
const handleGroupChange = async (val) => {
  projectOptions.value = [];
  if (!val) return;
  try {
    const res = await http.get('/code_management/projects_by_group', { params: { team: val } });
    if (res.data.status === 1) projectOptions.value = res.data.subgroup_team || [];
  } catch (e) { console.error(e); }
};

const handleProjectNameChange = async (val) => {
  formData.sdkVersion = '';
  if (!val) return;
  get_branches();
};

const get_branches = async () => {
  try {
    const res = await http.get(`/code_management/get_branches`, { params: { sdkGitLab: formData.sdkGitLab } });
    if (res?.data?.status === 1) {
      branchOptions.value = res.data.branches;
      sdkVersionOptions.value = res.data.tags;
    } else {
      ElMessage.error("分支加载失败");
    }
  } catch(e){ console.error(e); }
};

// 取消
const handleCancel = () => {
  currentVisible.value = false;
  emit('update:modelValue', false);
  emit('close');
};

// 自定义创建
const create_custom_project = async () => {
  try {
    const res = await http.get('/code_management/config_submit', { params: {
      selected_group: formData.engineeringGroup,
      project_space: formData.projectName,
      project_description: formData.projectDesc,
      version_rule: formData.defaultBranch || formData.sdkVersion,
      project_code: props.project_code,
      project_name: props.project_name
    }});
    if (res.data.config_status === 1) {
      ElMessage.success(`创建仓库成功:${res.data.message}`);
      currentVisible.value = false;
      emit('update:formLoading', false);
      emit('update:modelValue', false);
      emit('success');
    } else ElMessage.error(`创建仓库失败:${res.data.message}`);
  } catch(e){ console.error(e); ElMessage.error('创建仓库失败'); }
};

// SDK 初始化
const openInitDialog = async () => {
  if (!formData.sdkGitLab.trim() || !formData.projectName || !formData.engineeringGroup || (!formData.defaultBranch && !formData.sdkVersion)) {
    currentMessage?.close();
    currentMessage = ElMessage.warning('请填写表单信息后再初始化');
    return false;
  }
  emit('update:formLoading', true);
  formLoading.value = true;
  
  try {
    const res = await retryRequest(() => http.post('/code_management/init_project', {
      params: {
        sdkGitLab: formData.sdkGitLab,
        sdkBranch: formData.defaultBranch,
        sdkTag: formData.sdkVersion,
        group: formData.engineeringGroup,
        space: formData.projectName,
        project_code: props.project_code,
        project_name: props.project_name,
        project_description: formData.projectDesc
      }
    }, {timeout:60000}), 1, 2000);
    
    if (res.data.init_status === 1) {
      functionalModules.value = res.data.func;
      pickChips.value = res.data.pick_chips;
      formData.selectedModules = pickChips.value.slice();
      chipList.value = res.data.chip_list;
      return true;
    } else {
      currentMessage?.close();
      currentMessage = ElMessage.error('初始化失败');
      return false;
    }
  } catch(e) { 
    console.error(e); 
    currentMessage?.close(); 
    currentMessage = ElMessage.error('初始化失败'); 
    return false;
  } finally {
    emit('update:formLoading', false);
    formLoading.value = false;
  }
};

async function retryRequest(fn, retries = 3, delay = 1000) {
  for (let i=1;i<=retries;i++){
    try { return await fn(); }
    catch(e){ if(i<retries) await new Promise(res=>setTimeout(res,delay)); else throw e; }
  }
}

// 创建提交
const handleCreateSubmit = async () => {
    emit('update:formLoading', true);
    formLoading.value = true;
  try {
    if (activeTab.value === "custom") {
      await nextTick();
      await create_custom_project();
      return;
    }
    if (activeTab.value === "sdk") {
      if (!formData.engineeringGroup || !formData.projectName || !formData.projectDesc) {
        ElMessage.warning('请完善工程组、工程名称和工程描述信息');
        return;
      }
      await nextTick();
      // 调用初始化方法并等待结果
      const initResult = await openInitDialog();
      // 向父组件传递表单数据
      emit('sdk-created', {
        formData: { ...formData },
        functionalModules: functionalModules.value,
        pickChips: pickChips.value,
        chipList: chipList.value
      });
      // 关闭当前弹窗
      currentVisible.value = false;
      emit('update:modelValue', false);
    }
  } catch(e){ 
    console.error(e); 
    ElMessage.error('初始化失败'); 
  } finally{ 
    emit('update:formLoading', false);
    formLoading.value = false; 
  }
};

const handleTabChange = () => {
  resetFormValidation();
};

// 表单重置
const resetForm = () => {
  createFormRef.value?.resetFields();
  activeTab.value = 'sdk';
  projectOptions.value = [];
  sdkVersionOptions.value = [];
  formData.defaultBranch = '';
  formData.sdkVersion = '';
  formData.sdkGitLab = 'http://*********/mcu-team/sdk_code/hiwaysdk_2.0';
};

// 15. 重置表单验证
const resetFormValidation = () => {
  createFormRef.value?.clearValidate();
};

</script>

<style scoped>
/* 1. 表单整体样式：紧凑间距 */
.custom-form {
  width: 100%;
  padding: 20px;
  box-sizing: border-box;
}

/* 2. 表单项样式：统一间距 */
.form-item {
  text-align: center;
  justify-content: center;
  min-height: 40px; /* 最小高度，确保单选组有足够空间 */
  padding: 10px;
  margin-bottom: 15px; /* 增加底部间距，避免与其他表单项重叠 */
  border: none;
}

/* 3. 去除item的阴影效果 */
.form-item:hover {
  box-shadow: none !important;
}

:deep(.form-item .el-form-item__label) {
  text-align: left;  
  padding-right: 15px;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  height: 30px;
}


/* 2. 表单内容区：移除固定高度，允许自适应 */

:deep(.form-item .el-form-item__content) {
  margin-left: 30px !important;  /* 与label-width保持一致 */
  height: 30px;
  text-align: left;  /* 右侧内容左对齐 */
}

.custom-radio-tabs {
  display: inline-flex;
  border-radius: 4px;
  overflow: hidden; /* 保证圆角不被内部元素破坏 */
}

/* 按钮通用样式 */
:deep(.el-radio-button__inner) {
  border: 1px solid #409eff;
  background-color: #fff;
  color: #409eff;
  width: 120px;
  font-size: 14px;
  padding: 6px 20px;
  border-radius: 0; /* 默认无圆角，由外层控制 */
  transition: all 0.2s;
}

/* 选中状态 */
:deep(.el-radio-button.is-checked .el-radio-button__inner) {
  background-color: #409eff;
  color: #fff;
  border-color: #409eff;
}

/* 左右圆角修复：第一个和最后一个按钮 */
:deep(.el-radio-button:first-child .el-radio-button__inner) {
    border: 1px solid #409eff;
    border-radius: 4px 0 0 4px;
}
:deep(.el-radio-button:last-child .el-radio-button__inner) {
  border-radius: 0 4px 4px 0;
}


</style>
