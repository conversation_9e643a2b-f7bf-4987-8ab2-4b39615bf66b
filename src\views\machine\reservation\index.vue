<template>
<div style="display: flex; flex-direction: column; height: calc(104.9vh - 250px);">
    <div class="tool-bar-container" style="display: flex;">
        <div style="margin-left: auto; display: flex; gap: 10px;">
            <el-tooltip class="box-item" effect="dark" content="重置已添加的筛选条件" placement="top-start">
                <el-button text bg @click="handleReset">重置</el-button>
            </el-tooltip>
            <filterButton @click="onFilterStatusChange" :count="filterCount" />
            <el-button icon="Refresh" text bg @click="handleRefresh">刷新</el-button>
        </div>
    </div>

    <div class="filter-container" v-if="showFilterContainer">
        <el-input v-model="form.machine_name_re" placeholder="请输入机台名称" @keyup.enter="onFilter" clearable>
            <template #append>
                <el-button icon="Search" @click="onFilter"></el-button>
            </template>
        </el-input>
        <el-input v-model="form.username_re" placeholder="请输入预约人" @keyup.enter="onFilter" clearable>
            <template #append>
                <el-button icon="Search" @click="onFilter"></el-button>
            </template>
        </el-input>
    </div>

    <el-table :data="tableData" stripe border style="width: 100%;flex: 1;" class="table-container">

        <el-table-column prop="machine_name" label="机台名称" min-width="180" align="center"></el-table-column>
        <el-table-column prop="user" label="预约人" min-width="120" align="center"></el-table-column>
        <el-table-column prop="start_time" label="开始时间" min-width="200" align="center"></el-table-column>
        <el-table-column prop="end_time" label="结束时间" min-width="200" align="center"></el-table-column>
        <el-table-column prop="project" label="项目" min-width="200" align="center"></el-table-column>
        <el-table-column prop="content" label="测试内容" min-width="200" align="center"></el-table-column>
        <el-table-column prop="executive_personnel_name" label="执行人员" min-width="120" align="center"></el-table-column>

        <el-table-column label="预约状态" min-width="130" align="center">
            <template #default="{ row }">
                <el-tag v-if="row.status == 0" type="info">审核中</el-tag>
                <el-tag v-else-if="row.status == 1" type="primary">已批准</el-tag>
                <el-tag v-else-if="row.status == 2" type="danger">已取消</el-tag>
                <el-tag v-else-if="row.status == 3" type="success">已签到</el-tag>
                <el-tag v-else-if="row.status == 4" type="danger">超期未签到释放</el-tag>
                <el-tag v-else-if="row.status == 5" type="primary">已结束</el-tag>
                <el-tag v-else-if="row.status == 6" type="danger">超期未批准释放</el-tag>
                <el-tag v-else type="danger">未知</el-tag>
            </template>
        </el-table-column>

        <el-table-column label="使用模式" min-width="130" align="center">
            <template #default="{ row }">
                <el-tag v-if="row.t_mode == 'DEBUG'" type="success">调试模式</el-tag>
                <el-tag v-else type="success">测试模式</el-tag>
            </template>
        </el-table-column>

        <el-table-column label="操作" min-width="180" fixed="right" align="center">
            <template #default="{ row }">
                <div style="display: flex; justify-content: center;">
                    <el-button type="primary" size="small" @click="handleApprove(row)">批准</el-button>
                    <el-button type="primary" size="small" @click="handleCheckin(row)">签到</el-button>
                    <el-button type="danger" size="small" @click="handleCancel(row)" v-if="userStore.user_info.employee_number == row.employee_number">取消</el-button>
                </div>
            </template>
        </el-table-column>

    </el-table>
</div>

    <div class="pagination-container">
        <el-pagination :page-sizes="[1, 10, 15, 20, 25, 50, 100]" layout="prev, pager, next, jumper, total, sizes"
            v-model:current-page="form.page" v-model:page-size="form.pagesize" :total="total" background
            @change="onPageChange" />
    </div>

    <el-dialog v-if="dialogEditVisible" v-model="dialogEditVisible" title="编辑项目" width="800"
        :close-on-click-modal="false">
        <Edit @affirm="onEditAffirm" @cancel="onEditCancel" :r_id="r_id" />
    </el-dialog>

</template>


<script setup>

import { ref, reactive, watch, onMounted } from 'vue';
import http from '@/utils/http/http.js';
import Edit from './edit.vue';
import { useUserStore } from '@/stores/user.js';
import { useProjectStore } from '@/stores/project.js';

const tableData = ref([]);
let r_id = ref(0);
let userStore = useUserStore();
const dialogEditVisible = ref(false);
const filterCount = ref(0);
let projectStore = useProjectStore();

let form = reactive({
    page: 1,
    pagesize: 15,
});

let total = ref(0);

let showFilterContainer = ref(false);

function onFilterStatusChange() {
    showFilterContainer.value = !showFilterContainer.value;
};

function update_table() {
    http.get('/machines/reservations', { params: form }).then(res => {
        tableData.value = res.data.data.results;
        total.value = res.data.data.count;
    });

    filterCount.value = Object.keys(form).filter(key => !['page', 'pagesize', 'project_number'].includes(key)).reduce((count, key) => {
        if (form[key] == '' || form[key] == undefined || form[key] == null || form[key].length == 0) {
            return count;
        } else {
            return count + 1;
        }
    }, 0)
};

function handleReset() {
    form = reactive({
        page: 1,
        pagesize: 10,
        project_number: form.project_number,
    });
    update_table();
};

function onPageChange() {
    update_table();
};

function onFilter() {
    form.page = 1;
    form.pagesize = 10;
    update_table();
};

function handleCancel(row) {
    ElMessageBox.confirm(
        '确定取消吗?',
        '提示',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }
    ).then(() => {
        http.post(`/machines/reservations/${row.id}/cancel`).then(res => {
            ElMessage({
                message: '取消操作成功.',
                type: 'success',
            });
            update_table();
        }).catch(err => {
            ElMessage({
                type: 'error',
                message: err.response.data.msg
            });
        });
    }).catch(() => {
        ElMessage({
            type: 'info',
            message: '已取消取消操作'
        });
    });
};

function handleCheckin(row) {
    http.post('/machines/reservations/checkin', { id: row.id }).then(res => {
        ElMessage({
            message: '签到成功.',
            type: 'success',
        });
        update_table();
    }).catch(err => {
        ElMessageBox.alert(err.response.data.msg, '签到失败', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'error',
        })
    });
};

function onEditAffirm() {
    dialogEditVisible.value = false;
    update_table();
};

function onEditCancel() {
    dialogEditVisible.value = false;
};

function handleRefresh() {
    update_table();
};

function handleApprove(row) {
    ElMessageBox.confirm(
        '确定批准吗?',
        '提示',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }
    ).then(() => {
        http.post(`/machines/reservations/${row.id}/approve`).then(res => {
            ElMessage({
                message: '批准操作成功.',
                type: 'success',
            });
            update_table();
        }).catch(err => {
            ElMessage({
                type: 'error',
                message: err.response.data.msg
            });
        });
    }).catch(() => {
        ElMessage({
            type: 'info',
            message: '已取消批准操作'
        });
    });
};

watch(() => projectStore.project_info, () => {
    form.project_number = projectStore.project_info.projectCode;
    update_table();
});

onMounted(() => {
    form.project_number = projectStore.project_info.projectCode;
    update_table();
});

</script>


<style scoped>
.search-container {
    width: 100%;
    display: flex;
    justify-content: center;
    justify-items: center;
    height: 40px;
}

.search-input {
    width: 50%;
    max-width: 500px;
}

.search-button {
    border: 1px solid #dcdfe6;
}

.filter-container {
    width: 100%;
    display: flex;
    justify-content: flex-start;

    margin-bottom: 10px;

    .el-input {
        width: 50%;
        max-width: 300px;
        margin-right: 10px;
    }
}
</style>