<template>
    <el-divider />
    <div class="add-container">
        <el-form :model="form" label-width="auto" :rules="rules" status-icon ref="formRef">

            <el-form-item label="名称" prop="name">
                <el-input v-model="form.name"></el-input>
            </el-form-item>
 
            <!-- <el-form-item label="编号" prop="code">
                <el-input v-model="form.code" readonly></el-input>
            </el-form-item> -->

            <el-form-item label="描述">
                <el-input type="textarea" :rows="3" v-model="form.desc"></el-input>
            </el-form-item>

            <div class="submit-button-container">
                <el-button type="default" @click="onCancel">取消</el-button>
                <el-button type="primary" @click="onSubmit">提交</el-button>
            </div>

        </el-form>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import http from '@/utils/http/http.js';


const props = defineProps({
    r_id: {
        type: Number,
        required: true,
    },
});

const formRef = ref(null);

const form = ref({
    name: '',
    code: '',
    desc: '',
});

const rules = ref({
    name: [
        { required: true, message: '请输入名称', trigger: 'blur' },
    ],
    code: [
        { required: true, message: '请输入编号', trigger: 'blur' },
    ],
});

const emit = defineEmits(['confirm', 'cancel'])

const onSubmit = () => {
    formRef.value.validate(async (valid) => {
        if (valid) {
            let data = {
                ...form.value,
            };

            http.put(`/inspecs/items/${props.r_id}`, data).then(res => {
                emit('confirm');
            }).catch(err => {
                ElMessageBox.alert(err.response.data.msg, '编辑失败', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'error',
                })
            });
        };
    });
};

const onCancel = () => {
    emit('cancel');
};

onMounted(() => {
    if (props.r_id) {
        http.get(`/inspecs/items/${props.r_id}`).then(res => {
            let data = res.data.data;

            form.value.name = data.name;
            form.value.code = data.code;
            form.value.desc = data.desc;
        });
    };
});

</script>


<style lang="scss" scoped>
.add-container {
    padding: 0 20px;
}

.submit-button-container {
    display: flex;
    justify-content: flex-end;
}
</style>