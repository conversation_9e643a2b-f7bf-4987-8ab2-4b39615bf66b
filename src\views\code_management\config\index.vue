<template>
  <!-- 提示弹框 -->
  <TipDialog v-if="showTipDialog" />
  <div v-else class="page-container" v-custom-loading="loading">
    <!-- 顶部工具栏组件 -->
    <Toolbar
      :sdk-version="SdkVersion"
      :form="form"
      :space-options="spaceOptions"
      :branch-options="branchOptions"
      :loading="loading"
      :has-edit-permission="hasEditPermission"
      @update:form="(newForm) => Object.assign(form, newForm)"
      @gitlab-click="handleGitlabClick"
      @download="handleDownload"
      @save="handleSave"
      @publish="handlePublish"
      @merge-test="confirmAction"
      @confirm-action="confirmAction"
    />

    <!-- 主要内容区域 -->
    <div class="main-content" >
      <!-- 第二行：功能模块和配置详情 -->
      <div class="content-row">
        <!-- 功能模块区域 -->
        <div class="module-section">
          <div class="section-header">
            <div class="section-title">
              <el-icon class="section-icon"><Menu /></el-icon>
              <span>功能模块</span>
            </div>
          </div>
          <div class="module-content">
            <el-tree
              ref="treeRef"
              :data="treeData"
              :props="defaultProps"
              @node-click="handleNodeClick"
              node-key="label"
              highlight-current
              class="custom-tree"
              :expand-on-click-node="false"
              :default-expand-all="false"
            />
          </div>
        </div>

        <!-- 配置详情区域 -->
        <div class="config-section">
          <div class="section-header">
            <div class="section-title">
              <el-icon class="section-icon"><Tools /></el-icon>
              <span>配置详情</span>
            </div>
            <div class="section_tab">
              <el-radio-group 
                v-model="moduleTab" 
                @change="handleModuleTabClick"
                class="radio-tab-group"
              >
                <el-radio-button label="config">配置视图</el-radio-button>
                <el-radio-button label="code">代码视图</el-radio-button>
              </el-radio-group>
            </div>
           
          </div>
          
            <div v-if="moduleTab==='config'" class="config-content">

              <el-tabs 
                  v-model="activeTab" 
                  type="card" 
                  class="config-tabs"
                >
                  <el-tab-pane 
                    v-for="title in nodetitles" 
                    :key="title" 
                    :label="title"
                    :name="nodetitles.indexOf(title).toString()"
                  >

                  
                  </el-tab-pane>
                  <div class="config-form">
                    
                      <Configuration
                        v-if="showTree && activeTab === '0' "
                        :key="`Configuration-${node_level}`" 
                        :config="configData"
                        :workspacePath="workspace"
                        :branchStatus="branch_status"
                        :project_code="project_code"
                        :project_name="project_name"
                        :project_gitlab="form.gitlab"
                        :project_branch="form.project_branch"
                        :node_level="node_level"
                        :nodeName="nodeName"
                        :hasEditPermission="hasEditPermission"
                      />
                    
                      <!-- 第二个标签页内容 -->
                    
                      <Curve
                        v-if="showTree && activeTab === '1' && node_level === '3.1'"
                        :key="`curve-${project_code}-${node_level}`"
                        :config="configData"
                        :workspacePath="workspace"
                        :branchStatus="branch_status"
                        :project_code="project_code"
                        :project_name="project_name"
                        :project_gitlab="form.gitlab"
                        :project_branch="form.project_branch"
                        :node_level="node_level"
                        :nodeName="nodeName"
                        :hasEditPermission="hasEditPermission"
                      />
                    
                      <Memory
                      v-if="showTree && activeTab === '1' && node_level === '2.1'"
                        :key="`memory-${project_code}-${node_level}`"
                        :config="configData"
                        :workspacePath="workspace"
                        :branchStatus="branch_status"
                        :project_code="project_code"
                        :project_name="project_name"
                        :project_gitlab="form.gitlab"
                        :project_branch="form.project_branch"
                        :node_level="node_level"
                        :nodeName="nodeName"
                        :hasEditPermission="hasEditPermission"
                      />
                      <!-- 空状态 -->
                      <div class="empty-state" 
                        v-if="!showTree">
                        <el-empty description="请选择左侧功能模块进行配置">
                          <template #image>
                            <el-icon size="64" color="#c0c4cc"><DocumentCopy /></el-icon>
                          </template>
                        </el-empty>
                      </div>
                    <div>
                    </div>
                  </div>   
              </el-tabs>
            </div>
            <div v-if="moduleTab==='code' && node_level && nodeName" class="code_content">
              <MonacoPreview
                :key="`Monaco-${project_code}-${node_level}`"
                :workspacePath="workspace"
                :branchStatus="branch_status"
                :project_code="project_code"
                :project_name="project_name"
                :project_gitlab="form.gitlab"
                :project_branch="form.project_branch"
                :node_level="node_level"
                :nodeName="nodeName"
                :hasEditPermission="hasEditPermission"
                :read-only="false"
               >

              </MonacoPreview>
            </div>
        </div>
      </div>
    </div>
  </div>

 
</template>

<script setup>
import { ref, onMounted, watch, onActivated, onDeactivated, reactive, computed, nextTick} from 'vue';
import { useRoute } from 'vue-router';
import { ElMessageBox } from 'element-plus';
import http from '@/utils/http/http';
import messageManager from '@/utils/messageManager';
import Configuration from '@/views/code_management/config/components/Configuration.vue';
import Toolbar from '@/views/code_management/top/Toolbar.vue';
import { useProjectStore } from '@/stores/project.js';
import TipDialog from '../None.vue';
import Curve from '@/views/code_management/config/components/Curve.vue';
import Memory from '@/views/code_management/config/components/memory/Memory.vue';
import MonacoPreview from '@/views/code_management/config/edit/MonacoPreview.vue';
import {
  Folder,
  Monitor,
  Operation,
  Star,
  EditPen,
  Lightning,
  Menu,
  Tools,
  DocumentCopy
} from '@element-plus/icons-vue';



const content = `
def hello():
    print("Hello, Python!")
`;
// 定义组件名称，用于本地存储键名
const componentName = 'configInfoConfig';
const branch_create = ref(true);
const route = useRoute();
const projectStore = useProjectStore();

// 界面状态数据
const workspace = ref('');
const branch_status = ref('');
const SdkVersion = ref('');
const showTree = ref(false);
const treeData = ref([]);
const treeRef = ref(null);
const defaultProps = { children: 'children', label: 'label' };
const configData = ref({});
const node_level = ref('');
const nodeName = ref('');
const nodetitles = ref([]);
const loading = ref(false);
const showTipDialog = ref(false);
const moduleTab = ref('config')
// 默认激活第一个标签页
const activeTab = ref('0');

// 项目相关变量
let project_code = '';
let project_name = '';
let pick_tree_info = null;
let workspace_path = '';

// 表单数据 - 使用全局状态
const form = reactive({
  gitlab: '',
  project_branch: ''
});
const spaceOptions = ref([]);
const branchOptions = ref([]);

const handleModuleTabClick = async(tab) =>{
    await nextTick();
    console.log('切换标签页:', tab)
    console.log('moduleTab:', moduleTab.value)
}


// 初始化时从全局状态恢复数据
const initializeFromStore = () => {
  const codeManagement = projectStore.codeManagement;
  form.gitlab = codeManagement.gitlab || '';
  form.project_branch = codeManagement.project_branch || '';
  spaceOptions.value = codeManagement.spaceOptions || [];
  branchOptions.value = codeManagement.branchOptions || [];
  SdkVersion.value = codeManagement.sdkVersion || '';
};

const chipForm = reactive({
  workspace: 'default', // 工作空间
  branchStatus: 'main', // 分支状态
});

// 单击仓库
const handleGitlabClick = (visible) => {
  // 判断有无存在的ElMessage， 存在则关闭
  if (!project_code) {
    messageManager.warning('请选择项目！')
  }
}

// 合并条件为计算属性（自动缓存）
const shouldShowCurve = computed(() => {
  // 打印日志可观察触发频率（调试用）
  console.log('条件判断执行'); 
  // 增加稳定性检查，避免频繁切换
  if (!showTree.value || activeTab.value !== '1') {
    return false;
  }
  // 缓存节点级别判断结果
  return node_level.value === '3.1';
});

// 权限控制 - 基于branch_create状态
const hasEditPermission = computed(() => branch_create.value);

onMounted(() => {
   console.log('[Parent index.vue] onMounted', Date.now());
  try {
    // 从全局状态初始化数据
    initializeFromStore();

    // 监控项目信息变化
    const info = projectStore.project_info || {};
    project_code = info.projectCode || '';
    project_name = info.name || '';

    console.log('project_code:', project_code);
    console.log('project_name:', project_name);
    console.log('从全局状态恢复的仓库信息:', form.gitlab);
    console.log('从全局状态恢复的分支信息:', form.project_branch);

    if (project_code=="" && project_name=="") {
      console.info('项目信息为空，请先选择项目');
      showTipDialog.value = true;
    } else {
      console.info('项目信息已选择，开始初始化');
      showTipDialog.value = false;
      // 如果没有仓库信息，则获取仓库列表
      if (!form.gitlab || spaceOptions.value.length === 0) {
        get_space();
      } else {
        // 如果有仓库信息但没有分支信息，则获取分支列表
        if (!form.project_branch || branchOptions.value.length === 0) {
          get_branch();
        } else {
          // 如果都有，直接提交分支信息
          submit_branch_info();
        }
      }
    }

  } catch (error) {
    console.error('组件初始化错误:', error);
    messageManager.error('组件初始化失败，请刷新页面重试');
  }
});

// 监控项目信息变化
watch(() => projectStore.project_info, (newval, oldval) => {
  if (newval) {
    const newProjectCode = newval.projectCode || '';
    const newProjectName = newval.name || '';

    // 只有当项目代码真正发生变化时才重新获取仓库信息
    if (newProjectCode !== project_code) {
      console.log('项目切换:', project_code, '->', newProjectCode);

      project_code = newProjectCode;
      project_name = newProjectName;

      // 检查项目是否选择
      if (project_code=="" && project_name=="") {
        showTipDialog.value = true;
      } else {
        showTipDialog.value = false;
        // 清空全局状态和本地状态
        projectStore.clearCodeManagement();
        form.gitlab = '';
        form.project_branch = '';
        spaceOptions.value = [];
        branchOptions.value = [];
        get_space();
      }
    }
  }
});

// 监控仓库信息变化
watch(() => form.gitlab, ( newval, oldval) => {
  if (newval !== oldval) {
    console.log('👀 watch监听器触发 - 仓库变化:', { oldval, newval });
    // 同步更新全局状态
    projectStore.updateCodeManagement({ gitlab: newval });
    form.project_branch = '';
    get_branch();
  }
});

// 监控分支信息变化
watch(() => form.project_branch, ( newval, oldval) => {
  if (newval !== oldval) {
    console.log('👀 watch监听器触发 - 分支变化:', { oldval, newval });
    // 同步更新全局状态
    projectStore.updateCodeManagement({ project_branch: newval });
    submit_branch_info();
  }
});


watch([() => showTree.value, () => activeTab.value, () => node_level.value], (newVal, oldVal) => {
  console.log('状态变化:', { oldVal, newVal });
});


    
// 获取仓库列表
const get_space = async () => {
  try {
    loading.value = true;
    console.log('开始获取仓库信息，项目代码:', project_code);

    const response = await http.get('/code_management/space_options', {
      params: {
        project_code: project_code
      }
    });

    console.log("获取已创建的仓库信息:", response.data);

    if (response.data.status === 1) {
      spaceOptions.value = response.data.space_options || [];
      console.log('仓库列表:', spaceOptions.value);

      // 同步更新全局状态
      projectStore.setSpaceOptions(spaceOptions.value);

      // 设置默认仓库为第一个
      if (spaceOptions.value.length > 0 && !form.gitlab) {
        form.gitlab = spaceOptions.value[0];
        console.log('🔧 get_space 设置默认仓库:', form.gitlab, '- watch监听器将自动触发get_branch');
        // 移除手动调用，依赖watch监听器自动触发get_branch
      } else if (spaceOptions.value.length === 0) {
        messageManager.warning('该项目暂无可用仓库');
        showTipDialog.value = true;
      }
    } else {
      console.error('获取仓库信息失败:', response.data.message);
      messageManager.error(response.data.message || '获取仓库信息失败');
    }
  } catch (error) {
    console.error('获取仓库信息失败:', error.message);
    messageManager.error('获取仓库信息失败: ' + error.message);
  } finally {
    loading.value = false;
  }
};

// 获取分支列表
const get_branch = async () => {
  if (!project_code || !project_name) {
      console.info('项目信息为空，不请求分支数据');
      return;
    }

  if (!form.gitlab) {
    console.info('仓库地址为空，无法获取分支');
    return;
  }

  try {
    console.log('开始获取分支信息，仓库:', form.gitlab);

    const response = await http.get('/code_management/branch_options', {
      params: {
        project_code: project_code,
        project_gitlab: form.gitlab
      }
    });

    if (response.data.status === 1) {
      branchOptions.value = response.data.branch_options || [];
      console.log('分支列表:', branchOptions.value);

      // 同步更新全局状态
      projectStore.setBranchOptions(branchOptions.value);

      // 设置默认分支为第一个
      if (branchOptions.value.length > 0 && !form.project_branch) {
        form.project_branch = branchOptions.value[0];
        console.log('🔧 get_branch 设置默认分支:', form.project_branch, '- watch监听器将自动触发submit_branch_info');
        // 移除手动调用，依赖watch监听器自动触发submit_branch_info
      } else if (branchOptions.value.length === 0) {
        messageManager.warning('该仓库暂无可用分支');
        showTipDialog.value = true;
      }
    } else {
      console.error('获取分支信息失败:', response.data.message);
      messageManager.error(response.data.message || '获取分支信息失败');
    }
  } catch (error) {
    console.error('获取分支信息失败:', error.message);
    messageManager.error('获取分支信息失败: ' + error.message);
  }
};

// 提交分支信息并获取SDK版本具体信息
const submit_branch_info = async () => {
  if (!project_code || !project_name) {
    console.info('项目信息为空，不提交数据');
    return;
  }
  if (!form.gitlab || !form.project_branch) {
    console.info('仓库或分支信息不完整，无法提交');
    return;
  }

  try {
    loading.value = true;
    console.log('🔄 调用submit_branch_info - 提交分支信息:', {
      project_code,
      project_name,
      gitlab: form.gitlab,
      branch: form.project_branch
    });

    const response = await http.get('/code_management/branch_submit', {
      params: {
        project_code: project_code,
        project_name: project_name,
        project_gitlab: form.gitlab,
        project_branch: form.project_branch
      },
      timeout: 60000
    });

    console.log("分支提交响应:", response.data);

    if (response.data.config_status === 1) {
      treeData.value = response.data.pick_tree_info || [];
      workspace_path = response.data.work_space;
      workspace.value = response.data.work_space;
      branch_status.value = response.data.branch_status;
      SdkVersion.value = response.data.sdk_version;
      branch_create.value = response.data.branch_create === true || response.data.branch_create === 'true';
      console.log('SDK版本:', SdkVersion.value);
      console.log('树形数据:', JSON.stringify(treeData.value, null, 2));
      console.log('工作空间:', workspace.value);
      console.log('分支状态:', branch_status.value);

      // 同步更新全局状态
      projectStore.setSdkVersion(SdkVersion.value);

      // 可以在这里添加其他成功后的操作
      messageManager.success('项目配置加载成功');
    } else {
      const errorMsg = response.data.message || '分支提交失败';
      console.error('分支提交失败:', errorMsg);
      messageManager.error(errorMsg);
    }
  } catch (error) {
    console.error('分支提交失败:', error.message);
    messageManager.error('分支提交失败: ' + error.message);
  } finally {
    loading.value = false;
  }
};



// 左侧目录树点击事件
const handleNodeClick = (node, nodeData, nodeComponent) => {
  const isLeaf = !node.children || node.children.length === 0;
  console.log('节点点击:', node.label, 'isLeaf:', isLeaf);

  // 如果是父节点（有子节点），处理展开/折叠逻辑
  if (!isLeaf) {
    console.log('点击父节点，处理展开/折叠状态');
    if (treeRef.value) {
      const nodeKey = node.label; // 使用 label 作为 node-key
      const currentNode = treeRef.value.getNode(nodeKey);
      
      // 判断是否为一级节点（父节点为根节点）
      const isFirstLevel = currentNode.parent && currentNode.parent.level === 0;

      if (currentNode) {
        // 如果是一级节点，先关闭其他所有一级节点
        if (isFirstLevel) {
          // 获取所有一级节点
          const firstLevelNodes = treeRef.value.store.root.childNodes || [];
          
          // 关闭所有其他一级节点
          firstLevelNodes.forEach(node => {
            if (node.label !== nodeKey && node.expanded) {
              node.collapse();
              console.log('关闭其他一级节点:', node.label);
            }
          });
        }

        // 切换当前节点的展开/折叠状态
        if (currentNode.expanded) {
          currentNode.collapse();
          console.log('折叠节点:', nodeKey);
        } else {
          currentNode.expand();
          console.log('展开节点:', nodeKey);
        }
      }
    }
    return;
  }

  // 只有点击叶子节点时才加载配置
  console.log('点击叶子节点:', node, "加载配置");
  showTree.value = false;
  const parentName = findTopLevelParentLabel(treeData.value, node.label);
  // 只有当视图是配置视图时才请求
  console.log('当前的视图 moduleTab:', moduleTab.value)

    http.post('/code_management/configuration_info', {
    params: { 
      node_level:node.level,
      nodeName: node.label, 
      parentName, 
      workspace_path: workspace.value, 
      branch_status: branch_status.value,
      project_code: project_code,
      project_name: project_name,
      project_gitlab: form.gitlab,
      project_branch: form.project_branch
    }
  }).then(response => {
    if (response.data.config_status === 1) {
      console.log('配置详情获取成功:', response.data.config_info);
      configData.value = response.data.config_info;
      node_level.value=node.level; 
      nodeName.value=node.label;
      nodetitles.value=node.titles;
      console.log('nodetitles:', nodetitles);
      activeTab.value = '0';
      showTree.value = true;
    } else {
      messageManager.error('配置详情获取失败');
    }
  }).catch(() => {
    messageManager.error('无法获取配置详情');
  });
  
  
  
};

// 提取节点顶层父标签
const findTopLevelParentLabel = (tree, targetLabel, path = []) => {
  if (!tree || !Array.isArray(tree)) {
    console.info('树数据无效:', tree);
    return null;
  }

  for (const node of tree) {
    if (!node || !node.label) {
      console.info('节点数据无效:', node);
      continue;
    }

    const currentPath = [...path, node.label];
    if (node.label === targetLabel) return currentPath[0];
    if (node.children) {
      const result = findTopLevelParentLabel(node.children, targetLabel, currentPath);
      if (result) return result;
    }
  }
  return null;
};

// download 操作
const handleDownload = () => {
  console.log('开始下载配置');
  messageManager.success('配置下载成功');
};

// commit 操作 - 接收工具栏传递的 commit 信息
const handleSave = async (commitMessage) => {
  try {
    loading.value = true;
    console.log('开始保存配置，Commit信息:', commitMessage);

    const response = await http.post('/code_management/config_commit', {
      params: {
        commit_message: commitMessage,
        workspace_path: workspace.value,
        branch_status: branch_status.value
      }
    }, {
      timeout: 60000
    });

    loading.value = false;
    console.log('保存配置响应:', response.data);

    if (response.data.commit_status === 1) {
      messageManager.success('配置已保存:commit成功');
    } else {
      messageManager.error('配置保存失败:commit失败');
    }
  } catch (error) {
    loading.value = false;
    console.error('保存配置失败:', error);
    messageManager.error('保存失败: ' + (error.message || '网络错误'));
  }
};

// push 操作 - 接收工具栏传递的发布确认
const handlePublish = async () => {
  try {
    loading.value = true;
    console.log('开始发布配置');

    const response = await http.post('/code_management/config_push', {
      params: {
        workspace_path: workspace.value,
        branch_status: branch_status.value
      }
    }, {
      timeout: 60000
    });

    loading.value = false;
    console.log('发布配置响应:', response.data);

    if (response.data.push_status === 1) {
      messageManager.success('配置已发布');
    } else {
      messageManager.error('配置发布失败');
    }
  } catch (error) {
    loading.value = false;
    console.error('发布配置失败:', error);
    messageManager.error('发布失败: ' + (error.message || '网络错误'));
  }
};

// merge 确认操作 - 接收工具栏传递的 merge 分支信息
const confirmAction = async (mergeBranch) => {
  try {
    loading.value = true;
    console.log('开始合并分支到:', mergeBranch);

    const response = await http.post('/code_management/merge_project', {
      params: {
        workspace_path: workspace.value,
        merge_branch: mergeBranch
      }
    }, {
      timeout: 60000
    });

    loading.value = false;
    console.log('合并分支响应:', response.data);
    messageManager.success('已发送merge信息,请等待管理员审批');
  } catch (error) {
    loading.value = false;
    console.error('合并分支失败:', error);
    messageManager.error('merge失败: ' + (error.message || '网络错误'));
  }
};

// 测试merge功能（由工具栏组件处理弹窗）
const mergetest = () => {
  // 弹窗逻辑现在在Toolbar组件中处理
  console.log('准备合并操作');
};




</script>

<style scoped>


/* 树形菜单样式优化 */
:deep(.el-tree) {
  --level-indent: 20px;
  --level-color-1: #333;
  --level-color-2: #666;
  --level-color-3: #999;
  background-color: transparent;
  font-size: 14px;
}

:deep(.el-tree-node__content) {
  height: auto;
  transition: all 0.3s;
  border-radius: 4px;
  margin-bottom: 15px;
  padding: 15px 12px;
  background-color: #cccccc11;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
}

:deep(.el-tree-node__content:hover) {
  background-color: #f0f0f0;
  color: #000;
}

:deep(.el-tree .el-tree-node.is-current > .el-tree-node__content) {
  background-color: #409eff !important; 
}

:deep(.el-tree .el-tree-node.is-current > .el-tree-node__content .el-tree-node__label) {
  color: #fff !important; 
}

:deep(.el-tree-node__label) {
  color: #777;
}

:deep(.el-tree-node__expand-icon) {
  color: #ccc;
  font-size: 14px;
  margin-right: 8px;
}

:deep(.el-tree-node__expand-icon.expanded) {
  color: #666;
  font-size: 14px;
  margin-right: 8px;
  transform: rotate(90deg);
}


/* 主要内容区域 */
.main-content {
  width:100%;
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 0;
  gap: 20;
  min-height: 0;
}

/* 第二行：功能模块和配置详情 */
.content-row {
  flex: 1;
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 20px;
  margin: 20px 0px;
  min-height: calc(100vh - 120px); /* 确保有足够的最小高度 */
  width: 100%;
}

/* 功能模块区域 */
.module-section {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  min-height: fit-content;
  max-width: 300px;
}

/* 配置详情区域 */
.config-section {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  min-height: fit-content;
  width: 100%;
  border: 3px solid #f0f0f0;
  box-sizing: border-box;
}

/* 区域头部样式 */
.section-header {
  padding: 20px 0px 20px 24px;
  border-bottom: 1px solid #e4e7ed;
  background: #fff;
  display:flex;
  justify-content: space-between; 
  align-items: center; /* 可选，让内容垂直居中 */
}

/* 功能模块头部 */
.module-section .section-header {
  border-radius: 12px;
}

/* 配置详情头部 */
.config-section .section-header {
  border-radius: 12px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #666;
  margin: 0;
}

.section-icon {
  font-size: 18px;
  color: #409eff;
}

/* 侧边栏头部 */
.sidebar-header {
  border-radius: 8px;
  padding: 50px;
  border-bottom: 1px solid #e4e7ed;
  /* background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); */
}

.module-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: white;
  margin: 0;
}

.module-icon {
  font-size: 18px;
}

/* 模块内容区域 */
.module-content {
  padding: 10px 0px;
}



/* 为单选按钮组添加样式，使其更接近标签页效果 */
.radio-tab-group {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fff;
  margin-right: 30px;
}

/* 移除默认的聚焦样式（可选） */
:deep(.el-radio-button__inner) {
  border-radius: 0;
  border-color: transparent;
}

/* 选中状态样式 */
:deep(.el-radio-button.is-active .el-radio-button__inner) {
  background-color: #409eff;
  color: #fff;
  border-color: #409eff;
}

/* 悬停效果 */
:deep(.el-radio-button:not(.is-active):hover .el-radio-button__inner) {
  color: #409eff;
  background-color: #f5f7fa;
}




.code_content{
  padding: 20px;
}

/* 空状态 */
.empty-state {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.version-info {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #999;
  font-weight: 500;
}

/* 页面容器 */
.page-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  overflow-y: auto;
  width:100%;
  /* margin: 20px; */
}

/* 配置详情标签页样式优化 */
:deep(.config-tabs) {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 0px 0px;
  --el-tabs-header-height: 50px;
}


.config-tabs :deep(.el-tabs__header) {
  /* border-bottom: 1px solid #f0f0f0; */
  margin-bottom: 0px;
  padding-bottom: 4px;
}

.config-tabs :deep(.el-tabs__item) {
  padding: 20px 20px;
  height: var(--el-tabs-header-height);
  line-height: var(--el-tabs-header-height);
  font-size: 14px;
  color: #666;
  transition: all 0.2s;
}

.config-tabs :deep(.el-tabs__item.is-active) {
  color: #409eff;
  font-weight: 500;
  border-bottom: 2px solid #409eff;
}

.config-tabs :deep(.el-tabs__content) {
  flex: 1;
  overflow: auto;
  padding: 8px 0;
}

.config-tabs :deep(.el-tabs__item.is-active) {
  background-color: #ffffff; /* 选中标签背景色 */
  color: #1890ff; /* 选中标签文字色（Element默认主题色） */
  border-bottom-color: #1890ff; /* 选中标签底部边框色 */
}

.config-tabs :deep(.el-tabs__item:hover:not(.is-active)) {
  color: #1890ff; /* hover时文字颜色 */
  background-color: #e6f7ff; /* hover时背景色 */
}


/* 配置表单区域优化 */
.config-form {
  flex: 1;
  overflow-y: auto;
  padding: 10px 10px;
  background-color: #fff;
  /* border-radius: 6px; */
  /* margin: 0 0px; */
  /* box-shadow:  0 2px 12px 0 rgba(181, 47, 47, 0.1); */
}







</style>