<template>
  <div class="code-editor-container">
    <div v-if="loading" v-custom-loading="loading" class="loading"></div>
    <div v-if="error" class="error">{{ error }}</div>
    
    <div v-else class="file-list">
      <div 
        v-for="(file, index) in fileList" 
        :key="file.containerId"  
        class="file-item"
      >
        <div 
          class="file-header" 
          @click="handleExpandClick(file, index)"
        >
          <div class="file-info">
            <ArrowRight 
              class="toggle-icon" 
              :class="{ 'expanded': file.expanded }" 
              size="16" 
            />
            <span class="file-name">{{ file.file_name }}</span>
            <!-- 路径文本外层新增容器，辅助小屏换行控制 -->
            <div class="full-path-wrapper">
              <span class="full-path">{{ getFullPath(file) }}</span>
            </div>
          </div>
          <span class="status">{{ file.isProcessing ? '处理中' : (file.expanded ? '折叠' : '展开') }}</span>
        </div>
        
        <div 
          :id="file.containerId"
          :ref="(el) => setContainerRef(index, el)"
          class="editor-container"
          :class="{ 'visible': file.expanded }"
        ></div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 脚本部分完全不变，无需修改
import { ref, onMounted, onBeforeUnmount, nextTick } from 'vue'
import { ArrowRight } from '@element-plus/icons-vue'
import * as monaco from 'monaco-editor/esm/vs/editor/editor.api.js'
import editorWorker from 'monaco-editor/esm/vs/editor/editor.worker?worker'
import jsonWorker from 'monaco-editor/esm/vs/language/json/json.worker?worker'
import tsWorker from 'monaco-editor/esm/vs/language/typescript/ts.worker?worker'
import cssWorker from 'monaco-editor/esm/vs/language/css/css.worker?worker'
import htmlWorker from 'monaco-editor/esm/vs/language/html/html.worker?worker'
import http from '@/utils/http/http'
import 'monaco-editor/min/vs/editor/editor.main.css'
import 'monaco-editor/esm/vs/basic-languages/python/python.contribution.js';
import 'monaco-editor/esm/vs/basic-languages/cpp/cpp.contribution.js';
import 'monaco-editor/esm/vs/basic-languages/markdown/markdown.contribution.js'
import 'monaco-editor/esm/vs/basic-languages/xml/xml.contribution.js';




const LOG_PREFIX = '[CodeEditor]'
if (!self.MonacoEnvironment) {
  self.MonacoEnvironment = {
    getWorker(_, label) {
      if (label === 'json') return new jsonWorker()
      if (label === 'css' || label === 'scss' || label === 'less') return new cssWorker()
      if (label === 'html' || label === 'handlebars' || label === 'razor') return new htmlWorker()
      if (label === 'typescript' || label === 'javascript') return new tsWorker()
      return new editorWorker()
    }
  }
}
const props = defineProps({
  workspacePath: { type: String, required: true },
  node_level: { type: String, required: true },
  nodeName: { type: String, required: true }
})
const fileList = ref([])
const editorContainers = ref([])
const loading = ref(true)
const error = ref('')
const maxExpandedFiles = 6
function generateUniqueId(index, fileName) {
  const safeFileName = fileName.replace(/[^a-zA-Z0-9_\-.]/g, '_')
  return `editor-${index}-${safeFileName}`
}
function getLanguageFromFileName(fileName) {
  if (!fileName) return 'text'
  const ext = fileName.split('.').pop().toLowerCase()
  const langMap = {
    js: 'javascript', ts: 'typescript', json: 'json', html: 'html',
    css: 'css', py: 'python', java: 'java', c: 'c', cpp: 'cpp',
    h: 'c', md: 'markdown',xml: 'xml'
  }
  return langMap[ext] || 'text'
}
function getFullPath(file) {
  const endsWithAny = (str, chars) => {
    if (!str) return false
    const lastChar = str.slice(-1)
    return chars.includes(lastChar)
  }
  const workspace = endsWithAny(props.workspacePath, ['/', '\\']) 
    ? props.workspacePath 
    : `${props.workspacePath}/`
  const filePath = file.file_path 
    ? (endsWithAny(file.file_path, ['/', '\\']) ? file.file_path : `${file.file_path}/`) 
    : ''
  return `${filePath}${file.file_name}`
}
async function initEditor(file, index) {
  const containerRef = editorContainers.value[index]
  if (!file || !containerRef) {
    console.error(`${LOG_PREFIX} 初始化失败:`, { file: file?.file_name, index, containerExists: !!containerRef })
    return false
  }
  if (file.editorInstance) {
    console.log(`${LOG_PREFIX} ${file.file_name} 已存在编辑器实例`)
    return true
  }
  try {
    containerRef.style.height = '600px'
    containerRef.style.width = '100%'
    containerRef.innerHTML = `<div style="padding:10px;"></div>`
    const content = file.content || '文件内容为空'
    console.log(`${LOG_PREFIX} 初始化 ${file.file_name} (索引${index}): 内容长度=${content.length}`)
    const model = monaco.editor.createModel(content, getLanguageFromFileName(file.file_name))
    const editorInstance = monaco.editor.create(containerRef, {
      model: model,
      theme: 'vs',
      readOnly: true,
      automaticLayout: true,
      fontSize: 14,
      wordWrap: 'on',
      lineNumbers: 'on'
    })
    file.editorInstance = editorInstance
    file.model = model
    file.containerRef = containerRef
    console.log(`${LOG_PREFIX} ${file.file_name} 初始化成功`, { 索引: index, 编辑器ID: editorInstance.getId(), 容器ID: containerRef.id })
    return true
  } catch (err) {
    console.error(`${LOG_PREFIX} ${file.file_name} 初始化失败:`, err)
    containerRef.innerHTML = `<div style="padding:10px;color:red;">加载失败</div>`
    return false
  }
}
function disposeEditor(file, index) {
  if (!file) return
  try {
    console.log(`${LOG_PREFIX} 销毁 ${file.file_name} (索引${index})`)
    if (file.model) { file.model.dispose(); file.model = null }
    if (file.editorInstance) { file.editorInstance.dispose(); file.editorInstance = null }
    const containerRef = editorContainers.value[index]
    if (containerRef) { containerRef.innerHTML = ''; containerRef.style.height = '0' }
  } catch (err) {
    console.error(`${LOG_PREFIX} 销毁失败:`, err)
  } finally {
    file.expanded = false; file.isProcessing = false
  }
}
async function handleExpandClick(file, index) {
  if (file.isProcessing) { console.log(`${LOG_PREFIX} ${file.file_name} 正在处理中，跳过点击`); return }
  file.isProcessing = true
  try {
    if (file.expanded) { disposeEditor(file, index) }
    else {
      const expandedCount = fileList.value.filter(f => f.expanded).length
      if (expandedCount >= maxExpandedFiles) { alert(`最多只能同时展开${maxExpandedFiles}个文件`); return }
      file.expanded = true; await nextTick(); await initEditor(file, index)
    }
  } catch (err) {
    console.error(`${LOG_PREFIX} 处理失败:`, err); file.expanded = false
  } finally {
    file.isProcessing = false
  }
}
function setContainerRef(index, el) {
  if (el) { editorContainers.value[index] = el; console.log(`${LOG_PREFIX} 容器引用设置: 索引=${index}, ID=${el.id}`) }
}

onMounted(async () => {
  loading.value = true
  try {
    const response = await http.post('/code_management/file_content', {
      params: { node_level: props.node_level, nodeName: props.nodeName, workspace_path: props.workspacePath }
    })
    console.log(`${LOG_PREFIX} 文件列表加载成功:`, response.data)
    if (!response.data || !Array.isArray(response.data.files)) throw new Error('无效的文件数据')
    fileList.value = response.data.files.map((file, index) => {
      console.log(`${LOG_PREFIX} 处理文件: 原始名="${file.file_name}", 索引=${index}`)
      return { ...file, expanded: false, isProcessing: false, editorInstance: null, model: null, containerId: generateUniqueId(index, file.file_name), index }
    })
    
    // 新增：默认展开第一个文件
    if (fileList.value.length > 0) {
      await nextTick() // 等待DOM更新
      const firstFile = fileList.value[0]
      await handleExpandClick(firstFile, 0)
    }
  } catch (err) {
    error.value = `加载失败: ${err.message}`
  } finally {
    loading.value = false
  }
})


onBeforeUnmount(() => {
  fileList.value.forEach((file, index) => disposeEditor(file, index)); editorContainers.value = []
})
</script>

<style scoped>
.code-editor-container {
  padding: 0px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  box-sizing: border-box; /* 关键：确保padding不撑大容器 */
  width: 100%; /* 强制容器占满父元素宽度，不溢出 */
}
.loading {
  padding: 16px;
  color: #666;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}
.error {
  color: #dc3545;
  padding: 16px;
  background-color: #f8d7da;
  border-radius: 6px;
  margin-bottom: 16px;
  border: 1px solid #f5c6cb;
}
.file-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%; /* 确保文件列表不超出容器 */
}
.file-item {
  border: 1px solid #e9ecef;
  border-radius: 6px;
  transition: all 0.2s ease;
  overflow: hidden;
  width: 100%; /* 单个文件项占满列表宽度 */
}
.file-item:hover {
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  border-color: #dee2e6;
}
.file-header {
  padding: 12px 0px 12px 16px;
  background: #f8f9fa;
  display: flex;
  justify-content: space-between;
  cursor: pointer;
  align-items: flex-start; /* 改为顶部对齐，避免换行后元素错位 */
  transition: background-color 0.2s ease;
  white-space: normal; /* 取消强制不换行，允许header内元素换行 */
  width: 100%;
  box-sizing: border-box;
}
.file-header:hover {
  background-color: #f1f3f5;
}
.file-info {
  display: flex;
  align-items: flex-start; /* 图标和文本顶部对齐，适配换行 */
  gap: 10px;
  overflow: hidden;
  flex: 1;
  min-width: 0;
  flex-wrap: wrap; /* 允许file-info内元素换行（关键） */
}
.toggle-icon {
  color: #6c757d;
  transition: transform 0.25s ease;
  flex-shrink: 0;
  width: 16px;
  height: 16px;
  margin-top: 2px; /* 微调图标位置，与文本对齐 */
}
.toggle-icon.expanded {
  transform: rotate(90deg);
  color: #409eff;
}
.file-name {
  font-weight: 500;
  color: #212529;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 400px;
  flex-shrink: 1;
  line-height: 1.4;
  padding: 2px 0;
}
/* 新增路径外层容器：控制换行范围 */
.full-path-wrapper {
  flex: 1;
  min-width: 120px; /* 小屏时最小宽度，避免被压缩到消失 */
  max-width: 100%; /* 最大宽度不超过父容器 */
  padding: 2px 0;
}
.full-path {
  font-size: 12px;
  color: #6c757d;
  line-height: 1.5;
  /* 核心：强制长文本换行，兼容中英文路径 */
  white-space: normal; /* 取消nowrap，允许换行 */
  word-break: break-all; /* 英文/数字强制断行 */
  word-wrap: break-word; /* 中文自动换行 */
}
.status {
  font-size: 12px;
  padding: 4px 12px;
  border-radius: 16px;
  background-color: #e9ecef;
  color: #495057;
  transition: all 0.2s ease;
  margin-left: 16px;
  flex-shrink: 0;
  line-height: 1.4;
  /* 小屏时状态标签可换行到下一行 */
  white-space: normal;
  text-align: center;
  min-width: 60px;
}
.file-header:hover .status {
  background-color: #dee2e6;
  color: #212529;
}
.status:contains('处理中') {
  background-color: #fff3cd;
  color: #856404;
  border-color: #ffeeba;
}
.editor-container {
  height: 0;
  overflow: hidden;
  transition: height 0.3s ease;
  border-top: 1px solid #e9ecef;
  width: 100%; /* 编辑器容器占满宽度 */
  box-sizing: border-box;
}
.editor-container.visible {
  height: 400px;
  background-color: #fff;
}
:deep(.monaco-editor) {
  border-radius: 0 0 4px 4px;
  width: 100% !important; /* 强制编辑器占满容器宽度 */
}

/* 小屏设备适配（≤768px，手机等） */
@media (max-width: 768px) {
  .code-editor-container {
    padding: 8px;
    max-height: 100vh; /* 强制容器只占一屏高度 */
    overflow: hidden; /* 避免整体溢出屏幕 */
  }
  .file-header {
    padding: 8px 0px 8px 10px;
    gap: 8px; /* 缩小元素间距，节省空间 */
  }
  .file-name {
    max-width: 180px; /* 小屏缩小文件名最大宽度，优先显示核心名称 */
    flex-basis: 120px; /* 确保文件名至少占120px，不被压缩过短 */
  }
  .full-path-wrapper {
    min-width: 100px; /* 路径最小宽度，保证可读性 */
    margin-top: 4px; /* 换行后与文件名保持间距 */
  }
  .full-path {
    font-size: 11px; /* 小屏缩小路径字体 */
  }
  .status {
    padding: 3px 8px;
    font-size: 11px;
    margin-left: 0;
    margin-top: 4px; /* 状态标签换行后与上方元素间距 */
    align-self: flex-start; /* 状态标签靠顶部对齐 */
  }
  /* 小屏编辑器高度：占满屏幕剩余空间，不超出一屏 */
  .editor-container.visible {
    height: calc(100vh - 140px); /* 140px = 头部+间距，根据实际调整 */
    max-height: 100vh;
    overflow: auto; /* 编辑器内容超出时可滚动，不撑大容器 */
  }
  /* 强制Monaco编辑器在小屏自适应宽度，避免横向溢出 */
  :deep(.monaco-editor .monaco-scrollable-element) {
    width: 100% !important;
  }}
  </style>