<template>
    <div class="add-container">
        <el-form :model="form" label-width="auto" :rules="rules" status-icon ref="formRef">

            <el-form-item label="所属项目">
                <el-input readonly
                    :value="`${currentDbc.project_name}(${currentDbc.project_number})`"></el-input>
            </el-form-item>

            <el-form-item label="文件名称">
                <el-input v-model="currentDbc.file_name" readonly></el-input>
            </el-form-item>

              <el-form-item label="描述">
                <el-input type="textarea" :rows="3" v-model="form.description" placeholder="请输入描述"></el-input>
            </el-form-item>

            <div class="submit-button-container">
                <el-button type="default" @click="onCancel">取消</el-button>
                <el-button type="primary" @click="onConfirm">提交</el-button>
            </div>

        </el-form>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import http from '@/utils/http/http.js';

const props = defineProps({
    r_id: {
        type: Number,
        default: 0
    }
});

const formRef = ref(null);
const currentDbc = ref({});

const form = ref({
    description: '',
});

const rules = ref({
});

const emit = defineEmits(['confirm', 'cancel'])

const onConfirm = () => {
    formRef.value.validate(async (valid) => {
        if (valid) {
            let data = {
                ...form.value,
            };

            http.put(`/can_dbc/${props.r_id}`, data).then(res => {
                emit('confirm');
            }).catch(err => {
                ElMessageBox.alert(err.response.data.msg, '提交失败', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'error',
                })
            });
        };
    });
};

const onCancel = () => {
    emit('cancel');
};

onMounted(() => {

    if (props.r_id > 0) {
        http.get(`/can_dbc/${props.r_id}`).then(res => {
            let data = res.data.data;

            form.value.description = data.description;

            currentDbc.value = data;
        });
    }
});

</script>


<style lang="scss" scoped>
.add-container {
    padding: 0 20px;
}

.submit-button-container {
    display: flex;
    justify-content: flex-end;
}
</style>