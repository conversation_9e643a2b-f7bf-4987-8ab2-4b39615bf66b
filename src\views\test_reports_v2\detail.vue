<template>

    <div>
        <div style="display: flex; align-items: center;">
            <h2 style="color:#666"> {{ testPlan.name }} </h2>
            <el-button @click="onBack" style="margin-left: auto;">返回</el-button>
        </div>

        <div class="tool-container">
            <el-button text bg icon="Download" @click="handleDownload">下载报告</el-button>
            <el-button text bg icon="UploadFilled" @click="createApproval">软件发布</el-button>
        </div>

    </div>

    <el-tabs v-model="tabActiveName">

        <el-tab-pane label="总结" name="总结">
            <el-collapse v-model="activeNames">

                <el-collapse-item class="custom-collapse-header" title="测试总结" name="1">
                    <div>
                        <el-form style="padding: 20px" label-width="auto">
                            <el-form-item label="所属项目：">
                                <span>{{ testPlan.project_name }}({{ testPlan.project_number }})</span>
                            </el-form-item>
                            <el-form-item label="测试版本：">
                                <el-tag>{{ testPlan.m_version }}</el-tag>
                            </el-form-item>

                            <el-form-item label="次级版本：">
                                <el-tag v-for="sub_version in testPlan?.product_version || []">{{ sub_version.name }}</el-tag>
                            </el-form-item>

                            <template v-for="pv in (testPlan?.product_version || [])">
                                <el-form-item v-if="pv.type_name" :label="pv.type_name + '：'">
                                    <el-tag>{{ pv.name }}</el-tag>
                                </el-form-item>
                            </template>

                            <!-- <el-form-item label="测试结论：">
                                <el-tag type="success" v-if="testPlan.stats?.result">通过</el-tag>
                                <el-tag type="danger" v-else>不通过</el-tag>
                            </el-form-item> -->
                            <el-form-item label="责任人：">
                                <span>{{ testPlan.pic_name }}</span>
                            </el-form-item>
                            <el-form-item label="开始日期：">
                                <span>{{ testPlan.p_start_time }}</span>
                            </el-form-item>
                            <el-form-item label="结束日期：">
                                <span> {{ testPlan.p_end_time }} </span>
                            </el-form-item>
                        </el-form>
                    </div>
                </el-collapse-item>

                <el-collapse-item class="custom-collapse-header" title="测试用例汇总分析" name="2">

                    <el-table :data="testPlan.stats?.m_stats || []" stripe style="width: 100%" class="table-container"
                        height="300">

                        <el-table-column label="功能模块" width="200" align="center">
                            <template #default="{ row }">
                                <span>{{ moduleMap[row.module] || row.module }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="total" label="测试用例总数" width="200" align="center"></el-table-column>
                        <el-table-column prop="non_exec" label="未执行用例数" width="200" align="center"></el-table-column>
                        <el-table-column prop="pass" label="通过" width="100" align="center"></el-table-column>
                        <el-table-column prop="ng" label="不通过" width="100" align="center"></el-table-column>
                        <el-table-column prop="pass_rate" label="通过率" width="100" align="center">
                            <template #default="{ row }">
                                <span>{{ (row.pass_rate * 100).toFixed(2) }}%</span>
                            </template>
                        </el-table-column>
                        <!-- <el-table-column prop="name" label="问题等级 -致命(A)" width="200" align="center"></el-table-column>
        <el-table-column prop="project_name" label="问题等级 - 严重(B)" width="200" align="center"></el-table-column>
        <el-table-column prop="software_version" label="问题等级 - 一般(C)" width="200" align="center"></el-table-column>
        <el-table-column prop="name" label="问题等级 - 建议(D)" width="200" align="center"></el-table-column> -->
                        <el-table-column label="测试结果" width="200" align="center">
                            <template #default="{ row }">
                                <el-tag v-if="row.result" type="success">通过</el-tag>
                                <el-tag v-else type="danger">不通过</el-tag>
                            </template>
                        </el-table-column>

                    </el-table>

                </el-collapse-item>

                <el-collapse-item class="custom-collapse-header" title="故障统计" name="3">

                    <el-table :data="testPlan?.process_monitor_exp || []" stripe style="width: 100%" class="table-container"
                        height="400">

                        <el-table-column prop="code" label="编码" min-width="200" align="center"></el-table-column>
                        <el-table-column prop="name" label="名称" min-width="200" align="center"></el-table-column>
                        <el-table-column prop="count" label="次数" min-width="200" align="center"></el-table-column>

                    </el-table>

                </el-collapse-item>

            </el-collapse>
        </el-tab-pane>

        <el-tab-pane label="测试用例执行情况" name="测试用例执行情况">
            <TestCases :testCases="testPlan.test_cases || []" />
        </el-tab-pane>
    </el-tabs>



</template>

<script setup>
import { ref, onMounted, createApp } from 'vue';
import http from '@/utils/http/http.js';
import { useRouter, useRoute } from 'vue-router';
import TestCases from './test_cases.vue';

const tabActiveName = ref('总结');
const activeNames = ref(['1', '2', '3']);
const router = useRouter();
const route = useRoute();
let moduleMap = ref({});
const testPlan = ref({});

function handleDownload() {
    window.open(import.meta.env.VITE_BASE_URL + `/v2/test_reports/${route.params.id}/download`);
};

function createApproval() {
    router.push({ path: `/test_reports_v2/approval_create` });
}

function onBack() {
    router.push({ path: '/test_reports_v2' });
}

onMounted(() => {
    http.get(`/v2/test_reports/${route.params.id}`).then(res => {
        let data = res.data.data;
        testPlan.value = data;    
    });

    http.get('/functions').then(res => {
        res.data.data.results.forEach(item => {
            moduleMap.value[item.number] = item.name;
            if (item.children) {
                item.children.forEach(item2 => {
                    moduleMap.value[item.number + '-' + item2.number] = item2.name;
                    if (item2.children) {
                        item2.children.forEach(item3 => {
                            moduleMap.value[item.number + '-' + item2.number + '-' + item3.number] = item3.name;
                        });
                    }
                });
            }
        });
    });
});



</script>

<style lang="scss" scoped>
.tool-container {
    display: flex;
    justify-content: flex-start;
    margin-bottom: 10px;
}

:deep(.el-form-item) {
    margin: 0;
}

:deep(.el-collapse-item__arrow) {
    order: -1;
    margin: 0 8px 0 0;
}

:deep(.el-collapse-item__header) {
    font-size: 16px;
}

:deep(.custom-collapse-header .el-collapse-item__header) {
    font-weight: bold;
    color: "#666"
}
</style>