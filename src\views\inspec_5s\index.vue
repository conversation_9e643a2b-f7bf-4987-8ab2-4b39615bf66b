<template>
  <div class="test-activity-tabs">
    <el-tabs v-model="activeTabName" class="custom-tabs" @tab-click="handleTabClick">
      <el-tab-pane label="值班表" name="persons"></el-tab-pane>
      <el-tab-pane label="值班记录" name="records"></el-tab-pane>
      <el-tab-pane label="值班检查项" name="items"></el-tab-pane>
    </el-tabs>
    <div v-if="activeTabName==='persons'">
        <persons></persons>
    </div>
    <div v-if="activeTabName==='records'">   
      <records></records>
  </div>
  <div v-if="activeTabName==='items'">
    <items></items>   
  </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import persons from '@/views/inspec-persons/index.vue'
import records from '@/views/inspec-records/index.vue'
import items  from '@/views/inspec-items/index.vue'
import { useRouter } from 'vue-router';

const router = useRouter();



const activeTabName = ref('persons');

const handleTabClick = (tab) => {
  console.log('点击的标签：', tab.props.name);
  // 可在此处添加切换标签时的逻辑，如请求对应数据等
};
</script>

<style scoped>
 
.test-activity-tabs {
  width: 100%;
  margin: 0px 20px;
}

.custom-tabs {
    margin-bottom: 25px;
}

.el-tabs__nav {
  border-bottom: none;
}

.el-tabs__item {
  padding: 0 16px;
  font-size: 14px;
  color: #606266;
  margin-right: 24px;
}

.el-tabs__item.is-active {
  color: #409eff;
  border-bottom: 2px solid #409eff;
}
</style>