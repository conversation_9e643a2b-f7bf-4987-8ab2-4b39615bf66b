<template>
    <el-form>
        <el-form-item label="DBC文件ID：">
            <span>{{ params.dbc_file }}</span>
        </el-form-item>

        <el-form-item label="发送报文：">
            <el-tag v-for="(msg, index) in params.send_msg || []" :key="index" style="margin-right: 5px;">{{ showMessage(msg) }}</el-tag>
        </el-form-item>

        <el-form-item label="接收报文：">
            <el-tag v-for="(msg, index) in params.recv_msg || []" :key="index" style="margin-right: 5px;">{{ showMessage(msg) }}</el-tag>
        </el-form-item>
    </el-form>
</template>

<script setup>

import { computed } from 'vue';

const props = defineProps(
    {
        params: {
            type: Object,
            required: true,
        },
    }
);

const params = computed(() => props.params);


function showMessage(v) {
    if (!v || v.frame_id === undefined) return '请选择消息';
    const hexId = '0x' + Number(v.frame_id).toString(16).toUpperCase().padStart(3, '0');
    const bytes = normalizeToBytes(v.msg);
    const payload = formatHex(bytes, { groupSize: 4 });
    return `${hexId} | [${bytes.length}] ${payload}`;
}

function normalizeToBytes(msg) {
    let m = msg;
    if (m && typeof m === 'object' && !Array.isArray(m) && !(m instanceof Uint8Array)) {
        if ('data' in m) m = m.data;
    }
    if (Array.isArray(m)) {
        return m.map(n => (Number(n) & 0xFF) >>> 0);
    }
    if (m instanceof Uint8Array) {
        return Array.from(m);
    }
    if (typeof m === 'string') {
        // 去掉0x/0X、空格、非十六进制字符
        let s = m.replace(/^0x/i, '').replace(/\s+/g, '').replace(/[^0-9a-fA-F]/g, '');
        if (s.length % 2 === 1) s = '0' + s;
        const out = [];
        for (let i = 0; i < s.length; i += 2) {
            out.push(parseInt(s.slice(i, i + 2), 16) & 0xFF);
        }
        return out;
    }
    return [];
}

function formatHex(bytes, { groupSize = 4 } = {}) {
    if (!bytes?.length) return '';
    const hex = bytes.map(b => (b & 0xFF).toString(16).toUpperCase().padStart(2, '0'));
    const parts = [];
    for (let i = 0; i < hex.length; i++) {
        parts.push(hex[i]);
        if (i !== hex.length - 1) {
            parts.push(' ');
            if (groupSize > 0 && (i + 1) % groupSize === 0) parts.push(' ');
        }
    }
    return parts.join('').replace(/\s{2,}/g, '  ');
}

</script>

<style scoped>
.el-form-item {
    margin-bottom: 0;
}
</style>