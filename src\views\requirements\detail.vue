<template>

    <div>
        <div style="display: flex; align-items: center;">
            <h2 style="color:#666"> {{ requirement.name }} </h2>
            <el-button @click="onBack" style="margin-left: auto;">返回</el-button>
        </div>

        <div class="tool-container">
            <el-button text bg icon="Plus" @click="handleAddTestCase">添加用例</el-button>
        </div>

    </div>

    <el-collapse v-model="activeNames">

        <el-collapse-item class="custom-collapse-header" title="基本属性" name="1">
            <div>
                <el-row :gutter="20" style="padding: 20px">
                    <el-col :span="12">
                        <el-form label-width="auto">
                            <el-form-item label="所属模块：">
                                <span>{{ requirement.moduleName }}</span>
                            </el-form-item>
                            <el-form-item label="需求属性：">
                                <span>{{ propertyMap[requirement.property] || requirement.property }}</span>
                            </el-form-item>
                            <el-form-item label="优先级：">
                                <span>{{ requirement.priority }}</span>
                            </el-form-item>
                            <el-form-item label="需求来源：">
                                <span>{{ sourceMap[requirement.source] || requirement.source }}</span>
                            </el-form-item>
                            <el-form-item label="文档名称：">
                                <span>{{ requirement.documentName }}</span>
                            </el-form-item>
                        </el-form>
                    </el-col>
                    <el-col :span="12">
                        <el-form label-width="auto">
                            <el-form-item label="需求状态：">
                                <span>{{ stateMap[requirement.state] || requirement.state }}</span>
                            </el-form-item>
                            <el-form-item label="特性等级：">
                                <span>{{ peculiarityLevelMap[requirement.peculiarityLevel] ||
                                    requirement.peculiarityLevel }}</span>
                            </el-form-item>
                            <!-- <el-form-item label="状态：">
                                <span>{{ statusMap[requirement.status] || requirement.status }}</span>
                            </el-form-item> -->
                            <el-form-item label="需求类别：">
                                <span>{{ categoryMap[requirement.category] || requirement.category }}</span>
                            </el-form-item>
                            <el-form-item label="文档章节：">
                                <span>{{ requirement.documentChapter }}</span>
                            </el-form-item>
                        </el-form>
                    </el-col>
                </el-row>
            </div>
        </el-collapse-item>

        <el-collapse-item class="custom-collapse-header" title="详细信息" name="2">
            <el-tabs style="padding: 20px" v-model="tabActiveName">
                <el-tab-pane label="需求描述" name="需求描述">
                    <div>
                        <div v-html="descConverter(requirement.describe)"></div>
                    </div>
                </el-tab-pane>
                <el-tab-pane label="备注" name="备注">
                    <div>
                        <p>{{ requirement.remark }}</p>
                    </div>
                </el-tab-pane>
            </el-tabs>
        </el-collapse-item>

        <el-collapse-item class="custom-collapse-header" title="关联用例" name="3">
            <div style="padding: 0 20px">
                <div style="display: flex; align-items: center; margin-bottom: 10px; justify-content: end;">
                    <el-button plain type="primary" icon="Plus" @click="handleRelateTestCase">关联用例</el-button>
                </div>
                <el-table :data="testCases" style="width: 100%" stripe height="400">
                    <el-table-column prop="name" label="用例名称" width="400">
                        <template #default="{ row }">
                            <el-link type="primary" @click="onDetail(row)" :underline="false">{{ row.name }}</el-link>
                        </template>
                    </el-table-column>
                    <el-table-column prop="number" label="用例ID" min-width="400"></el-table-column>
                    <el-table-column prop="version" label="用例版本" min-width="400">
                        <template #default="{ row }">
                            <el-tag type="primary">{{ row.version }}.0</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" min-width="100" fixed="right">
                        <template #default="{ row }">
                            <el-button type="danger" @click="onRemove(row)" size="small">移除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </el-collapse-item>

    </el-collapse>

    <el-drawer v-model="drawerDetailVisible" :with-header="false" size="50%" :destroy-on-close="true">
        <TestCaseDetail :id="testCaseId" />
    </el-drawer>

    <el-dialog v-if="dialogRelateVisible" v-model="dialogRelateVisible" title="关联测试用例" width="1000"
        :close-on-click-modal="false">
        <RelateTestCases :requirement="requirement" @confirm="dialogRelateVisible = false; updateRequirement();"
            @cancel="dialogRelateVisible = false" />
    </el-dialog>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import http from '@/utils/http/http.js';
import { useRouter, useRoute } from 'vue-router';
import TestCaseDetail from '@/components/test_case.vue';
import RelateTestCases from './relate-test-cases.vue'

const tabActiveName = ref('需求描述');
const activeNames = ref(['1', '2', '3']);
const router = useRouter();
const route = useRoute();
const requirement = ref({});
const testCases = ref([]);
const testCaseId = ref(0);
const relatedTestCaseIds = ref([]);
const drawerDetailVisible = ref(false);
const dialogRelateVisible = ref(false);

const propertyMap = {
    "PRODUCT": '产品需求',
    "TITLE": '需求标题',
    "FUNCTION_SAFE": '功能安全需求',
    "NON_FUNCTION_SAFE": '非功能安全需求',
};

const reviewStatusMap = {
    "CANCEL": '已撤销',
    "INIT": '初始状态',
    "APPROVE": '审批中',
    "ACCEPT": '已评审',
    "REJECT": '已驳回',
};

const multiReviewStatusMap = {
    "CANCEL": '已撤销',
    "INIT": '未评审',
    "APPROVE": '审批中',
    "ACCEPT": '已评审',
    "REJECT": '已驳回',
};

const stateMap = {
    "NOT_START": '未开始',
    "DOING": '进行中',
    "REALIZE": '已实现',
}

const sourceMap = {
    "CLIENT": '客户需求',
    "INNER": '内部需求',
}

const peculiarityLevelMap = {
    "NORMAL": '一般特性',
    "KEY": '关键特性',
    "IMPORT": '重要特性',
}

const categoryMap = {
    "FUNCTION": '功能性需求',
    "NOT_FUNCTION": '非功能性需求',
}

const statusMap = {
    "ACCEPT": "接受",
}

function descConverter(desc) {
    if (!desc) return desc;
    return desc.replace('ipd.hiwaytech.com:56289', 'ipd.hiway.com:56289');
}

function handleAddTestCase() {
    router.push({ path: '/test_cases2/add', query: { project_number: requirement.value.projectCode, requirement_id: requirement.value.id } });
};

function onBack() {
    router.push({ path: '/requirements' });
}

function onDetail(row) {
    testCaseId.value = row.id;
    drawerDetailVisible.value = true;
};

function handleRelateTestCase() {
    dialogRelateVisible.value = true;
};

function onRemove(row) {
    http.post("/requirements/remove_test_cases", {
        requirement_id: requirement.value.id,
        requirement_version: requirement.value.version,
        requirement_number: requirement.value.num,
        test_cases: [{
            id: row.id,
            version: row.version,
        }],
    }).then(res => {
        updateRequirement();
    }).catch(err => {
        ElMessageBox.alert(
            err.response.data.msg,
            '移除失败',
            {
                confirmButtonText: '确定',
                type: 'warning',
            }
        )
    });
}

let updateRequirement = () => {
    http.get(`/requirements/${route.params.id}`).then(res => {
        let data = res.data.data;
        requirement.value = data;

        http.get('/requirements/test_cases', {
            params: {
                requirement_id: data.id,
                requirement_version: data.version
            }
        }).then(res => {
            testCases.value = res.data.data;
            relatedTestCaseIds.value = testCases.value.map(item => item.id);
        }).catch(err => {
            console.log(err);
        });

    }).catch(err => {
        console.log(err);
    });
}

onMounted(() => {
    updateRequirement();
});

</script>

<style lang="scss" scoped>
.tool-container {
    display: flex;
    justify-content: flex-start;
    margin-bottom: 10px;
}

:deep(.el-form-item) {
    margin: 0;
}

:deep(.el-collapse-item__arrow) {
    order: -1;
    margin: 0 8px 0 0;
}

:deep(.el-collapse-item__header) {
    font-size: 16px;
}

:deep(.custom-collapse-header .el-collapse-item__header) {
    font-weight: bold;
    color: "#666"
}
</style>