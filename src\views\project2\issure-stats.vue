<template>



    <el-card style="width: 100%;">
        <template #header>
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <h3>{{ title }}</h3>
            </div>
        </template>
        <EchartsComponent :options="options" height="600px" />
    </el-card>

</template>

<script setup>
import { ref, onMounted, inject, watch } from 'vue';
import EchartsComponent from '@/components/echartsComponent.vue';
import http from '@/utils/http/http.js';

const title = ref('用例-问题统计');
const options = ref(
    {
        xAxis: {
            data: []
        },
        legend: {
            orient: 'vertical',
            left: 10,
            top: 10,
        },
        grid: {
            left: '250px',
        },
        yAxis: {},
        series: []
    }
);

const project_number = inject('project_number');

function getSeriesColor(index) {
    const colors = [
        '#5470c6',  // 蓝色
        '#fac858',  // 黄色
        '#91cc75',  // 绿色
        '#ee6666',  // 红色
        '#73c0de',  // 浅蓝色
        '#3ba272',  // 深绿色
        '#fc8452',  // 橙色
        '#9a60b4',  // 紫色
        '#ea7ccc'   // 粉色
    ];
    return colors[index % colors.length];
}

watch(() => project_number.value, (newVal) => {
    if (newVal) {

        http.get('/projects/test_case_action_type_issue_stats', { params: { number: project_number.value } }).then((res) => {
            let data = res.data.data;

            options.value.xAxis.data = data.labels;

            options.value.series = data.data.map((item, index) => ({
                name: item.name,
                type: 'bar',
                data: item.data,

                label: {
                    show: true,
                    position: 'top',
                    color: '#333',
                    fontSize: 12,
                    fontWeight: 'normal',
                    formatter: '{c}'
                },
                itemStyle: {
                    borderRadius: [2, 2, 0, 0],
                    color: getSeriesColor(index)
                }
            }));
        });

    }
}, { immediate: true });


</script>

<style scoped>
:deep(.el-card__header) {
    border-bottom: 0;
    padding: 0 10px;
}
</style>