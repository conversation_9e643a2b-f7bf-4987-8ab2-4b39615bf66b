<template>
  <div class="pin-editor-panel">
    <!-- 面板头部 -->
    <div class="panel-header">
      <h3 class="panel-title">
        <el-icon style="color: #ccc; font-size: 14px; margin-right: 5px;"><EditPen /></el-icon>
        引脚编辑器
      </h3>
    </div>

    <!-- 面板内容 -->
    <div class="panel-content">
      <el-form :model="formData" label-position="left" label-width="140px" class="form-left-align">
        <!-- 基本信息 -->
        <div class="section-header">
          <el-divider content-position="left">
            <span class="section-title">
              <el-icon style="color:#409eff"><InfoFilled /></el-icon>
              基本信息
            </span>
          </el-divider>
        </div>
        
        <div class="config-section">
          <!-- 动态生成基本信息表单项 -->
          <template v-for="field in pinInfoFields" :key="field.key">
            <el-form-item :label="field.display" >
              <!-- 文本输入框 - 用于字符串和数字 -->
              <el-input
                v-if="!Array.isArray(field.value)"
                v-model="formData[field.key]"
                :disabled="field.key === 'pin_id' || isReadOnlyMode"
                size="small"
                :placeholder="`请输入${field.display}`"
                @input="handleFormChange"
              ></el-input>
              
              <!-- 下拉选择框 - 用于数组值 -->
              <el-select
                v-else
                v-model="formData[field.key]"
                :disabled="isReadOnlyMode"
                :placeholder="`请选择${field.display}`"
                size="small"
                @change="(value) => handleSelectChange(field.key, value)"
              >
                <!-- 为功能类型字段添加空的默认选项 -->
                <el-option
                  v-if="field.key === 'module'"
                  label="请选择功能类型"
                  value=""
                  disabled
                ></el-option>
                <el-option
                  v-for="option in field.value"
                  :key="option"
                  :label="option"
                  :value="option"
                ></el-option>
              </el-select>
            </el-form-item>
          </template>
        </div>

        <!-- 模块配置字段 -->
        <div v-if="localModuleConfig.length > 0" class="config-section" style=" margin-top: 60px;">
          <div class="section-header">
            <el-divider content-position="left">
              <span class="section-title">
                <el-icon  style="color:#409eff"><InfoFilled /></el-icon>
                模块配置 ({{ localModuleConfig.length }} 个字段)
              </span>
            </el-divider>
          </div>

          <template v-for="field in localModuleConfig" :key="field.key">
            <el-form-item :label="field.label">
              <!-- 下拉选择框 -->
              <el-select
                v-if="field.type === 'select'"
                v-model="formData[field.key]"
                :disabled="isReadOnlyMode"
                :placeholder="`请选择${field.label}`"
                size="small"
                clearable
                @change="(value) => handleModuleConfigComplete(field.key, value, field.label)"
              >
                <el-option
                  v-for="option in field.options"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                ></el-option>
              </el-select>

              <!-- 文本输入框 -->
              <el-input
                v-else-if="field.type === 'text'"
                v-model="formData[field.key]"
                :disabled="isReadOnlyMode"
                size="small"
                :placeholder="field.placeholder || `请输入${field.label}`"
                @blur="(event) => handleModuleConfigComplete(field.key, event.target.value, field.label)"
                @keyup.enter="(event) => handleModuleConfigComplete(field.key, event.target.value, field.label)"
              ></el-input>

              <!-- 数字输入框 -->
              <el-input-number
                v-else-if="field.type === 'number'"
                v-model="formData[field.key]"
                :disabled="isReadOnlyMode"
                size="small"
                :min="field.min || 0"
                :max="field.max || 999999"
                @blur="() => handleModuleConfigComplete(field.key, formData[field.key], field.label)"
                @change="(value) => handleModuleConfigComplete(field.key, value, field.label)"
              ></el-input-number>

              <!-- 开关 -->
              <el-switch
                v-else-if="field.type === 'switch'"
                v-model="formData[field.key]"
                :disabled="isReadOnlyMode"
                @change="(value) => handleModuleConfigComplete(field.key, value, field.label)"
              ></el-switch>

              <!-- 默认文本输入框 -->
              <el-input
                v-else
                v-model="formData[field.key]"
                :disabled="isReadOnlyMode"
                size="small"
                :placeholder="field.placeholder || `请输入${field.label}`"
                @blur="(event) => handleModuleConfigComplete(field.key, event.target.value, field.label)"
                @keyup.enter="(event) => handleModuleConfigComplete(field.key, event.target.value, field.label)"
              ></el-input>
            </el-form-item>
          </template>
        </div>

      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue';
import {
  InfoFilled
} from '@element-plus/icons-vue';

// Props
const props = defineProps({
  // 当前编辑的引脚数据
  pinData: {
    type: Object,
    default: () => ({})
  },
  // 引脚信息数据（来自response.data.data.pin_info）
  pinInfo: {
    type: Array,
    default: () => []
  },
  // 是否禁用编辑
  disabled: {
    type: Boolean,
    default: false
  },
  // 模块配置数据（来自requestChipModulConfig）
  moduleConfig: {
    type: Array,
    default: () => []
  }
});

// Emits
const emit = defineEmits([
  'update:pinData',
  'save',
  'reset',
  'change',
  'module-change',
  'request-module-config',
  'module-config-change',
  'field-change'
]);

// 表单数据
const formData = reactive({});

// 本地模块配置数据
const localModuleConfig = ref([]);

// 只读模式
const isReadOnlyMode = computed(() => props.disabled);

// 处理pin_info数据，转换为表单字段
const pinInfoFields = computed(() => {
  if (!props.pinInfo || !Array.isArray(props.pinInfo)) {
    console.log('DynamicPinEditor - pinInfo为空或不是数组:', props.pinInfo);
    return [];
  }

  const fields = props.pinInfo.map(field => ({
    key: field.key,
    display: field.display,
    value: field.value
  }));

  console.log('DynamicPinEditor - pinInfoFields计算结果:', fields);
  console.log('DynamicPinEditor - 是否包含module字段:', fields.some(f => f.key === 'module'));

  return fields;
});

// 初始化表单数据
const initializeFormData = () => {
  // 保存当前的module值（如果存在）
  const currentModuleValue = formData.module;

  // 清空现有数据
  Object.keys(formData).forEach(key => {
    delete formData[key];
  });

  // 首先从pinData初始化基本信息
  if (props.pinData) {
    Object.assign(formData, props.pinData);
    console.log('从pinData初始化基本信息:', props.pinData);
  }

  // 根据pin_info初始化表单数据
  pinInfoFields.value.forEach(field => {
    if (Array.isArray(field.value)) {
      // 对于功能类型（module字段），检查是否有缓存值
      if (field.key === 'module') {
        // 优先使用缓存值，如果没有则为空
        formData[field.key] = field.cachedValue || '';
        console.log(`🎯 初始化功能类型字段:`);
        console.log(`   - 字段信息:`, field);
        console.log(`   - 缓存值: ${field.cachedValue}`);
        console.log(`   - 最终设置值: ${formData[field.key]}`);
        console.log(`   - 可选项:`, field.value);
      } else {
        // 其他数组字段，默认选择第一个值
        formData[field.key] = field.value.length > 0 ? field.value[0] : '';
      }
    } else {
      // 如果是普通值，直接使用
      formData[field.key] = field.value;
    }
  });

  // 如果之前有module值，恢复它（这样可以保持缓存的值）
  if (currentModuleValue) {
    formData.module = currentModuleValue;
    console.log('恢复缓存的功能类型值:', currentModuleValue);
  }

  // 注意：模块配置字段的初始化现在在setModuleConfig方法中处理

  console.log('初始化表单数据:', formData);
};

// 下拉框变化处理
const handleSelectChange = (fieldKey, value) => {
  console.log(`字段 ${fieldKey} 变化为:`, value);

  // 检测是否是功能类型变化
  const isModuleChange = fieldKey === 'module';

  const changeData = {
    ...formData,
    isModuleChange: isModuleChange,
    changedField: fieldKey,
    changedValue: value
  };

  console.log('下拉框数据变化:', changeData);
  emit('change', changeData);

  // 触发字段变化事件，用于缓存
  emit('field-change', {
    pinId: formData.pin_id,
    fieldKey: fieldKey,
    fieldValue: value,
    formData: { ...formData }
  });

  // 如果是功能类型变化，触发特殊事件
  if (isModuleChange) {
    // 清空所有模块配置字段（保留基本字段）
    Object.keys(formData).forEach(key => {
      if (!['pin_id', 'pin_name', 'module'].includes(key)) {
        delete formData[key];
        console.log(`�️ 功能类型变化，清空字段: ${key}`);
      }
    });

    emit('module-change', {
      pinId: formData.pin_id,
      pinName: formData.pin_name,
      newModule: value,
      formData: { ...formData }
    });

    // 当功能类型有值时，请求模块配置
    if (value && value !== '') {
      console.log(`功能类型变化为 ${value}，请求模块配置`);
      emit('request-module-config', value);
    } else {
      // 如果功能类型为空，清空模块配置
      localModuleConfig.value = [];
    }
  }
};

// 模块配置完成处理（用户完成输入后触发）
const handleModuleConfigComplete = (fieldKey, value, fieldLabel) => {
  console.log(`🔄 模块配置字段 ${fieldKey} 修改完成:`, value);

  // 检查是否是模块配置字段
  const isModuleConfigField = !['pin_id', 'pin_name', 'module'].includes(fieldKey);

  if (isModuleConfigField && formData.module) {
    console.log(`✅ 触发模块配置完成事件: ${fieldKey} = ${value}`);

    // 触发模块配置变动事件
    emit('module-config-change', {
      pinId: formData.pin_id,
      pinName: formData.pin_name,
      moduleType: formData.module,
      fieldKey: fieldKey,
      fieldValue: value,
      fieldLabel: fieldLabel,
      formData: { ...formData }
    });

    // 同时触发字段变化事件，用于缓存
    emit('field-change', {
      pinId: formData.pin_id,
      fieldKey: fieldKey,
      fieldValue: value,
      formData: { ...formData }
    });
  }
};

// 表单变化处理
const handleFormChange = () => {
  const changeData = {
    ...formData,
    isModuleChange: false
  };

  console.log('表单数据变化:', changeData);
  emit('change', changeData);
};

// 保存处理
const handleSave = () => {
  emit('save', { ...formData });
  emit('update:pinData', { ...formData });
};

// 重置处理
const handleReset = () => {
  initializeFormData();
  emit('reset');
};

// 设置模块配置数据
const setModuleConfig = (config, fromCache = false, cachedFormData = null) => {
  console.log('🎯 DynamicPinEditor - 开始设置模块配置:', config);
  console.log('🔍 DynamicPinEditor - 配置数据类型:', typeof config);
  console.log('🔍 DynamicPinEditor - 是否为数组:', Array.isArray(config));
  console.log('🏷️ DynamicPinEditor - 配置来源:', fromCache ? '缓存' : 'API');
  console.log('📦 DynamicPinEditor - 缓存的表单数据:', cachedFormData);

  // 首先清空所有现有的模块配置字段
  console.log('🧹 清空现有的模块配置字段');
  Object.keys(formData).forEach(key => {
    // 保留基本字段，清空模块配置字段
    if (!['pin_id', 'pin_name', 'module'].includes(key)) {
      delete formData[key];
      console.log(`🗑️ 清空字段: ${key}`);
    }
  });

  // 设置本地模块配置
  localModuleConfig.value = config || [];
  console.log('✅ DynamicPinEditor - localModuleConfig设置完成');
  console.log('📊 DynamicPinEditor - localModuleConfig长度:', localModuleConfig.value.length);
  console.log('📋 DynamicPinEditor - localModuleConfig内容:', localModuleConfig.value);

  // 初始化模块配置字段的表单数据
  if (Array.isArray(config) && config.length > 0) {
    console.log('🔄 DynamicPinEditor - 开始初始化表单字段');
    config.forEach((field, index) => {
      console.log(`📝 DynamicPinEditor - 处理字段${index + 1}:`, {
        key: field.key,
        label: field.label,
        type: field.type,
        hasOptions: !!field.options
      });

      // 检查是否有缓存的值
      const cachedValue = cachedFormData && cachedFormData[field.key];
      const hasCachedValue = cachedValue !== undefined && cachedValue !== null && cachedValue !== '';

      if (fromCache && hasCachedValue) {
        // 如果是从缓存恢复且有缓存值，使用缓存值
        formData[field.key] = cachedValue;
        console.log(`🔄 从缓存恢复模块配置字段 ${field.key}: "${cachedValue}"`);
      } else {
        // 否则初始化为空值
        if (field.type === 'select') {
          formData[field.key] = ''; // 下拉框不显示默认值
        } else if (field.type === 'switch') {
          formData[field.key] = false; // 开关默认为关闭
        } else if (field.type === 'number') {
          formData[field.key] = ''; // 数字输入框为空
        } else {
          formData[field.key] = ''; // 文本输入框为空
        }
        console.log(`✅ 初始化模块配置字段 ${field.key} (无默认值): "${formData[field.key]}"`);
      }

      // 特别检查下拉框字段
      if (field.type === 'select') {
        console.log(`🔍 下拉框字段 ${field.key} 处理完成:`, {
          value: formData[field.key],
          isEmpty: formData[field.key] === '',
          fromCache: fromCache && hasCachedValue,
          options: field.options?.map(opt => opt.value) || []
        });
      }
    });
    console.log('🎉 DynamicPinEditor - 表单字段初始化完成');
    console.log('📋 当前表单数据状态:', { ...formData });
  } else {
    console.log('⚠️ DynamicPinEditor - 配置数据为空或不是数组，无法处理');
    console.log('📋 当前表单数据状态:', { ...formData });
  }
};

// 暴露方法给父组件
defineExpose({
  setModuleConfig
});

// 组件挂载时的初始化
import { onMounted } from 'vue';
onMounted(() => {
  console.log('DynamicPinEditor - 组件已挂载');
});

// 监听pinInfo变化
watch(() => props.pinInfo, () => {
  initializeFormData();
}, { immediate: true, deep: true });

// 注意：不再监听props.moduleConfig，改为通过setModuleConfig方法设置

// 监听pinData变化
watch(() => props.pinData, (newData) => {
  if (newData && Object.keys(newData).length > 0) {
    console.log('DynamicPinEditor - pinData变化:', newData);

    // 更新表单数据
    Object.keys(newData).forEach(key => {
      if (formData.hasOwnProperty(key)) {
        formData[key] = newData[key];
      }
    });

    // 特别处理pinType字段，映射到module字段
    if (newData.pinType !== undefined && newData.pinType !== '') {
      formData.module = newData.pinType;
      console.log(`DynamicPinEditor - 设置功能类型为: ${newData.pinType}`);
    }

    // 处理moduleConfig变化
    console.log('🔍 DynamicPinEditor - 检查moduleConfig:', newData.moduleConfig);
    console.log('🔍 DynamicPinEditor - moduleConfig是否存在:', !!newData.moduleConfig);
    console.log('🔍 DynamicPinEditor - moduleConfig是否为数组:', Array.isArray(newData.moduleConfig));
    console.log('🔍 DynamicPinEditor - moduleConfigFromCache:', newData.moduleConfigFromCache);
    console.log('🔍 DynamicPinEditor - cachedFormData:', newData.cachedFormData);

    if (Array.isArray(newData.moduleConfig)) {
      console.log('✅ DynamicPinEditor - 检测到moduleConfig变化:', newData.moduleConfig);
      console.log('📊 DynamicPinEditor - 配置字段数量:', newData.moduleConfig.length);
      console.log('🏷️ DynamicPinEditor - 配置来源:', newData.moduleConfigFromCache ? '缓存' : 'API');

      // 传递是否来自缓存的信息以及缓存的表单数据
      setModuleConfig(newData.moduleConfig, newData.moduleConfigFromCache, newData.cachedFormData);
    } else if (newData.moduleConfig !== undefined) {
      console.log('⚠️ DynamicPinEditor - moduleConfig存在但不是数组:', typeof newData.moduleConfig);
      // 设置为空数组，避免报错
      setModuleConfig([], false, newData.cachedFormData);
    } else {
      console.log('ℹ️ DynamicPinEditor - 没有检测到moduleConfig');
    }

    // 恢复缓存的基本字段数据（非模块配置字段）
    if (newData.cachedFormData) {
      console.log('🔄 DynamicPinEditor - 恢复缓存的基本字段数据:', newData.cachedFormData);
      Object.keys(newData.cachedFormData).forEach(key => {
        // 只恢复基本字段，模块配置字段已在setModuleConfig中处理
        if (['pin_id', 'pin_name', 'module'].includes(key) && formData.hasOwnProperty(key)) {
          formData[key] = newData.cachedFormData[key];
          console.log(`🔄 恢复基本字段 ${key}: ${newData.cachedFormData[key]}`);
        }
      });
    }
  }
}, { deep: true, immediate: true });
</script>

<style scoped>
.pin-editor-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: transparent; /* 移除背景，使用外层容器的背景 */
  /* border-radius: 8px; */ /* 移除边框圆角，使用外层容器的 */
  /* box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1); */ /* 移除阴影，使用外层容器的 */
  overflow: hidden;
}

.panel-header {
  padding: 16px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.panel-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
  gap: 8px;
}

.panel-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.form-left-align {
  width: 100%;
}

.section-header {
  margin-bottom: 20px;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #666;
  display: flex;
  align-items: center;
  gap: 6px;
}

.config-section {
  margin-bottom: 20px;
 
}

.config-section:last-child {
  margin-bottom: 0;
}

.action-buttons {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #e9ecef;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

:deep(.el-form-item) {
  margin-bottom: 16px;
}

:deep(.el-form-item__label) {
  color: #606266;
  font-weight: 500;
}

:deep(.el-input__inner),
:deep(.el-select .el-input__inner) {
  border-radius: 4px;
}

:deep(.el-divider__text) {
  background-color: #fff;
  padding: 0 16px;
}

:deep(.el-divider--horizontal) {
  margin: 12px 0;
}

/* 设置表单项标签加粗 */
:deep(.el-form-item__label) {
  font-weight: bold;
}
</style>
