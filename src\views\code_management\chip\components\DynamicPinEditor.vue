<template>
  <div class="pin-editor-panel">
    <!-- 面板头部 -->
    <div class="panel-header">
      <h3 class="panel-title">
        <el-icon style="color: #ccc; font-size: 14px; margin-right: 5px;"><EditPen /></el-icon>
        引脚编辑器
      </h3>
    </div>

    <!-- 面板内容 -->
    <div class="panel-content">
      <el-form :model="formData" label-position="left" label-width="140px" class="form-left-align">
        <!-- 基本信息 -->
        <div class="section-header">
          <el-divider content-position="left">
            <span class="section-title">
              <el-icon style="color:#409eff"><InfoFilled /></el-icon>
              基本信息
            </span>
          </el-divider>
        </div>
        
        <div class="config-section">
          <!-- 动态生成基本信息表单项 -->
          <template v-for="field in pinInfoFields" :key="field.key">
            <el-form-item :label="field.display" >
              <!-- 文本输入框 - 用于字符串和数字 -->
              <el-input
                v-if="!Array.isArray(field.value)"
                v-model="formData[field.key]"
                :disabled="field.key === 'pin_id' || isReadOnlyMode"
               
                :placeholder="field.placeholder !== undefined && field.placeholder !== null ? field.placeholder : `请输入${field.display}`"
                @input="handleFormChange"
              ></el-input>

              <!-- 下拉选择框 - 用于数组值 -->
              <el-select
                v-else
                v-model="formData[field.key]"
                :disabled="isReadOnlyMode"
                :placeholder="field.placeholder !== undefined && field.placeholder !== null ? field.placeholder : `请选择${field.display}`"
                
                @change="(value) => handleSelectChange(field.key, value)"
              >
                <!-- 为功能类型字段添加空的默认选项 -->
                <el-option
                  v-if="field.key === 'module'"
                  label="请选择功能类型"
                  value=""
                  disabled
                ></el-option>
                <el-option
                  v-for="option in field.value"
                  :key="option"
                  :label="option"
                  :value="option"
                ></el-option>
              </el-select>
            </el-form-item>
          </template>
        </div>

        <!-- 模块配置字段 -->
        <div v-if="localModuleConfig.length > 0" class="config-section" style=" margin-top: 60px;">
          <div class="section-header">
            <el-divider content-position="left">
              <span class="section-title">
                <el-icon  style="color:#409eff"><InfoFilled /></el-icon>
                模块配置 ({{ localModuleConfig.length }} 个字段)
              </span>
            </el-divider>
          </div>

          <template v-for="field in localModuleConfig" :key="field.key">
            <el-form-item :label="field.label">
              <!-- 下拉选择框 -->
              <el-select
                v-if="field.type === 'select'"
                :model-value="getFieldValue(field)"
                :disabled="isReadOnlyMode"
                :placeholder="field.placeholder !== undefined && field.placeholder !== null && field.placeholder !== ''? field.placeholder : `请选择${field.label}`"
               
                clearable
                @update:model-value="(value) => setFieldValue(field.key, value)"
                @change="(value) => handleModuleConfigComplete(field.key, value, field.label)"
              >
                <el-option
                  v-for="option in field.options"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                ></el-option>
              </el-select>

              <!-- 文本输入框 -->
              <el-input
                v-else-if="field.type === 'text'"
                :model-value="getFieldValue(field)"
                :disabled="isReadOnlyMode"
                
                :placeholder="field.placeholder || `请输入${field.label}`"
                @update:model-value="(value) => setFieldValue(field.key, value)"
                @blur="(event) => handleModuleConfigComplete(field.key, event.target.value, field.label)"
                @keyup.enter="(event) => handleModuleConfigComplete(field.key, event.target.value, field.label)"
              ></el-input>

              <!-- 数字输入框 -->
              <!-- 数字输入框（使用 tooltip 显示提示信息） -->
              <el-tooltip
                v-else-if="field.type === 'number'"
                :content="field.placeholder !== undefined && field.placeholder !== null ? String(field.placeholder) : `请输入${field.label}`"
                placement="top"
                :disabled="false"
              >
                <el-input-number
                  :model-value="getFieldValue(field)"
                  :placeholder="field.placeholder !== undefined && field.placeholder !== null && field.placeholder !== '' ? String(field.placeholder) : `请输入${field.label}`"
                  :disabled="isReadOnlyMode"
                  
                  :min="field.min || 0"
                  :max="field.max || 999999"
                  :step="field.step || 1"
                  :precision="field.precision || 0"
                  @update:model-value="(value) => setFieldValue(field.key, value)"
                  @blur="() => handleModuleConfigComplete(field.key, formData[field.key], field.label)"
                  @change="(value) => handleModuleConfigComplete(field.key, value, field.label)"
                  style="width: 100%"
                />
              </el-tooltip>

              <!-- 开关 -->
              <el-switch
                v-else-if="field.type === 'switch'"
                :model-value="getFieldValue(field)"
                :disabled="isReadOnlyMode"
                @update:model-value="(value) => setFieldValue(field.key, value)"
                @change="(value) => handleModuleConfigComplete(field.key, value, field.label)"
              ></el-switch>

              <!-- 默认文本输入框 -->
              <el-input
                v-else
                :model-value="getFieldValue(field)"
                :disabled="isReadOnlyMode"
               
                :placeholder="field.placeholder !== undefined && field.placeholder !== null && field.placeholder !== ''? field.placeholder : `请输入${field.label}`"
                @update:model-value="(value) => setFieldValue(field.key, value)"
                @blur="(event) => handleModuleConfigComplete(field.key, event.target.value, field.label)"
                @keyup.enter="(event) => handleModuleConfigComplete(field.key, event.target.value, field.label)"
              ></el-input>
            </el-form-item>
          </template>
        </div>

      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick, onMounted } from 'vue';
import {
  InfoFilled
} from '@element-plus/icons-vue';
import http from '@/utils/http/http';
import messageManager from '@/utils/messageManager';

// Props
const props = defineProps({
  // 当前编辑的引脚数据
  pinData: {
    type: Object,
    default: () => ({})
  },
  // 引脚信息数据（来自response.data.data.pin_info）
  pinInfo: {
    type: Array,
    default: () => []
  },
  // 是否禁用编辑
  disabled: {
    type: Boolean,
    default: false
  },
  // 模块配置数据（来自requestChipModulConfig）
  moduleConfig: {
    type: Array,
    default: () => []
  },
  // 项目信息（用于请求）
  projectCode: {
    type: String,
    default: ''
  },
  projectName: {
    type: String,
    default: ''
  },
  gitlab: {
    type: String,
    default: ''
  },
  projectBranch: {
    type: String,
    default: ''
  },
  branchStatus: {
    type: String,
    default: ''
  },
  chipName: {
    type: String,
    default: ''
  }
});

// Emits
const emit = defineEmits([
  'update:pinData',
  'save',
  'reset',
  'change',
  'module-change',
  'request-module-config',
  'module-config-change',
  'field-change'
]);

// 表单数据
const formData = reactive({});

// 本地模块配置数据
const localModuleConfig = ref([]);

// 存储字段的原始值，用于权限检查时的回退
const fieldOriginalValues = ref({});

// 只读模式
const isReadOnlyMode = computed(() => props.disabled);

// 处理pin_info数据，转换为表单字段
const pinInfoFields = computed(() => {
  if (!props.pinInfo || !Array.isArray(props.pinInfo)) {
    console.log('DynamicPinEditor - pinInfo为空或不是数组:', props.pinInfo);
    return [];
  }

  const fields = props.pinInfo.map(field => ({
    key: field.key,
    display: field.display,
    value: field.value,
    placeholder: field.placeholder,
    cachedValue: field.cachedValue // 🎯 传递缓存值
  }));

  console.log('DynamicPinEditor - pinInfoFields计算结果:', fields);
  console.log('DynamicPinEditor - 是否包含module字段:', fields.some(f => f.key === 'module'));

  // 🎯 专门检查module字段的cachedValue
  const moduleField = fields.find(f => f.key === 'module');
  if (moduleField) {
    console.log('🎯 DynamicPinEditor - module字段详情:', moduleField);
    console.log('🎯 DynamicPinEditor - module字段cachedValue:', moduleField.cachedValue);
  }

  return fields;
});

// 初始化表单数据
const initializeFormData = () => {
  // 保存当前的module值（如果存在）
  const currentModuleValue = formData.module;

  // 清空现有数据
  Object.keys(formData).forEach(key => {
    delete formData[key];
  });

  // 首先从pinData初始化基本信息
  if (props.pinData) {
    Object.assign(formData, props.pinData);
    console.log('从pinData初始化基本信息:', props.pinData);
  }

  // 根据pin_info初始化表单数据
  pinInfoFields.value.forEach(field => {
    if (Array.isArray(field.value)) {
      // 对于功能类型（module字段），检查是否有缓存值
      if (field.key === 'module') {
        // 优先使用缓存值，如果没有则为空
        formData[field.key] = field.cachedValue || '';
        console.log(`🎯 初始化功能类型字段:`);
        console.log(`   - 字段信息:`, field);
        console.log(`   - 缓存值: ${field.cachedValue}`);
        console.log(`   - 最终设置值: ${formData[field.key]}`);
        console.log(`   - 可选项:`, field.value);
      } else {
        // 其他数组字段，默认选择第一个值
        formData[field.key] = field.value.length > 0 ? field.value[0] : '';
      }
    } else {
      // 如果是普通值，直接使用
      formData[field.key] = field.value;
    }
  });

  // 如果之前有module值，恢复它（这样可以保持缓存的值）
  if (currentModuleValue) {
    formData.module = currentModuleValue;
    console.log('恢复缓存的功能类型值:', currentModuleValue);
  }

  // 注意：模块配置字段的初始化现在在setModuleConfig方法中处理

  console.log('初始化表单数据:', formData);
};

// 下拉框变化处理
const handleSelectChange = (fieldKey, value) => {
  console.log(`字段 ${fieldKey} 变化为:`, value);

  // 检测是否是功能类型变化
  const isModuleChange = fieldKey === 'module';

  const changeData = {
    ...formData,
    isModuleChange: isModuleChange,
    changedField: fieldKey,
    changedValue: value
  };

  console.log('下拉框数据变化:', changeData);
  emit('change', changeData);

  // 触发字段变化事件，用于缓存
  emit('field-change', {
    pinId: formData.pin_id,
    fieldKey: fieldKey,
    fieldValue: value,
    formData: { ...formData }
  });

  // 如果是功能类型变化，触发特殊事件
  if (isModuleChange) {
    // 清空所有模块配置字段（保留基本字段）
    Object.keys(formData).forEach(key => {
      if (!['pin_id', 'pin_name', 'module'].includes(key)) {
        delete formData[key];
        console.log(`�️ 功能类型变化，清空字段: ${key}`);
      }
    });

    emit('module-change', {
      pinId: formData.pin_id,
      pinName: formData.pin_name,
      newModule: value,
      formData: { ...formData }
    });

    // 当功能类型有值时，直接在子组件中请求模块配置
    if (value && value !== '') {
      console.log(`功能类型变化为 ${value}，直接请求模块配置`);
      handleRequestModuleConfig(value);
    } else {
      // 如果功能类型为空，清空模块配置
      localModuleConfig.value = [];
    }
  }
};

// 模块配置完成处理（用户完成输入后触发）
const handleModuleConfigComplete = (fieldKey, value, fieldLabel) => {
  try {
    console.log(`🔄 模块配置字段 ${fieldKey} 修改完成:`, value);

  // 检查是否是模块配置字段
  const isModuleConfigField = !['pin_id', 'pin_name', 'module'].includes(fieldKey);

  if (isModuleConfigField && formData.module) {
    // 在修改前保存原始值（如果还没有保存的话）
    if (fieldOriginalValues.value[fieldKey] === undefined) {
      fieldOriginalValues.value[fieldKey] = formData[fieldKey];
      console.log(`💾 首次保存字段 ${fieldKey} 的原始值:`, fieldOriginalValues.value[fieldKey]);
    } else {
      console.log(`📋 字段 ${fieldKey} 的原始值已存在:`, fieldOriginalValues.value[fieldKey], '当前值:', formData[fieldKey]);
    }

    console.log(`✅ 触发模块配置完成事件: ${fieldKey} = ${value}`);

    // 触发模块配置变动事件
    emit('module-config-change', {
      pinId: formData.pin_id,
      pinName: formData.pin_name,
      moduleType: formData.module,
      fieldKey: fieldKey,
      fieldValue: value,
      fieldLabel: fieldLabel,
      formData: { ...formData }
    });

    // 同时触发字段变化事件，用于缓存
    emit('field-change', {
      pinId: formData.pin_id,
      fieldKey: fieldKey,
      fieldValue: value,
      formData: { ...formData }
    });
  }
  } catch (error) {
    console.error('❌ handleModuleConfigComplete 错误:', error);
    console.error('❌ 错误堆栈:', error.stack);
  }
};

// 表单变化处理
const handleFormChange = () => {
  const changeData = {
    ...formData,
    isModuleChange: false
  };

  console.log('表单数据变化:', changeData);
  emit('change', changeData);
};

// 保存处理
const handleSave = () => {
  emit('save', { ...formData });
  emit('update:pinData', { ...formData });
};

// 重置处理
const handleReset = () => {
  initializeFormData();
  emit('reset');
};

// 获取字段值，如果当前值为空且有placeholder，则使用placeholder作为默认值
const getFieldValue = (field) => {
  const currentValue = formData[field.key];

  // 如果当前值不为空，直接返回
  if (currentValue !== undefined && currentValue !== null && currentValue !== '') {
    return currentValue;
  }

  // 如果当前值为空，检查是否有placeholder可以作为默认值
  if (field.placeholder !== undefined && field.placeholder !== null && field.placeholder !== '') {
    if (field.type === 'number') {
      // 数字类型：检查placeholder是否为有效数字
      // const numValue = Number(field.placeholder);
      // if (!isNaN(numValue)) {
      //   // 自动设置默认值到formData
      //   formData[field.key] = numValue;
      //   return numValue;
      // }
        const numValue = Number(field.placeholder);
        if (!isNaN(numValue)) {
          formData[field.key] = numValue;
          return numValue;
        } else {
          console.error(`DynamicPinEditor - 数字类型字段 ${field.key} 的placeholder "${field.placeholder}" 不是有效数字`);
          formData[field.key] = 0;  // 🔁 兜底设置 0，避免 el-input-number 报错
          return 0;
        }
    } else if (field.type === 'select') {
      // 下拉框类型：检查placeholder是否在选项中
      const validOption = field.options?.find(opt => opt.value === field.placeholder);
      if (validOption) {
        // 自动设置默认值到formData
        formData[field.key] = field.placeholder;
        return field.placeholder;
      }
      // 如果placeholder不在选项中，返回null以显示placeholder提示
      return null;
    } else if (field.type === 'switch') {
      // 开关类型：检查placeholder是否为布尔值
      if (field.placeholder === 'true' || field.placeholder === true) {
        formData[field.key] = true;
        return true;
      } else if (field.placeholder === 'false' || field.placeholder === false) {
        formData[field.key] = false;
        return false;
      }
    } else {
      // 文本类型：直接使用placeholder作为默认值
      formData[field.key] = field.placeholder;
      return field.placeholder;
    }
  }

  // 如果没有有效的placeholder，根据字段类型返回合适的默认值
  if (field.type === 'select') {
    return null; // 下拉框返回null以显示placeholder
  } else if (field.type === 'number') {
    return null; // 数字类型返回null，避免类型错误
  } else if (field.type === 'switch') {
    return false; // 开关默认为false
  } else {
    return currentValue || ''; // 文本类型返回空字符串
  }
};

// 设置字段值
const setFieldValue = (fieldKey, value) => {
  // 对于数字类型字段，确保值的类型正确
  const field = localModuleConfig.value.find(f => f.key === fieldKey);
  if (field && field.type === 'number') {
    // 如果是数字类型，确保值为数字或null
    if (value === '' || value === undefined) {
      formData[fieldKey] = null;
    } else {
      const numValue = Number(value);
      formData[fieldKey] = isNaN(numValue) ? null : numValue;
    }
  } else {
    formData[fieldKey] = value;
  }

  // 如果是功能类型字段，缓存到引脚功能类型缓存中
  if (fieldKey === 'module' && formData.pin_id) {
    cachePinModuleType(formData.pin_id, value);
  }
};

// 设置模块配置数据
const setModuleConfig = (config, fromCache = false, cachedFormData = null) => {
  try {
    console.log('🎯 DynamicPinEditor - 开始设置模块配置:', config);
    console.log('🔍 DynamicPinEditor - 配置数据类型:', typeof config);
    console.log('🔍 DynamicPinEditor - 是否为数组:', Array.isArray(config));
    console.log('🏷️ DynamicPinEditor - 配置来源:', fromCache ? '缓存' : 'API');
    console.log('📦 DynamicPinEditor - 缓存的表单数据:', cachedFormData);

  // 首先清空所有现有的模块配置字段和原始值
  console.log('🧹 清空现有的模块配置字段');
  Object.keys(formData).forEach(key => {
    // 保留基本字段，清空模块配置字段
    if (!['pin_id', 'pin_name', 'module'].includes(key)) {
      delete formData[key];
      console.log(`🗑️ 清空字段: ${key}`);
    }
  });

  // 清空原始值缓存
  fieldOriginalValues.value = {};
  console.log('🧹 清空原始值缓存');

  // 设置本地模块配置
  localModuleConfig.value = config || [];
  console.log('✅ DynamicPinEditor - localModuleConfig设置完成');
  console.log('📊 DynamicPinEditor - localModuleConfig长度:', localModuleConfig.value.length);
  console.log('📋 DynamicPinEditor - localModuleConfig内容:', localModuleConfig.value);

  // 初始化模块配置字段的表单数据
  if (Array.isArray(config) && config.length > 0) {
    console.log('🔄 DynamicPinEditor - 开始初始化表单字段');
    config.forEach((field, index) => {
      console.log(`📝 DynamicPinEditor - 处理字段${index + 1}:`, {
        key: field.key,
        label: field.label,
        type: field.type,
        hasOptions: !!field.options
      });

      // 检查是否有缓存的值
      const cachedValue = cachedFormData && cachedFormData[field.key];
      const hasCachedValue = cachedValue !== undefined && cachedValue !== null && cachedValue !== '';

      if (fromCache && hasCachedValue) {
        // 如果是从缓存恢复且有缓存值，使用缓存值
        formData[field.key] = cachedValue;
        console.log(`🔄 从缓存恢复模块配置字段 ${field.key}: "${cachedValue}"`);
      } else {
        // 否则初始化为空值
        if (field.type === 'select') {
          // 下拉框：检查placeholder是否在选项中
          if (field.placeholder && field.options?.find(opt => opt.value === field.placeholder)) {
            formData[field.key] = field.placeholder; // 使用有效的placeholder作为默认值
          } else {
            formData[field.key] = null; // 使用null以显示placeholder提示
          }
        } else if (field.type === 'switch') {
          formData[field.key] = false; // 开关默认为关闭
        } else if (field.type === 'number') {
          // 数字输入框：如果有placeholder且是有效数字，使用它作为默认值，否则为null
          if (field.placeholder !== undefined && field.placeholder !== null && field.placeholder !== '' && !isNaN(Number(field.placeholder))) {
            formData[field.key] = Number(field.placeholder);
          } else {
            formData[field.key] = null; // 数字输入框为null，避免类型错误
          }
        } else {
          formData[field.key] = ''; // 文本输入框为空
        }
        console.log(`✅ 初始化模块配置字段 ${field.key} (无默认值): "${formData[field.key]}"`);
      }

      // 保存字段的原始值（用于权限检查时的回退）
      fieldOriginalValues.value[field.key] = formData[field.key];
      console.log(`💾 保存字段 ${field.key} 的原始值:`, fieldOriginalValues.value[field.key]);

      // 特别检查下拉框字段
      if (field.type === 'select') {
        console.log(`🔍 下拉框字段 ${field.key} 处理完成:`, {
          value: formData[field.key],
          isEmpty: formData[field.key] === '',
          fromCache: fromCache && hasCachedValue,
          options: field.options?.map(opt => opt.value) || []
        });
      }
    });
    console.log('🎉 DynamicPinEditor - 表单字段初始化完成');
    console.log('📋 当前表单数据状态:', { ...formData });
  } else {
    console.log('⚠️ DynamicPinEditor - 配置数据为空或不是数组，无法处理');
    console.log('📋 当前表单数据状态:', { ...formData });
  }
  } catch (error) {
    console.error('❌ setModuleConfig 错误:', error);
    console.error('❌ 错误堆栈:', error.stack);
    // 确保即使出错也设置一个空的配置
    localModuleConfig.value = [];
  }
};

// 请求芯片模块配置
// const requestChipModulConfig = async (moduleType, pinId = null) => {
//   try {
//     console.log(`🔄 DynamicPinEditor - 开始请求芯片模块配置, 功能类型: ${moduleType}, 引脚ID: ${pinId}`);

//    // 如果功能类型为 'OTHER' 或 'CAN'，直接返回空配置，不发送请求
// if (moduleType === 'OTHER' || moduleType === 'CAN') {
//       console.log(`ℹ️ 功能类型为 'other'，跳过请求，返回空配置`);
//       return {
//         config: [],
//         fromCache: false,
//         pinId: pinId
//       };
//     }

//     // 取消缓存机制，每次都发送请求获取最新数据
//     console.log(`🚀 每次都发送请求获取最新模块配置: ${moduleType}`);

//     const response = await http.get('/code_management/chip_modul', {
//       params: {
//         project_code: props.projectCode,
//         project_name: props.projectName,
//         gitlab: props.gitlab,
//         project_branch: props.projectBranch,
//         branch_status: props.branchStatus,
//         chip_name: props.chipName,
//         pin_number: pinId,
//         module: moduleType
//       }
//     });

//     console.log("DynamicPinEditor - 芯片模块配置完整响应:", response);
//     console.log("DynamicPinEditor - 芯片模块配置数据:", response.data);

//     if (response.data && response.data.status === 1) {
//       // 处理成功响应
//       const responseData = response.data.data || {};
//       console.log('🎉 DynamicPinEditor - 芯片模块配置API响应数据:', responseData);

//       // 检查数据结构，提取实际的配置数组
//       let modulConfig = [];
//       if (Array.isArray(responseData)) {
//         // 如果直接是数组
//         modulConfig = responseData;
//       } else if (responseData.module && Array.isArray(responseData.module)) {
//         // 如果是 {module: Array} 结构
//         modulConfig = responseData.module;
//       } else {
//         console.warn('⚠️ 未知的模块配置数据结构:', responseData);
//         // 返回空数组而不是null，避免报错
//         modulConfig = [];
//       }

//       console.log('✅ DynamicPinEditor - 提取的模块配置数组:', modulConfig);
//       console.log('📊 DynamicPinEditor - 配置字段数量:', modulConfig.length);

//       // 如果是空数组，给出友好提示
//       if (Array.isArray(modulConfig) && modulConfig.length === 0) {
//         console.log('ℹ️ DynamicPinEditor - 该功能类型暂无模块配置字段');
//       } else if (Array.isArray(modulConfig) && modulConfig.length > 0) {
//         // 打印每个配置字段的详细信息
//         modulConfig.forEach((field, index) => {
//           console.log(`📋 DynamicPinEditor - 字段${index + 1}:`, {
//             key: field.key,
//             label: field.label,
//             type: field.type,
//             options: field.options?.length || 0
//           });
//         });
//       }

//       // 取消缓存机制，不再保存到缓存
//       console.log(`✅ DynamicPinEditor - 获取到模块配置，不使用缓存: 引脚${pinId}, 类型${moduleType}, 字段数${modulConfig.length}`);

//       return {
//         config: modulConfig,
//         fromCache: false,
//         pinId: pinId
//       };
//     } else {
//       console.error('❌ DynamicPinEditor - 芯片模块配置API返回状态不正确:', response.data);
//       messageManager.error('获取芯片模块配置失败: ' + (response.data?.message || '未知错误'));
//       return null;
//     }
//   } catch (error) {
//     console.error('DynamicPinEditor - 请求芯片模块配置失败:', error);
//     messageManager.error('请求芯片模块配置失败: ' + error.message);
//     return null;
//   }
// };
const requestChipModulConfig = async (moduleType, pinId = null) => {
  try {
    console.log(`🔄 开始请求芯片模块配置 - 功能类型: ${moduleType}, 引脚ID: ${pinId}`);

    // 跳过特殊功能类型
    if (moduleType === 'OTHER' || moduleType === 'CAN') {
      console.log(`ℹ️ 功能类型为 '${moduleType}'，跳过请求，返回空配置`);
      return {
        config: [],
        fromCache: false,
        pinId
      };
    }

    // 请求后端接口
    const response = await http.get('/code_management/chip_modul', {
      params: {
        project_code: props.projectCode,
        project_name: props.projectName,
        gitlab: props.gitlab,
        project_branch: props.projectBranch,
        branch_status: props.branchStatus,
        chip_name: props.chipName,
        pin_number: pinId,
        module: moduleType
      }
    });

    const resData = response.data;
    console.log("📥 模块配置响应:", resData);

    if (resData && resData.status === 1) {
      let modulConfig = [];

      if (Array.isArray(resData.data)) {
        modulConfig = resData.data;
      } else if (Array.isArray(resData.data?.module)) {
        modulConfig = resData.data.module;
      }

      console.log(`✅ 模块配置字段数量: ${modulConfig.length}`);

      modulConfig.forEach((field, index) => {
        console.log(`📋 字段 ${index + 1}:`, {
          key: field.key,
          label: field.label,
          type: field.type,
          options: field.options?.length || 0
        });
      });

      return {
        config: modulConfig,
        fromCache: false,
        pinId
      };
    } else {
      console.error('❌ API返回失败:', resData);
      messageManager.error('获取芯片模块配置失败: ' + (resData?.message || '未知错误'));
      return null;
    }
  } catch (error) {
    console.error('❌ 请求异常:', error);
    messageManager.error('请求芯片模块配置失败: ' + error.message);
    return null;
  }
};


// 处理请求模块配置
const handleRequestModuleConfig = async (moduleType) => {
  console.log('🎯 DynamicPinEditor - 收到请求模块配置事件:', moduleType);

  // 获取当前引脚ID
  const currentPinId = formData.pin_id;
  if (!currentPinId) {
    console.error('❌ DynamicPinEditor - 当前引脚ID为空，无法请求模块配置');
    return;
  }

  try {
    // 调用requestChipModulConfig获取模块配置，传递引脚ID
    const result = await requestChipModulConfig(moduleType, currentPinId);

    if (result && result.config) {
      const moduleConfig = result.config;

      console.log(`✅ DynamicPinEditor - 获取到模块配置 (来自API):`, moduleConfig);
      console.log('📊 DynamicPinEditor - 模块配置字段数量:', moduleConfig.length);

      if (moduleConfig.length === 0) {
        console.log('ℹ️ DynamicPinEditor - 该功能类型暂无模块配置字段，设置为空数组');
      }

      // 直接设置模块配置数据，不使用缓存
      setModuleConfig(moduleConfig, false, null);
      console.log('✅ DynamicPinEditor - 模块配置已设置完成');
    } else {
      console.warn('⚠️ DynamicPinEditor - 模块配置数据格式不正确，设置为空数组:', result);
      // 即使数据格式不正确，也设置为空数组，避免报错
      setModuleConfig([], false, null);
    }
  } catch (error) {
    console.error('❌ DynamicPinEditor - 请求模块配置失败:', error);
  }
};

// 暴露方法给父组件
defineExpose({
  setModuleConfig,
  requestChipModulConfig,
  handleRequestModuleConfig
});

// 组件挂载时的初始化

onMounted(() => {
  console.log('DynamicPinEditor - 组件已挂载');
});

// 监听pinInfo变化
watch(() => props.pinInfo, () => {
  initializeFormData();
}, { immediate: true, deep: true });

// 注意：不再监听props.moduleConfig，改为通过setModuleConfig方法设置

// 监听pinData变化
watch(() => props.pinData, (newData) => {
  try {
    if (newData && Object.keys(newData).length > 0) {
      console.log('DynamicPinEditor - pinData变化:', newData);

      // 检查是否需要回退字段值
      if (newData.shouldRevert && newData.revertFieldKey) {
        const fieldKey = newData.revertFieldKey;
        const originalValue = fieldOriginalValues.value[fieldKey];
        const currentValue = formData[fieldKey];

        console.log(`🔄 数据回退处理 - 字段: ${fieldKey}`);
        console.log(`📋 当前值:`, currentValue);
        console.log(`🔙 原始值:`, originalValue);
        console.log(`💾 所有原始值:`, fieldOriginalValues.value);

        if (originalValue !== undefined) {
          // 只回退指定的字段，不影响其他字段
          formData[fieldKey] = originalValue;
          console.log(`✅ 字段 ${fieldKey} 已回退到原始值:`, originalValue);

          // 触发响应式更新，确保UI更新
          nextTick(() => {
            console.log(`🔄 字段 ${fieldKey} 回退后的值:`, formData[fieldKey]);
          });
        } else {
          console.warn(`⚠️ 字段 ${fieldKey} 没有找到原始值，无法回退`);
        }

        // 不直接修改newData对象，避免Vue响应式问题
        console.log('✅ 回退处理完成，跳过其他更新');
        return; // 不继续处理其他更新
      }

    // 更新表单数据
    Object.keys(newData).forEach(key => {
      if (formData.hasOwnProperty(key)) {
        formData[key] = newData[key];
      }
    });

    // 特别处理pinType字段，映射到module字段
    if (newData.pinType !== undefined && newData.pinType !== '') {
      formData.module = newData.pinType;
      console.log(`DynamicPinEditor - 设置功能类型为: ${newData.pinType}`);
    }

    // 处理moduleConfig变化
    console.log('🔍 DynamicPinEditor - 检查moduleConfig:', newData.moduleConfig);
    console.log('🔍 DynamicPinEditor - moduleConfig是否存在:', !!newData.moduleConfig);
    console.log('🔍 DynamicPinEditor - moduleConfig是否为数组:', Array.isArray(newData.moduleConfig));
    console.log('🔍 DynamicPinEditor - moduleConfigFromCache:', newData.moduleConfigFromCache);
    console.log('🔍 DynamicPinEditor - cachedFormData:', newData.cachedFormData);

    if (Array.isArray(newData.moduleConfig)) {
      console.log('✅ DynamicPinEditor - 检测到moduleConfig变化:', newData.moduleConfig);
      console.log('📊 DynamicPinEditor - 配置字段数量:', newData.moduleConfig.length);
      console.log('🏷️ DynamicPinEditor - 配置来源:', newData.moduleConfigFromCache ? '缓存' : 'API');

      // 传递是否来自缓存的信息以及缓存的表单数据
      setModuleConfig(newData.moduleConfig, newData.moduleConfigFromCache, newData.cachedFormData);
    } else if (newData.moduleConfig !== undefined) {
      console.log('⚠️ DynamicPinEditor - moduleConfig存在但不是数组:', typeof newData.moduleConfig);
      // 设置为空数组，避免报错
      setModuleConfig([], false, newData.cachedFormData);
    } else {
      console.log('ℹ️ DynamicPinEditor - 没有检测到moduleConfig');
    }

      // 恢复缓存的基本字段数据（非模块配置字段）
      if (newData.cachedFormData) {
        console.log('🔄 DynamicPinEditor - 恢复缓存的基本字段数据:', newData.cachedFormData);
        Object.keys(newData.cachedFormData).forEach(key => {
          // 只恢复基本字段，模块配置字段已在setModuleConfig中处理
          if (['pin_id', 'pin_name', 'module'].includes(key) && formData.hasOwnProperty(key)) {
            formData[key] = newData.cachedFormData[key];
            console.log(`🔄 恢复基本字段 ${key}: ${newData.cachedFormData[key]}`);
          }
        });
      }
    }
  } catch (error) {
    console.error('❌ DynamicPinEditor watcher 错误:', error);
    console.error('❌ 错误堆栈:', error.stack);
    // 不抛出错误，避免影响Vue的响应式系统
  }
}, { deep: true, immediate: true });

// 组件挂载时检查功能类型并请求模块配置
onMounted(() => {
  console.log('🎯 DynamicPinEditor - 组件已挂载，检查功能类型');

  // 获取当前的功能类型
  const currentModuleType = formData.module;
  const currentPinId = formData.pin_id;

  console.log('📋 当前功能类型:', currentModuleType);
  console.log('📋 当前引脚ID:', currentPinId);

  // 判断功能类型是否有值
  if (currentModuleType && currentModuleType !== '' && currentModuleType !== 'OTHER') {
    console.log(`✅ 功能类型有值: ${currentModuleType}，开始请求模块配置`);

    // 检查是否有引脚ID
    if (currentPinId) {
      console.log(`🚀 引脚ID: ${currentPinId}，发起模块配置请求`);
      handleRequestModuleConfig(currentModuleType);
    } else {
      console.warn('⚠️ 引脚ID为空，无法请求模块配置');
    }
  } else {
    console.log('ℹ️ 功能类型没有值或为OTHER类型，跳过模块配置请求');
    console.log('💡 提示：当用户选择功能类型后，将自动请求对应的模块配置');
  }
});
</script>

<style scoped>
.pin-editor-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: transparent; /* 移除背景，使用外层容器的背景 */
  /* border-radius: 8px; */ /* 移除边框圆角，使用外层容器的 */
  /* box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1); */ /* 移除阴影，使用外层容器的 */
  overflow: hidden;
}

.panel-header {
  padding: 16px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.panel-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
  gap: 8px;
}

.panel-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.form-left-align {
  width: 100%;
}

.section-header {
  margin-bottom: 20px;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #666;
  display: flex;
  align-items: center;
  gap: 6px;
}

.config-section {
  margin-bottom: 20px;
 
}

.config-section:last-child {
  margin-bottom: 0;
}

.action-buttons {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #e9ecef;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

:deep(.el-form-item) {
  margin-bottom: 16px;
}

:deep(.el-form-item__label) {
  color: #606266;
  font-weight: 500;
}

:deep(.el-input__inner),
:deep(.el-select .el-input__inner) {
  border-radius: 4px;
}

/* 设置数字输入框的宽度 */
:deep(.el-input-number) {
  width: 30% !important;
}

:deep(.el-input-number .el-input__inner) {
  text-align: center;
}


:deep(.el-divider__text) {
  background-color: #fff;
  padding: 0 16px;
}

:deep(.el-divider--horizontal) {
  margin: 12px 0;
}

/* 设置表单项标签加粗 */
:deep(.el-form-item__label) {
  font-weight: bold;
}

</style>
