<template>
  <div class="chip-config-section">
    <div class="card">
      <h2 class="card-title">
        <el-icon style="color: #ccc; font-size: 14px; margin-right: 5px;">
          <Setting />
        </el-icon>
        芯片配置
      </h2>
      <el-form 
        label-position="left" 
        label-width="120px" 
        class="form-left-align"
        @submit.prevent
      >
        <el-form-item label="芯片型号">
          <el-select
            :model-value="props.chipOptions.length > 0 ? props.chipOptions[0].value : ''"
            placeholder="选择芯片型号"
            @change="handleChipModelChange"
            :loading="props.chipOptionsLoading"
          >
            <el-option
              v-for="chip in props.chipOptions"
              :key="chip.value"
              :label="chip.label"
              :value="chip.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="VCC 电压(V)">
          <el-input-number
            :model-value="props.chipForm.vccVoltage || 3.3"
            :min="0"
            :max="5"
            :step="0.1"
            :precision="1"
            @change="handleVccVoltageChange"
          />
        </el-form-item>

        <el-form-item label="时钟频率(MHz)">
          <el-input-number
            :model-value="props.chipForm.clockFreq || 160"
            :min="0"
            :max="200"
            :step="1"
            @change="handleClockFreqChange"
          />
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue';
import { Setting } from '@element-plus/icons-vue';

// 定义 props
const props = defineProps({
  chipForm: {
    type: Object,
    required: true,
    default: () => ({
      chipModel: '',
      vccVoltage: 3.3,
      clockFreq: 160,
      ioConfig: []
    })
  },
  chipOptions: {
    type: Array,
    default: () => []
  },
  chipOptionsLoading: {
    type: Boolean,
    default: false
  }
});

// 定义 emits
const emit = defineEmits(['update:chipForm', 'chip-model-change', 'vcc-voltage-change', 'clock-freq-change']);

// 事件处理函数
const handleChipModelChange = (value) => {
  console.log('ChipConfig: 芯片型号变更:', value);
  emit('chip-model-change', value);
  // 同时更新整个 chipForm
  emit('update:chipForm', {
    ...props.chipForm,
    chipModel: value
  });
};

const handleVccVoltageChange = (value) => {
  console.log('ChipConfig: VCC电压变更:', value);
  emit('vcc-voltage-change', value);
  // 同时更新整个 chipForm
  emit('update:chipForm', {
    ...props.chipForm,
    vccVoltage: value
  });
};

const handleClockFreqChange = (value) => {
  console.log('ChipConfig: 时钟频率变更:', value);
  emit('clock-freq-change', value);
  // 同时更新整个 chipForm
  emit('update:chipForm', {
    ...props.chipForm,
    clockFreq: value
  });
};
</script>

<style scoped>
.chip-config-section {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.card {
  background: #fff;
  padding: 20px 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebedf0;
  position: relative;
  z-index: 10;
  background: #fff;
  flex-shrink: 0;
}

.form-left-align {
  height: 100%;
  overflow-y: auto;
  padding-right: 5px;
}

.form-left-align.el-form .el-form-item .el-form-item__label {
  text-align: left !important;
  padding-right: 10px !important;
  width: 120px !important;
  min-width: 120px !important;
  flex-shrink: 0 !important;
  display: inline-block !important;
}

.form-left-align.el-form .el-form-item .el-form-item__content {
  flex: 1 !important;
  margin-left: 0 !important;
}

.form-left-align.el-form .el-form-item {
  display: flex !important;
  align-items: center !important;
  margin-bottom: 20px !important;
}

.form-left-align .el-select,
.form-left-align .el-input-number {
  width: 100%;
}

.form-left-align .el-input-number .el-input__inner {
  width: 100%;
}
</style>
