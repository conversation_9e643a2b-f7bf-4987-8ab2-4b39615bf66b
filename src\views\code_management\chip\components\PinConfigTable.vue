<template>
  <div class="left-bottom-row">
    <div class="card">
      <h2 class="card-title">
        <el-icon style="color: #ccc; font-size: 14px; margin-right: 5px;">
          <Operation />
        </el-icon>
        引脚配置表
      </h2>
      
      <div class="table-container" ref="tableContainer">
        <!-- 表格工具栏 -->
        <div class="table-toolbar">
          <div class="search-section">
            <el-input 
              v-model="localSearchKey" 
              placeholder="搜索引脚编号/名称..." 
              class="search-input"
              @input="handleSearchChange"
            />
          </div>
          
          <div class="io-type-section">
            <el-form-item class="io-type-form-item">
              <el-checkbox-group 
                v-model="localIoConfig" 
                @change="handleIoConfigChange"
              >
                <el-checkbox
                  v-for="ioType in availableIoTypes"
                  :key="ioType.value"
                  :value="ioType.value"
                  :title="ioType.description"
                >
                  {{ ioType.label }}
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </div>
        </div>
        
        <!-- 数据表格 -->
        <el-table
          :data="paginatedData"
          border
          stripe
          style="width: 100%;"
          :header-cell-style="{
            background: '#f5f7fa',
            color: '#606266',
            textAlign: 'center',
            fontSize: '14px',
            height: '50px'
          }"
          :cell-style="{
            textAlign: 'center',
            fontSize: '12px',
            height: '40px'
          }"
          @row-click="handleRowClick"
          highlight-current-row
          size="small"
          table-layout="fixed"
          :fit="true"
        >
          <el-table-column prop="pinId" label="引脚编号" width="120" sortable show-overflow-tooltip />
          <el-table-column prop="pinName" label="引脚名称" width="200" sortable show-overflow-tooltip />
          <el-table-column prop="pinType" label="引脚类型" :width="pinTypeColumnWidth" sortable show-overflow-tooltip>
            <template #default="scope">
              <el-tag
                v-if="scope.row.pinType && scope.row.pinType !== '未配置'"
                v-bind="getTypeTagStyle(scope.row.pinType) ?
                  { type: getTypeTagStyle(scope.row.pinType) } : {}"
                size="small"
                class="type-tag"
              >
                {{ scope.row.pinType }}
              </el-tag>
              <el-tag v-else type="info" size="small" class="type-tag">
                未配置
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="可选类型" :width="availableTypesColumnWidth" sortable show-overflow-tooltip>
            <template #default="scope">
              <div class="type-tags">
                <el-tag
                  v-for="(type, index) in scope.row.availableTypes"
                  :key="index"
                  v-bind="getTypeTagStyle(type) ?
                    { type: getTypeTagStyle(type) } : {}"
                  size="small"
                  class="type-tag"
                  style="margin-right: 4px; margin-bottom: 2px;"
                >
                  {{ type }}
                </el-tag>
                <span v-if="!scope.row.availableTypes || scope.row.availableTypes.length === 0" class="no-type-text">无可选类型</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="status" label="状态" width="150" sortable show-overflow-tooltip>
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.status)" size="small">
                {{ scope.row.status }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="150" fixed="right">
            <template #default="scope">
              <el-button 
                type="primary" 
                size="small" 
                @click.stop="handleEdit(scope.row)"
              >
                <i class="el-icon-edit"></i> 编辑
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <!-- 分页器 -->
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="localCurrentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="localPageSize"
          layout="prev, pager, next, jumper, total, sizes"
          :total="filteredTotal"
          class="pagination"
          small
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue';
import { Operation } from '@element-plus/icons-vue';

// 定义 props
const props = defineProps({
  pinTableData: {
    type: Array,
    required: true,
    default: () => []
  },
  searchKey: {
    type: String,
    default: ''
  },
  ioConfig: {
    type: Array,
    default: () => []
  },
  currentPage: {
    type: Number,
    default: 1
  },
  pageSize: {
    type: Number,
    default: 10
  },
  availableIoTypes: {
    type: Array,
    required: true,
    default: () => []
  },
  typeStyleConfig: {
    type: Object,
    required: true,
    default: () => ({})
  }
});

// 定义 emits
const emit = defineEmits([
  'update:searchKey',
  'update:ioConfig', 
  'update:currentPage',
  'update:pageSize',
  'row-click',
  'edit-pin'
]);

// 本地响应式数据
const localSearchKey = ref(props.searchKey);
const localIoConfig = ref([...props.ioConfig]);
const localCurrentPage = ref(props.currentPage);
const localPageSize = ref(props.pageSize);

// 响应式列宽计算
const tableContainer = ref(null);
const containerWidth = ref(0);

// 计算可用宽度（总宽度减去固定列宽度）
const availableWidth = computed(() => {
  // 固定列宽度：引脚编号(80) + 引脚名称(100) + 状态(80) + 操作(100) = 360
  const fixedColumnsWidth = 120 + 200 + 150 + 150;
  const totalWidth = containerWidth.value || 800; // 默认800px
  return Math.max(totalWidth - fixedColumnsWidth, 300); // 最小300px可用宽度
});

// 引脚类型列宽度（30%）
const pinTypeColumnWidth = computed(() => {
  const width = Math.floor(availableWidth.value * 0.3);
  console.log('引脚类型列宽度:', width, '可用宽度:', availableWidth.value);
  return width;
});

// 可选类型列宽度（70%）
const availableTypesColumnWidth = computed(() => {
  const width = Math.floor(availableWidth.value * 0.7);
  console.log('可选类型列宽度:', width, '可用宽度:', availableWidth.value);
  return width;
});

// 监听 props 变化
watch(() => props.searchKey, (newVal) => {
  localSearchKey.value = newVal;
});

watch(() => props.ioConfig, (newVal) => {
  localIoConfig.value = [...newVal];
}, { deep: true });

watch(() => props.currentPage, (newVal) => {
  localCurrentPage.value = newVal;
});

watch(() => props.pageSize, (newVal) => {
  localPageSize.value = newVal;
});

// 筛选逻辑已移至计算属性中

// 计算属性
const filteredData = computed(() => {
  // 直接在计算属性中实现过滤逻辑，确保响应性
  const key = localSearchKey.value.trim().toLowerCase();

  console.log('🔍 计算属性重新计算:');
  console.log('   - 搜索关键字:', key);
  console.log('   - 选中的IO类型:', localIoConfig.value);
  console.log('   - 原始数据数量:', props.pinTableData.length);

  // 调试：显示所有引脚类型统计
  if (props.pinTableData.length > 0) {
    const typeStats = {};
    props.pinTableData.forEach((item) => {
      const type = item.pinType || '空值';
      typeStats[type] = (typeStats[type] || 0) + 1;
    });
    console.log('   - 所有引脚类型统计:', typeStats);
  }

  // 如果选中了OTHER，显示已知类型列表
  if (localIoConfig.value.includes('未配置')) {
    const knownTypes = ['GPIO', 'ADC', 'PWM', 'IIC', 'SPI', 'UART', 'EXIT', 'OTHER'];
    console.log('   - OTHER模式：排除的已知类型:', knownTypes);
  }

  // 调试：显示引脚类型统计
  if (props.pinTableData.length > 0) {
    const typeStats = {};
    props.pinTableData.forEach((item) => {
      const type = item.pinType || '空';
      typeStats[type] = (typeStats[type] || 0) + 1;
    });
    console.log('   - 引脚类型统计:', typeStats);

    // 显示前3个引脚的详细信息
    console.log('   - 前3个引脚详情:');
    props.pinTableData.slice(0, 3).forEach((item) => {
      console.log(`     引脚${item.pinId}: 当前类型="${item.pinType}", 可选类型=${JSON.stringify(item.availableTypes)}`);
    });
  }

  const filtered = props.pinTableData.filter(item => {
    // 搜索关键字筛选
    const searchMatch = !key ||
      item.pinId.toLowerCase().includes(key) ||
      item.pinName.toLowerCase().includes(key);

    // IO类型筛选 - 根据引脚类型（当前已配置的类型）过滤
    const mainTypes = ['GPIO', 'ADC', 'PWM', 'IIC', 'SPI', 'UART', 'EXIT', 'OTHER'];

    const typeMatch = localIoConfig.value.length === 0 ||
      localIoConfig.value.some(selectedType => {
        const rawType = item.pinType;
        const pinType = (rawType === null || rawType === undefined)
          ? ''
          : String(rawType).trim().toUpperCase();

        if (selectedType === '未配置') {
          const isOtherType =
            !pinType ||                // null、undefined、空字符串
            pinType === 'Null' ||      // 字符串 "null"
            pinType === '未配置' ||    // 显示未配置
            !mainTypes.includes(pinType); // 不属于主类型

          return isOtherType;
        } else {
          return pinType === selectedType.toUpperCase();
        }
      });



    return searchMatch && typeMatch;
  });

  console.log('   - 过滤后数据数量:', filtered.length);
  console.log('   - 过滤后数据类型:', typeof filtered);
  console.log('   - 过滤后数据是否为数组:', Array.isArray(filtered));

  // 如果选择了OTHER类型，显示匹配到的引脚详情
  if (localIoConfig.value.includes('OTHER') && filtered.length > 0) {
    console.log('   - OTHER类型匹配到的引脚:');
    filtered.forEach((item, index) => {
      console.log(`     [${index}] 引脚${item.pinId}: "${item.pinType}"`);
    });

    // 检查数据结构
    console.log('   - 过滤数据样本:', filtered.slice(0, 2));
  }

  // 确保返回一个普通数组，避免响应式代理问题
  return [...filtered];
});

const filteredTotal = computed(() => filteredData.value.length);

const paginatedData = computed(() => {
  const start = (localCurrentPage.value - 1) * localPageSize.value;
  const end = start + localPageSize.value;

  // 确保 filteredData 是一个可切片的数组
  const dataArray = Array.isArray(filteredData.value) ? filteredData.value : [...filteredData.value];
  const result = dataArray.slice(start, end);

  console.log('分页计算:');
  console.log('  - 当前页:', localCurrentPage.value);
  console.log('  - 每页大小:', localPageSize.value);
  console.log('  - 开始索引:', start);
  console.log('  - 结束索引:', end);
  console.log('  - 过滤数据总数:', filteredData.value.length);
  console.log('  - 过滤数据类型:', typeof filteredData.value);
  console.log('  - 过滤数据是否为数组:', Array.isArray(filteredData.value));
  console.log('  - 分页结果数量:', result.length);

  if (filteredData.value.length > 0 && result.length === 0) {
    console.log('  - 🚨 分页异常: 有过滤数据但分页结果为空');
    console.log('  - 过滤数据前3项:', filteredData.value.slice(0, 3));
  }

  return result;
});

// 事件处理函数
const handleSearchChange = (value) => {
  emit('update:searchKey', value);
};

const handleIoConfigChange = (value) => {
  console.log('🔄 IO配置变化:', value);
  console.log('📊 当前表格数据总数:', props.pinTableData.length);
  emit('update:ioConfig', value);
};

const handleSizeChange = (val) => {
  emit('update:pageSize', val);
};

const handleCurrentChange = (val) => {
  emit('update:currentPage', val);
};

const handleRowClick = (row) => {
  emit('row-click', row);
};

const handleEdit = (row) => {
  emit('edit-pin', row);
};

// 样式相关函数
const getStatusType = (status) => {
  return status === '已配置' ? 'success' :
         status === '未配置' ? 'info' :
         status === '保留' ? 'warning' :
         status === '故障' ? 'danger' : 'info';
};

const getTypeTagStyle = (type) => {
  const tagStyle = props.typeStyleConfig[type]?.tagStyle ||
                   props.typeStyleConfig.DEFAULT?.tagStyle;
  return tagStyle === '' ? undefined : tagStyle;
};

// 更新容器宽度
const updateContainerWidth = () => {
  if (tableContainer.value) {
    const newWidth = tableContainer.value.offsetWidth;
    containerWidth.value = newWidth;
    console.log('PinConfigTable - 容器宽度更新:', newWidth);
  }
};

// 窗口大小变化监听
const handleResize = () => {
  updateContainerWidth();
};

// 生命周期钩子
onMounted(() => {
  nextTick(() => {
    updateContainerWidth();
  });
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
});
</script>

<style scoped>
.left-bottom-row {
  width: 100%;
  display: flex;
  flex-direction: column;
}

.card {
  background: #fff;
  padding: 20px 0px 20px 0px;
  border-radius: 8px;
  /* box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1); */
  height: 100%;
  display: flex;
  flex-direction: column;
  /* overflow: hidden; */
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  padding: 0px 20px 10px 20px;
  margin-bottom: 15px;
  border-bottom: 1px solid #ebedf0;
  position: relative;
  z-index: 10;
  background: #fff;
  flex-shrink: 0;
}

.table-container {
  background: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  margin: 5px 10px;
  overflow: hidden;
  width: calc(100% - 20px);
  min-width: 0;
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 确保表格能够正确响应容器宽度 */
.table-container :deep(.el-table) {
  width: 100% !important;
}

.table-container :deep(.el-table__body-wrapper) {
  overflow-x: auto;
}

.table-container :deep(.el-table th),
.table-container :deep(.el-table td) {
  padding: 8px 4px;
  text-align: center;
}

.table-toolbar {
  display: flex;
  align-items: center;
  padding: 15px 30px;
  height: 60px;
  background: #f5f7fa;
  color: #606266;
  border-bottom: 1px solid #e4e7ed;
  margin: 0px;
  gap: 10px;
}

.search-section {
  flex-shrink: 0;
}

.search-input {
  flex: 1;
  max-width: 300px;
}

.io-type-section {
  flex: 1;
  display: flex;
  justify-content: flex-start;
}

.io-type-form-item {
  margin-bottom: 0 !important;
}

.io-type-form-item .el-form-item__content {
  margin-left: 0 !important;
}

.io-type-form-item .el-checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 2px;
}

.pagination {
  margin: 0px;
  padding: 15px 30px;
  text-align: right;
  background: #f5f7fa;
  border-top: 1px solid #e4e7ed;
}

.table-container .el-table {
  border: none !important;
  border-radius: 0 !important;
  width: 100% !important;
  table-layout: auto;
  max-width: 100%;
  overflow-x: auto;
}

/* 确保表格不会超出容器宽度 */
.table-container .el-table__body-wrapper {
  overflow-x: auto;
}

.table-container .el-table__header-wrapper {
  overflow-x: hidden;
}

.table-container .el-table::before {
  display: none;
}

.type-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
  max-width: 100%;
  overflow: hidden;
}

.type-tag {
  margin: 1px;
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 3px;
  white-space: nowrap;
  max-width: 80px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.no-type-text {
  color: #909399;
  font-size: 12px;
  font-style: italic;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .table-container {
    margin: 5px 0;
    width: 100%;
  }

  .table-container .el-table {
    font-size: 12px;
  }

  .table-container .el-table .el-table__cell {
    padding: 6px 4px;
  }
}

@media (max-width: 768px) {
  .table-container {
    margin: 5px 0;
    width: 100%;
  }

  .table-toolbar {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }

  .search-section {
    width: 100%;
  }

  .io-type-section {
    width: 100%;
  }

  .table-container .el-table .el-table__cell {
    padding: 4px 2px;
    font-size: 11px;
  }

  /* 在小屏幕上进一步压缩列宽 */
  .table-container .el-table .el-table__cell:nth-child(1) {
    width: 60px !important;
    min-width: 60px !important;
  }

  .table-container .el-table .el-table__cell:nth-child(2) {
    min-width: 80px !important;
  }

  .table-container .el-table .el-table__cell:nth-child(3) {
    width: 70px !important;
    min-width: 70px !important;
  }

  .table-container .el-table .el-table__cell:nth-child(4) {
    min-width: 80px !important;
  }

  .table-container .el-table .el-table__cell:nth-child(5) {
    width: 60px !important;
    min-width: 60px !important;
  }

  .table-container .el-table .el-table__cell:nth-child(6) {
    width: 60px !important;
    min-width: 60px !important;
  }
}
</style>
