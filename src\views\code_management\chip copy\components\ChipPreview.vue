<template>
  <div class="chip-preview-section">
    <div class="card">
      <h2 class="title">
        <el-icon style="color: #ccc; font-size: 14px; margin-right: 5px;">
          <View />
        </el-icon>
        芯片预览 ({{ pinCount }}引脚)
      </h2>
      
      <div class="chip-preview-box">
        <!-- 传递引脚数据到 ChipPinDiagram 组件 -->
        <ChipPinDiagram
          :pinCount="pinCount"
          :chipModel="chipModel"
          :pins="pins"
          :pinSize="0.4"
          :showLegend="true"
          :typeStyleConfig="typeStyleConfig"
          :highlightedPin="highlightedPin"
          @pinClick="handlePinClick"
          @pinEdit="handlePinEdit"
        />
      </div>
      
      <!-- 引脚信息显示 -->
      <div class="pin-info" v-if="highlightedPin">
        <span class="current-pin">当前引脚: {{ highlightedPin }}</span>
        <span class="pin-type" v-if="getCurrentPinType(highlightedPin)">
          - {{ getCurrentPinType(highlightedPin) }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { View } from '@element-plus/icons-vue';
import ChipPinDiagram from '@/views/code_management/chip/components/ChipPinDiagram.vue';

// 定义 props
const props = defineProps({
  pinCount: {
    type: Number,
    required: true,
    default: 0
  },
  chipModel: {
    type: String,
    required: true,
    default: ''
  },
  pins: {
    type: Array,
    required: true,
    default: () => []
  },
  typeStyleConfig: {
    type: Object,
    required: true,
    default: () => ({})
  },
  highlightedPin: {
    type: [String, Number],
    default: null
  }
});

// 定义 emits
const emit = defineEmits(['pin-click', 'pin-edit']);

// 事件处理函数
const handlePinClick = (pinId) => {
  emit('pin-click', pinId);
};

const handlePinEdit = (pinData) => {
  emit('pin-edit', pinData);
};

// 获取当前引脚类型
const getCurrentPinType = (pinId) => {
  if (!pinId || !props.pins.length) return '';
  
  const pin = props.pins.find(item => item.pinId === pinId.toString());
  return pin ? pin.displayType || '未配置' : '未知';
};
</script>

<style scoped>
.chip-preview-section {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
}

.card {
  background: #fff;
  padding: 20px 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebedf0;
  position: relative;
  z-index: 10;
  background: #fff;
  flex-shrink: 0;
}

.chip-preview-box {
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1;
  overflow: hidden;
  background-color: #fff;
  border-radius: 4px;
  margin-bottom: 8px;
  position: relative;
  z-index: 1;
  padding: 10px;
  width: 100%;
  height: 100%;
}

.pin-info {
  text-align: center;
  font-size: 14px;
  margin-top: 5px;
  flex-shrink: 0;
}

.current-pin {
  font-weight: bold;
  color: #409eff;
}

.pin-type {
  font-weight: bold;
  color: #67c23a;
}
</style>
