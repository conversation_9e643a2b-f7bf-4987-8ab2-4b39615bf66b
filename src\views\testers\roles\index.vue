<template>
    <div style="display: flex; flex-direction: column; height: calc(100vh - 170px);">
    <div class="tool-bar-container">
        <el-button icon="Plus" type="primary" @click="handleAdd">新增</el-button>
        <div style="margin-left: auto; display: flex; gap: 10px;">
            <el-tooltip class="box-item" effect="dark" content="重置已添加的筛选条件" placement="top-start">
                <el-button text bg @click="handleReset">重置</el-button>
            </el-tooltip>
            <filterButton @click="onFilterStatusChange" :count="filterCount" />
            <el-button icon="Refresh" text bg @click="handleRefresh">刷新</el-button>
        </div>
    </div>

    <div class="filter-container" v-if="showFilterContainer">
        <el-input v-model="form.name_re" placeholder="请输入名称" @keyup.enter="onFilter" clearable>
            <template #append>
                <el-button icon="Search" @click="onFilter"></el-button>
            </template>
        </el-input>
    </div>

    <el-table :data="tableData" stripe border style="width: 100%;flex: 1;" class="table-container">

        <el-table-column prop="name" label="名称" min-width="200" align="center"></el-table-column>
        <el-table-column prop="desc" label="描述" min-width="500" align="center"></el-table-column>

        <el-table-column label="操作" min-width="190" fixed="right" align="center">
            <template #default="{ row }">
                <div style="display: flex; justify-content: center; gap: 10px;">
                    <el-button type="primary" size="small" @click="handleEdit(row)">编辑</el-button>
                    <el-button type="primary" size="small" @click="handleSkillConf(row)">技能</el-button>
                    <el-button type="danger" size="small" @click="handleDelete(row)">删除</el-button>
                </div>
            </template>
        </el-table-column>

    </el-table>
</div>

    <div class="pagination-container">
        <el-pagination :page-sizes="[1, 10, 15, 20, 25, 50, 100]" layout="prev, pager, next, jumper, total, sizes"
            v-model:current-page="form.page" v-model:page-size="form.pagesize" :total="total" background
            @change="onPageChange" />
    </div>

    <el-dialog v-if="dialogAddVisible" v-model="dialogAddVisible" title="添加角色" width="1000"
        :close-on-click-modal="false">
        <Add @submit="onAddSuccess" @cancel="dialogAddVisible = false" />
    </el-dialog>

    <el-dialog v-if="dialogEditVisible" v-model="dialogEditVisible" title="编辑角色" width="800"
        :close-on-click-modal="false">
        <Edit @submit="onEditSuccess" @cancel="dialogEditVisible = false" :r_id="r_id" />
    </el-dialog>

</template>


<script setup>
import { ref, reactive, onMounted } from 'vue';
import http from '@/utils/http/http.js';
import Add from './add.vue';
import Edit from './edit.vue';
import filterButton from '@/components/filterButton.vue';
import { useRouter } from 'vue-router'; 
import { useAccessStat } from '@/utils/accessStat';

useAccessStat('/tester_roles/list', '岗位角色列表');

const tableData = ref([]);
const router = useRouter();
const dialogAddVisible = ref(false);
const dialogEditVisible = ref(false);
const r_id = ref(0);

let form = reactive({
    name: '',
    number: '',
});

let total = ref(0);
const filterCount = ref(0);
let showFilterContainer = ref(false);

function onFilterStatusChange() {
    showFilterContainer.value = !showFilterContainer.value;
};

function update_table() {
    http.get('/testers/roles', { params: form }).then(res => {
        tableData.value = res.data.data.results;
        total.value = res.data.data.count;
    });

    filterCount.value = Object.keys(form).filter(key => !['page', 'pagesize'].includes(key)).reduce((count, key) => {
        if (form[key] == '' || form[key] == undefined || form[key] == null || form[key].length == 0) {
            return count;
        } else {
            return count + 1;
        }
    }, 0)
};

function handleReset() {
    form = reactive({
        page: 1,
        pagesize: 10,
        project_number: form.project_number,
    });
    update_table();
};

function onPageChange() {
    update_table();
};

function onFilter() {
    form.page = 1;
    form.pagesize = 10;
    update_table();
};

function handleAdd() {
    dialogAddVisible.value = true;
};

function handleEdit(row) {
    r_id.value = row.id;
    dialogEditVisible.value = true;
};

function handleSkillConf(row) {
    router.push(`/tester_roles/${row.id}/skills`);
};

function handleDelete(row) {
    ElMessageBox.confirm(
        '确定删除吗?',
        '提示',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }
    ).then(() => {
        http.delete(`/testers/roles/${row.id}`).then(res => {
            ElMessage({
                message: '删除成功.',
                type: 'success',
            });
            update_table();
        }).catch(err => {
            ElMessage({
                type: 'error',
                message: err.response.data.msg
            });
        });
    }).catch(() => {
        ElMessage({
            type: 'info',
            message: '已取消删除'
        });
    });
};

function onAddSuccess() {
    dialogAddVisible.value = false;
    update_table();
};

function onEditSuccess() {
    dialogEditVisible.value = false;
    update_table();
};

function handleRefresh() {
    update_table();
};

onMounted(() => {
    update_table();
});

</script>


<style scoped>
.search-container {
    width: 100%;
    display: flex;
    justify-content: center;
    justify-items: center;
    height: 40px;
}

.search-input {
    width: 50%;
    max-width: 500px;
}

.search-button {
    border: 1px solid #dcdfe6;
}

.filter-container {
    width: 100%;
    display: flex;
    justify-content: flex-start;

    margin-bottom: 10px;

    .el-input {
        width: 50%;
        max-width: 300px;
        margin-right: 10px;
    }
}

.tool-bar-container {
    display: flex;
    justify-content: flex-start;
}
</style>