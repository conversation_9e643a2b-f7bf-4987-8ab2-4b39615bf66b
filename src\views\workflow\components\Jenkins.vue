<template>
  <div class="jenkins-config-form">
    <el-form :model="form" label-width="120px">
      <!-- 基础信息 -->
      <el-form-item label="Project Name">
        <el-input v-model="form.project_name" placeholder="hw-collectx" />
      </el-form-item>
      <el-form-item label="Git URL">
        <el-input v-model="form.git_url" placeholder="git@repo..." />
      </el-form-item>
      <el-form-item label="Agent">
        <el-select v-model="form.agent" placeholder="请选择Jenkins节点" style="width: 100%" :loading="agentLoading">
          <el-option
            v-for="node in jenkinNodes"
            :key="node.name"
            :label="node.display_name"
            :value="node.name"
            :disabled="node.status === 'offline'"
          >
            <span style="float: left">{{ node.display_name }}</span>
            <span style="float: right; color: #8492a6; font-size: 13px">
              <el-tag v-if="node.status === 'online'" type="success" size="small">在线</el-tag>
              <el-tag v-else-if="node.status === 'offline'" type="danger" size="small">离线</el-tag>
              <el-tag v-else type="warning" size="small">未知</el-tag>
            </span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="Branch">
        <el-input v-model="form.branch" placeholder="master" />
      </el-form-item>

      <el-divider content-position="left">Environment</el-divider>
      <el-form-item label="PROJECT_NUMBER">
        <el-input v-model="form.environment.PROJECT_NUMBER" v-if="form.environment" />
      </el-form-item>
      <el-form-item label="PROJECT_TYPE">
        <el-input v-model="form.environment.PROJECT_TYPE" v-if="form.environment" />
      </el-form-item>

      <el-divider content-position="left">Parameters</el-divider>
      <el-table :data="form.parameters" style="width: 100%" border>
        <el-table-column prop="name" label="Name" width="140">
          <template #default="scope">
            <el-input v-model="scope.row.name" />
          </template>
        </el-table-column>
        <el-table-column prop="default_value" label="Default Value" width="160">
          <template #default="scope">
            <el-input v-model="scope.row.default_value" />
          </template>
        </el-table-column>
        <el-table-column prop="description" label="Description">
          <template #default="scope">
            <el-input v-model="scope.row.description" />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="60">
          <template #default="scope">
            <el-button type="danger" size="small" @click="removeParameter(scope.$index)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-button type="primary" plain size="small" @click="addParameter">新增参数</el-button>

      <el-divider content-position="left">Stages</el-divider>
      
      <el-alert
        title="使用说明"
        type="info"
        show-icon
        :closable="false"
        style="margin-bottom: 16px;">
        <template #default>
          <ul style="margin: 0; padding-left: 20px; font-size: 13px;">
            <li>每个Stage执行一个逻辑任务（如：构建、标签、上传等），也可在单个Stage中组合相关命令</li>
            <li><strong>⚠️ BAT语法要求</strong>：<code style="background: #f5f7fa; padding: 2px 4px;">^</code> 必须是行的最后一个字符，后面不能有空格或其他内容</li>
            <li><strong>推荐写法</strong>：使用 <code style="background: #f5f7fa; padding: 2px 4px;">&&</code> 连接命令更简单可靠</li>
            <li><strong>示例</strong>：<code style="background: #f5f7fa; padding: 2px 4px;">git tag -a V1.2 -m "release V1.2" && git push origin V1.2</code></li>
            <li>使用 %变量名% 引用环境变量：<code style="background: #f5f7fa; padding: 2px 4px;">%PROJECT_NUMBER%</code></li>
          </ul>
        </template>
      </el-alert>
      
      <div v-for="(stage, idx) in form.stages" :key="idx" class="stage-block">
        <el-card shadow="never">
          <template #header>
            <el-row justify="space-between" align="middle" style="width:100%">
              <span>Stage {{ idx + 1 }}</span>
              <div style="display:flex;gap:8px;align-items:center;">
                <el-input v-model="stage.name" placeholder="Stage Name" style="width:200px" />
                <el-button type="danger" size="small" @click="removeStage(idx)">删除</el-button>
              </div>
            </el-row>
          </template>

          <div v-for="(cmd, cIdx) in stage.commands" :key="cIdx" class="command-block">
            <el-form-item :label="`Command ${cIdx + 1}`" label-width="100px">
              <el-input
                type="textarea"
                v-model="stage.commands[cIdx]"
                :rows="4"
                placeholder="bat command..."
              />
              <el-button type="danger" plain size="small" @click="removeCommand(idx, cIdx)">删除</el-button>
            </el-form-item>
          </div>
          <el-button type="primary" plain size="small" @click="addCommand(idx)">新增命令</el-button>
        </el-card>
      </div>
      <el-button type="primary" plain size="small" @click="addStage">新增 Stage</el-button>

      <el-divider />
      <el-form-item style="margin-bottom: 8px;">
        <el-button type="primary" @click="handleSave">保存</el-button>
        <el-button @click="$emit('cancel')">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { reactive, ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import http from '@/utils/http/http'

const props = defineProps({
  initialConfig: { type: Object, default: () => ({}) }
})
const emit = defineEmits(['save', 'cancel'])

// Jenkins节点相关状态
const jenkinNodes = ref([])
const agentLoading = ref(false)

const deepCopy = (obj) => JSON.parse(JSON.stringify(obj || {}))

// 默认表单数据
const defaultFormData = {
  project_name: 'hw-collectx',
  git_url: 'git@*********:python-team/hw-collectx.git',
  agent: 'slave_win10',
  branch: 'master',
  environment: {
    PROJECT_NUMBER: 'WPTSN11',
    PROJECT_TYPE: 'Tool'
  },
  parameters: [],
  stages: [
    {
      name: 'Build',
      commands: ['build.bat HWCollectX']
    },
    {
      name: 'Upload',
      commands: ['D:\\jenkins\\HWUpload.exe -p %PROJECT_NUMBER% -f HWCollectX/%APP_VERSION%/Windows -v %WORKSPACE%/version/version.properties -d %WORKSPACE%\\dist\\Release\\HWCollectX.zip -t %PROJECT_TYPE% -w False']
    }
  ]
}

// 安全的表单初始化函数
const mergeFormData = (initialConfig, defaultData) => {
  const result = deepCopy(defaultData)
  
  if (initialConfig && typeof initialConfig === 'object') {
    // 安全地合并基础字段
    if (initialConfig.project_name) result.project_name = initialConfig.project_name
    if (initialConfig.git_url) result.git_url = initialConfig.git_url
    if (initialConfig.agent) result.agent = initialConfig.agent
    if (initialConfig.branch) result.branch = initialConfig.branch
    
    // 安全地合并环境变量
    if (initialConfig.environment && typeof initialConfig.environment === 'object') {
      result.environment = { ...result.environment, ...initialConfig.environment }
    }
    
    // 安全地合并参数
    if (Array.isArray(initialConfig.parameters)) {
      result.parameters = initialConfig.parameters
    }
    
    // 安全地合并阶段
    if (Array.isArray(initialConfig.stages)) {
      result.stages = initialConfig.stages
    }
  }
  
  return result
}

// 初始化表单，安全地合并 initialConfig 和 defaultFormData
const form = reactive(mergeFormData(props.initialConfig, defaultFormData))

// 获取Jenkins节点列表
const fetchJenkinsNodes = async () => {
  agentLoading.value = true
  try {
    const response = await http.get('/auto_jenkins/jenkins/nodes/')
    if (response.data.success) {
      jenkinNodes.value = response.data.data
      // 如果当前选择的agent不在列表中，且列表有可用节点，自动选择第一个在线节点
      const currentAgent = form.agent
      const validAgents = jenkinNodes.value.filter(node => node.status === 'online')
      if (!jenkinNodes.value.find(node => node.name === currentAgent) && validAgents.length > 0) {
        form.agent = validAgents[0].name
      }
    } else {
      ElMessage.error('获取Jenkins节点失败: ' + response.data.message)
      // 失败时添加默认节点，保证功能可用
      jenkinNodes.value = [{ name: 'slave_win10', display_name: 'slave_win10', status: 'unknown' }]
    }
  } catch (error) {
    console.error('获取Jenkins节点失败:', error)
    ElMessage.error('获取Jenkins节点失败')
    // 失败时添加默认节点，保证功能可用
    jenkinNodes.value = [{ name: 'slave_win10', display_name: 'slave_win10', status: 'unknown' }]
  } finally {
    agentLoading.value = false
  }
}

// 组件挂载时获取节点列表
onMounted(() => {
  fetchJenkinsNodes()
})

const addParameter = () => {
  form.parameters.push({ name: '', default_value: '', description: '' })
}
const removeParameter = (idx) => {
  form.parameters.splice(idx, 1)
}

const addStage = () => {
  form.stages.push({ name: '', commands: [''] })
}
const removeStage = (idx) => {
  form.stages.splice(idx, 1)
}

const addCommand = (stageIdx) => {
  form.stages[stageIdx].commands.push('')
}
const removeCommand = (stageIdx, cmdIdx) => {
  form.stages[stageIdx].commands.splice(cmdIdx, 1)
}

// %VAR% -> ${env.VAR} 并转义反斜杠用于Groovy
const convertPlaceholders = (str) => {
  // 先转换占位符
  let result = str.replace(/%([A-Za-z0-9_]+)%/g, (_, v) => '${env.' + v + '}');
  // 然后转义反斜杠用于Groovy字符串
  result = result.replace(/\\/g, '\\\\');
  return result;
}

// 格式化 BAT 脚本：智能处理续行和多命令
const formatBatScript = (str) => {
  console.log('formatBatScript 输入:', str);
  const lines = str.split(/\r?\n/).map(l => l.trim()).filter(l => l.length > 0);
  console.log('分割后的行:', lines);
  
  if (lines.length === 0) return '';
  if (lines.length === 1) return lines[0];
  
  // 检测独立命令的关键词
  const commandStarters = ['git', 'echo', 'cd', 'mkdir', 'copy', 'del', 'npm', 'yarn', 'mvn', 'docker', 'call', 'start', 'taskkill'];
  
  let result = '';
  let i = 0;
  
  while (i < lines.length) {
    let currentLine = lines[i];
    console.log(`处理第${i+1}行: "${currentLine}"`);
    
    // 检查当前行是否以命令开头
    const isCommand = commandStarters.some(cmd => currentLine.toLowerCase().startsWith(cmd + ' '));
    console.log(`第${i+1}行是否为命令:`, isCommand);
    
    // 如果当前行以命令开头，且不是第一行，则用 && 连接
    if (isCommand && result.length > 0) {
      console.log('添加独立命令，用 && 连接');
      result += ' && ' + currentLine;
    } else {
      // 如果是第一行或者不是命令开头，则直接添加
      if (result.length > 0) {
        console.log('当前 result:', `"${result}"`);
        console.log('检查是否以 ^ 结尾:', result.endsWith(' ^'));
        
        // 检查上一行是否以 ^ 结尾
        if (result.endsWith(' ^')) {
          // 上一行已有 ^，直接换行连接
          console.log('上一行有 ^，添加换行连接');
          result += '\\n' + currentLine;
        } else {
          // 上一行没有 ^，添加续行符
          console.log('上一行没有 ^，添加续行符');
          result += ' ^\\n ' + currentLine;
        }
      } else {
        console.log('第一行，直接设置');
        result = currentLine;
      }
    }
    
    console.log(`第${i+1}行处理后 result:`, `"${result}"`);
    i++;
  }
  
  console.log('处理完成，转义前:', `"${result}"`);
  
  // 转义反斜杠用于 JSON 字符串
  result = result.replace(/\\/g, '\\\\');
  console.log('转义后最终结果:', `"${result}"`);
  return result;
};

const handleSave = () => {
  if (form.stages.length === 0) {
    ElMessage.error('至少需要一个 Stage')
    return
  }
  for (const s of form.stages) {
    if (!s.name) {
      ElMessage.error('Stage 名称不能为空')
      return
    }
  }

  const output = {
    project_name: form.project_name,
    git_url: form.git_url,
    agent: form.agent,
    branch: form.branch,
    pipeline_config: {
      agent: form.agent,
      git_url: form.git_url,
      branch: form.branch,
      environment: {
        PROJECT_NUMBER: form.environment?.PROJECT_NUMBER || '',
        PROJECT_TYPE: form.environment?.PROJECT_TYPE || ''
      },
      parameters: form.parameters.map((p) => ({
        type: 'string',
        name: p.name,
        default_value: p.default_value,
        description: p.description
      })),
      stages: form.stages.map((s) => ({
        name: s.name,
        commands: s.commands.map((c) => ({ type: 'bat', content: formatBatScript(convertPlaceholders(c)) }))
      }))
    }
  }

  emit('save', output)
}
</script>

<style scoped>
.jenkins-config-form {
  padding-right: 10px;
}
.stage-block {
  margin-bottom: 16px;
}
.command-block {
  margin-bottom: 8px;
}
</style> 