<template>
  <div v-if="isDrawerMode" class="git-config-form">
    <div class="form-item">
      <label>Git URL:</label>
      <el-input 
        v-model="gitUrl" 
        placeholder="填入项目git地址"
        size="default"
      />
    </div>


    <div class="form-item">
      <label>分支:</label>
      <el-input 
        v-model="branch" 
        placeholder="master"
        size="default"
      />
    </div>
    <div class="form-item">
      <div class="form-item form-item-inline">
      <label>启用Webhook配置</label>
      <div class="item-content">
        <el-switch
          v-model="enable_webhook"
          active-text="开启"
          inactive-text="关闭"
          active-color="#13ce66"
          inactive-color="#ff4949"
          size="small"
        />
      </div>
</div>
    </div>

    <div class="form-item">
      <label>Git 凭据 ID:</label>
      <el-input 
        v-model="credentialsId" 
        placeholder="gitlab_token"
        size="default"
      />
      <div style="color: #909399; font-size: 12px; margin-top: 4px;">
        默认使用 gitlab_token 凭据，留空则不使用凭据
      </div>
    </div>
    <div class="form-actions">
      <el-button type="primary" @click="handleSave">保存配置</el-button>
      <el-button @click="$emit('cancel')">取消</el-button>
    </div>
  </div>
  <div v-else class="git-node" :class="nodeClasses">
    <!-- 执行顺序徽章 -->
    <div v-if="data.executionOrder" class="order-badge">{{ data.executionOrder }}</div>
    
    <div class="node-header">
      <div class="node-title">
        <svg class="node-icon" viewBox="0 0 24 24" width="20" height="20">
          <path fill="#f05032" d="M23.546 10.93L13.067.452c-.604-.603-1.582-.603-2.188 0L8.708 2.627l2.76 2.76c.645-.215 1.379-.07 1.889.441.516.515.658 1.258.438 1.9l2.658 2.66c.645-.223 1.387-.078 1.9.435.721.72.721 1.884 0 2.604-.719.719-1.881.719-2.6 0-.539-.541-.674-1.337-.404-1.996L12.86 8.955v6.525c.176.086.342.203.488.348.713.721.713 1.883 0 2.6-.719.721-1.889.721-2.609 0-.719-.719-.719-1.881 0-2.598.182-.18.387-.316.605-.406V8.835c-.217-.091-.424-.222-.6-.401-.545-.545-.676-1.342-.396-2.009L7.636 3.7.45 10.881c-.6.605-.6 1.584 0 2.189l10.48 10.477c.604.604 1.582.604 2.186 0L23.546 13.12c.603-.603.603-1.582 0-2.190"/>
        </svg>
        <span>Git 配置</span>
      </div>
      <div class="node-actions">
        <span v-if="data.configured" class="configured-indicator">✔️</span>
        <span v-else class="configured-indicator pending">⚙️</span>
      </div>
      
      <!-- 状态指示器 -->
      <div v-if="data.executionStatus" class="status-indicator" :class="data.executionStatus"></div>
    </div>
    <div class="node-content">
      {{ data.label || '未配置' }}
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElInput, ElButton } from 'element-plus'

const props = defineProps({
  data: {
    type: Object,
    required: true
  },
  isDrawerMode: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['save', 'cancel'])

// 表单数据
const gitUrl = ref(props.data.gitUrl || props.data.git_url || '')
const branch = ref(props.data.branch || 'master')
const credentialsId = ref(props.data.credentialsId || props.data.credentials_id || 'gitlab_token')
const enable_webhook = ref(props.data.enable_webhook !== undefined ? props.data.enable_webhook : true)

// 计算节点样式类
const nodeClasses = computed(() => ({
  'configured': props.data.configured,
  'executing': props.data.executionStatus === 'running',
  'success': props.data.executionStatus === 'success',
  'failed': props.data.executionStatus === 'failed'
}))


// 处理保存
const handleSave = () => {
  if (!gitUrl.value.trim()) {
    alert('请填写Git URL')
    return
  }
  
  if (!branch.value.trim()) {
    alert('请填写分支')
    return
  }
  
  const config = {
    gitUrl: gitUrl.value,
    git_url: gitUrl.value,
    branch: branch.value,
    credentialsId: credentialsId.value.trim() || '',
    credentials_id: credentialsId.value.trim() || '',
    configured: true,
    label: `Git 配置 (已配置)`,
    enable_webhook: enable_webhook.value
  }
  emit('save', config)
}

// 监听props变化更新本地状态
watch(() => props.data, (newData) => {
  if (newData) {
    gitUrl.value = newData.gitUrl || newData.git_url || ''
    branch.value = newData.branch || 'master'
    credentialsId.value = newData.credentialsId || newData.credentials_id || 'gitlab_token'
    enable_webhook.value = newData.enable_webhook !== undefined ? newData.enable_webhook : true
  }
}, { deep: true, immediate: true })


</script>

<style scoped>
.git-config-form {
  padding: 20px;
}
.webhook-help {
  margin-top: 20px;
  color: #374151;
}

.webhook-row {
  display: flex;
  align-items: center;   /* 垂直居中对齐 */
  gap: 10px;             /* 两个元素之间留点空隙 */
}
.form-item {
  margin-bottom: 20px;
}

.form-item label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #374151;
}

.form-actions {
  margin-top: 30px;
  display: flex;
  gap: 10px;
  width: 100%;                /* ⬅️ 先让容器拉满一行 */
}

.git-node {
  min-width: 180px;
  background: white;
  border: 1px solid #cbd5e1;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.05);
  transition: border-color 0.2s ease-in-out;
  overflow: visible;
  position: relative;
  padding: 0;
}

/* 执行顺序徽章样式 */
.order-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #3b82f6;
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 11px;
  font-weight: bold;
  z-index: 10;
  border: 2px solid white;
  box-shadow: 0 1px 3px rgba(0,0,0,0.2);
}

.git-node.configured {
  border-color: #16a34a;
}

.git-node.configured.selected {
  border-color: #16a34a;
  box-shadow: 0 0 0 3px rgba(22, 163, 74, 0.2);
}

.git-node.executing {
  border-color: #f59e0b;
  animation: pulse 2s infinite;
}

.git-node.success {
  border-color: #10b981;
  background-color: #f0fdf4;
}

.git-node.failed {
  border-color: #ef4444;
  background-color: #fef2f2;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.02); }
}

.node-header {
  padding: 8px 12px;
  border-bottom: 1px solid #e2e8f0;
  font-weight: 600;
  font-size: 13px;
  text-align: left;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f8fafc;
}

.node-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #374151;
}

.node-icon {
  flex-shrink: 0;
}

.node-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.config-button {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.config-button:hover {
  background-color: #f3f4f6;
  color: #374151;
}

.node-content {
  padding: 12px;
  font-size: 12px;
  color: #475569;
  text-align: left;
  min-height: 30px;
}

.configured-indicator {
  font-size: 14px;
}

.configured-indicator.pending {
  color: #f59e0b;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-left: 8px;
}

.status-indicator.pending {
  background: #9ca3af;
}

.status-indicator.running {
  background: #f59e0b;
  animation: blink 1s infinite;
}

.status-indicator.success {
  background: #10b981;
}

.status-indicator.failed {
  background: #ef4444;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.3; }
}


.form-item-inline {
  display: grid;
  grid-template-columns: 75px 1fr; 
  align-items: center;             
  column-gap: 12px;
}


.form-item-inline > label {
  margin-bottom: 0;     
  white-space: nowrap;   
}


.form-item-inline .item-content {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  margin-left: 80px;
}
</style>