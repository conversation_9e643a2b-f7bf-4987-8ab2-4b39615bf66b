<template>
  <div class="dynamic-form">
    <!-- 版本号显示区域 -->

    <!-- 1. 用户自定义配置（适配新数据结构） -->
    <template  v-if="config?.userDefined && config.userDefined.length > 0"
    v-for="(item, idx) in (config?.userDefined || [])" :key="idx">
      <div class="form-item" :class="{ 'first-item': idx === 0 }">
        <label class="form-label user-defined-label">
          {{ item.name }}
          <el-tooltip
            :content="Array.isArray(item.desc) ? item.desc.join('\n') : (item.desc || '暂无描述')"
            placement="top"
            effect="dark"
          >
            <span class="help-icon">?</span>
          </el-tooltip>
        </label>
        <el-select
          v-model="formData[item.name]"
          placeholder="请选择"
          class="form-select input-control"
          @change="(val) => handleChange(item.name, val, item)"
        >
          <el-option
            v-for="enumValue in item.enum"
            :key="enumValue"
            :label="enumValue"
            :value="enumValue"
          />
        </el-select>
      </div>

      <!-- 用户自定义项分割线 -->
      <div
        v-if="idx < (config?.userDefined?.length || 0) - 1 && idx > 0"
        class="user-defined-divider"
      ></div>
    </template>

    <!-- 用户自定义区域和变量分组之间的分割线 -->
    <div
      v-if="config?.userDefined && config.userDefined.length > 0"
      class="section-divider"
    ></div>

    <!-- 2. 变量分组（模拟左侧菜单层级，优化箭头显示） -->
    <div class="variables-container">
      <template v-for="(group, groupIdx) in (config?.variables || [])" :key="groupIdx">
        <!-- 分组标题（优化箭头样式，更明显） -->
        <div
            class="group-title"
            @click="toggleGroup(groupIdx)"
          >
           <!-- 优化后的箭头图标，使用Element Plus图标并确保类名正确 -->
            <el-icon
              :class="[
                'group-arrow',
                { 'is-open': activeGroup.includes(groupIdx) }
              ]"
            >
              <ArrowRight />
            </el-icon>
            <span class="group-title-text">{{ group.group }}</span>
          </div>

        <!-- 分组内容（展开时显示，模拟子项缩进） -->
        <div
          class="group-content"
          v-show="activeGroup.includes(groupIdx)"
          :style="{ marginLeft: '24px' }"
        >
          <!-- 遍历分组中的字段 -->
          <template v-for="(field, fieldKey) in group.list" :key="fieldKey">
            <!-- 主字段 -->
            <div class="form-item">
              <label class="form-label">
                {{ field.name }}{{ field.unit ? `(${field.unit})` : '' }}
                <el-tooltip
                  :content="Array.isArray(field.desc) ? field.desc.join('\n') : (field.desc || '暂无描述')"
                  placement="top"
                  effect="dark"
                >
                  <span class="help-icon">?</span>
                </el-tooltip>
              </label>

              <div class="input-container">
                <!-- enum 类型字段 -->
                <template v-if="field.type === 'enum'">
                  <el-select
                    v-model="formData[fieldKey]"
                    placeholder="请选择"
                    class="form-select input-control"
                    @change="(val) => handleEnumFieldChange(fieldKey, val, field)"
                  >
                    <el-option
                      v-for="enumValue in field.enum"
                      :key="enumValue"
                      :label="enumValue"
                      :value="enumValue"
                    />
                  </el-select>
                </template>
                <!-- uint 类型字段 -->
                <template v-else-if="field.type.includes('uint')">
                    <!-- 当类型为 uint_hex 时显示十六进制悬浮框 -->
                    <template v-if="field.type === 'uint8_hex'">
                      <el-tooltip
                        :content="`当前值: 0x${typeof formData[fieldKey] === 'string' ? formData[fieldKey].toUpperCase() : (formData[fieldKey] || '00').toString().toUpperCase()}`"
                        placement="top"
                        effect="dark"
                      >
                        <el-input
                          v-model="formData[fieldKey]"
                          :key="`hex-input-${fieldKey}`"
                          class="form-input input-control"
                          @input="handleHexInput(fieldKey, $event)"
                          @blur="handleHexBlur(fieldKey)"
                          @change="(val) => handleChange(fieldKey, val, field)"
                          placeholder="请输入十六进制值（如 1A、FF）"
                          clearable
                        >
                          <template #prefix>0x</template>
                          <template #suffix>
                            <span class="text-gray-400 ml-1"
                             v-if="field.min !== undefined && field.min !== null && field.max !== undefined && field.max !== null"
                            >
                              (范围: {{ field.min }}-{{ field.max }})
                            </span>
                          </template>
                        </el-input>
                      </el-tooltip>
                    </template>
                    <template v-else-if="field.type === 'uint8_list_or'">
                      <el-checkbox-group v-model="formData[fieldKey]" @change="(val) => handleChange(fieldKey, val, field)">
                        <el-checkbox
                          v-for="(option, index) in parsedDefaultOptions(field)"
                          :key="index"
                          :value="option.value"
                          :disabled="!props.hasEditPermission"
                          
                        >
                          {{ option.name }}（{{ option.value }}）
                        </el-checkbox>
                      </el-checkbox-group>
                    </template>
                    <!-- 其他 uint 类型保持原有样式 -->
                    <template v-else>
                      <el-input-number
                        v-model="formData[fieldKey]"
                        :min="parseInt(field.min)"
                        :max="parseInt(field.max)"
                        class="form-input input-control"
                        @change="(val) => handleChange(fieldKey, val,  field)"
                      />
                    </template>
                  </template>
                <template v-else-if="field.type === 'text'">
                  <el-input
                      v-model="formData[fieldKey]"
                      :key="`hex-input-${fieldKey}`"
                      class="form-input input-control"
                      @change="(val) => handleChange(fieldKey, val, field)"
                      placeholder="请输入文本内容"
                      clearable
                    ></el-input>
                </template>
               <template v-else-if="field.type === 'text_disable'">
                  <el-input
                      v-model="formData[fieldKey]"
                      :key="`hex-input-${fieldKey}`"
                      class="form-input input-control"
                      @change="(val) => handleChange(fieldKey, val, field)"
                      placeholder="请输入文本内容"
                      clearable
                      :disabled=true
                    ></el-input>
                </template>
                <!-- PWM 或 IIC 类型字段 -->
                <template v-else-if="field.type === 'PWM' || field.type === 'IIC' || field.type === 'EXIT' || field.type === 'ADC'">
                  <el-select
                    v-model="formData[fieldKey]"
                    placeholder="请选择对应通道"
                    class="form-select input-control"
                    @change="(val) => handleChange(fieldKey, val, field)"
                  >
                    <el-option
                      v-for="(opt, optIdx) in field.optional_keys"
                      :key="optIdx"
                      :label="opt"
                      :value="opt"
                    />
                  </el-select>
                </template>
              </div>
            </div>

            <!-- 嵌套的子字段（当主字段是 enum 类型且有 list 时） -->
            <template v-if="field.type === 'enum' && field.list && formData[fieldKey]">
              <div
                v-for="subField in getSubFields(field, formData[fieldKey])"
                :key="subField.key"
                class="form-item sub-field"
                :style="{ marginLeft: '24px' }"
              >
                <label class="form-label">
                  {{ subField.field.name }}{{ subField.field.unit ? `(${subField.field.unit})` : '' }}
                  <el-tooltip
                    :content="Array.isArray(subField.field.desc) ? subField.field.desc.join('\n') : (subField.field.desc || '暂无描述')"
                    placement="top"
                    effect="dark"
                  >
                    <span class="help-icon">?</span>
                  </el-tooltip>
                </label>

                <div class="input-container">
                  <!-- uint8 类型子字段 -->
                  <template v-if="subField.field.type.includes('uint')">
                    <el-input-number
                      v-model="formData[subField.key]"
                      :min="parseInt(subField.field.min)"
                      :max="parseInt(subField.field.max)"
                      class="form-input input-control"
                      @change="(val) => handleChange(subField.key, val, subField.field)"
                    />
                  </template>
                  <!-- PWM 或 IIC 类型子字段 -->
                  <template v-else-if="subField.field.type === 'PWM' || subField.field.type === 'IIC' || subField.field.type === 'EXIT' || subField.field.type === 'ADC'">

                    <el-select
                      v-model="formData[subField.key]"
                      placeholder="请选择对应通道"
                      class="form-select input-control"
                      @change="(val) => handleChange(subField.key, val, subField.field)"
                    >
                      <el-option
                        v-for="(opt, optIdx) in subField.field.optional_keys"
                        :key="optIdx"
                        :label="opt"
                        :value="opt"
                      />
                    </el-select>
                  </template>
                </div>
              </div>
            </template>
          </template>
        </div>

        <!-- 分组分割线 -->
        <div
          v-if="groupIdx < (config?.variables?.length || 0) - 1"
          class="group-divider"
        ></div>
      </template>
    </div>

    <!-- 🎯 配置修改加载对话框 -->
    <el-dialog
      v-model="isConfigChangeRequesting"
      title=""
      width="480px"
      :close-on-click-modal="false"
      :show-close="false"
      :draggable="false"
      align-center
      class="config-request-dialog"
    >
      <div class="request-dialog-content">
        <!-- 图标和标题 -->
        <div class="request-dialog-header">
          <div class="request-icon">
            <el-icon class="rotating-icon">
              <Loading />
            </el-icon>
          </div>
          <h3 class="request-title">配置修改中</h3>
        </div>

        <!-- 内容描述 -->
        <div class="request-dialog-body">
          <div class="request-info">
            <div class="info-item">
              <span class="info-label">修改字段：</span>
              <span class="info-value">{{ currentRequestInfo?.fieldLabel }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">修改值：</span>
              <span class="info-value">{{ currentRequestInfo?.fieldValue }}</span>
            </div>
          </div>
          <div class="request-message">
            请等待当前修改完成后，再进行其他配置更改
          </div>
        </div>

        <!-- 进度指示 -->
        <div class="request-progress">
          <el-progress
            :percentage="100"
            :indeterminate="true"
            :duration="3"
            :stroke-color="'#409eff'"
            :show-text="false"
          />
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, watch, toRaw, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { Loading, ArrowRight } from '@element-plus/icons-vue';
import http from '@/utils/http/http';


// 定义props接收config、workspace_path和branch_status
const props = defineProps({
  config: {
    type: Object,
    required: true
  },
  workspacePath: {
    type: String,
    required: true
  },
  branchStatus: {
    type: String,
    required: true
  },
  project_code:{
    type: String,
    required: true
  },
  project_name: {
    type: String,
    required: true
  },
  project_gitlab:{
    type: String,
    required: true
  },
  project_branch: {
    type: String,
    required: true
  },
  node_level: {
    type: String,
    required: true
  },
  nodeName: {
      type: String,
      required: true
  },
  previousConfig: {
    type: Object,
    default: () => ({})
  },
  hasEditPermission: {
    type: Boolean,
    default: true
  }
});

// 用于接收子组件配置变更事件
const emit = defineEmits(['configChanged']);

// 表单数据初始化
const formData = reactive({});
// 保存上一次的表单数据
const previousFormData = reactive({});
// 标记配置是否已变更
const isConfigChanged = ref(false);


// 存储原始的default数据
const originalDefaults = reactive({});

console.log('workspacePath:', props.workspacePath);
console.log('branchStatus:', props.branchStatus);

let currentMessage = null; // 用于存储当前消息实例

// 🎯 配置修改请求状态管理
const isConfigChangeRequesting = ref(false);
const currentRequestInfo = ref(null);

// 深度克隆对象（处理数组和对象）
const deepClone = (obj) => {
  // 基础类型直接返回
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }
  
  // 处理日期、正则等特殊对象（如果有）
  if (obj instanceof Date) {
    return new Date(obj.getTime());
  }
  
  // 处理数组
  if (Array.isArray(obj)) {
    // 特殊处理 uint8_list_or 类型的数组
    if (obj.length > 0 && obj[0].value !== undefined) {
      return obj.map(item => {
        const newItem = {...item};
        // 确保 value 是十六进制字符串
        if (typeof newItem.value === 'number') {
          newItem.value = `0x${newItem.value.toString(16).toUpperCase().padStart(2, '0')}`;
        } else if (typeof newItem.value === 'string') {
          newItem.value = `0x${newItem.value.replace(/^0x/i, '').toUpperCase()}`;
        }
        return newItem;
      });
    }
    
    // 普通数组克隆
    return obj.map(item => deepClone(item));
  }
  // 处理普通对象
  const clone = {};
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      // 特殊处理 uint8_hex 类型的字段
      if (key === 'default' && obj.type === 'uint8_hex' && typeof obj[key] === 'string') {
        clone[key] = obj[key].replace(/^0x/i, ''); // 保持无0x前缀的格式
      } else {
        clone[key] = deepClone(obj[key]);
      }
    }
  }
  
  return clone;
};

// 初始化 userDefined 默认值（适配新数据结构）
if (props.config.userDefined) {
  props.config.userDefined.forEach(item => {
    formData[item.name] = item.default_value || (item.enum && item.enum[0]) || '';
    previousFormData[item.name] = formData[item.name];
  });
}

// 初始化 variables 默认值（适配新数据结构）
if (props.config.variables) {
  props.config.variables.forEach(group => {
    Object.entries(group.list).forEach(([fieldKey, field]) => {

      let defaultValue = field.default || (field.enum && field.enum[0]) || '';
      
      // 对 uint8_hex 类型，保持字符串格式（去掉0x前缀）
      if (field.type === 'uint8_hex' && typeof defaultValue === 'string') {
        defaultValue = defaultValue.replace(/^0x/i, ''); // 去掉0x前缀，如"0xFF"→"FF"
      } else if (typeof defaultValue === 'string' && !isNaN(Number(defaultValue)) && (field.type.includes('uint') || field.type === 'number')) {
        defaultValue = Number(defaultValue); // 其他类型仍可转为数字
      }


     
      // 处理uint8_list_or类型
        if (field.type === 'uint8_list_or') {
          try {
            console.log('初始化uint8_list_or的defaultValue:', defaultValue, '类型:', typeof defaultValue);
            
            // 如果defaultValue已经是数组格式，直接使用
            if (Array.isArray(defaultValue)) {
              defaultValue = defaultValue.filter(option => option.enable).map(option => option.value);
            } 
            // 如果是字符串，尝试解析JSON
            else if (typeof defaultValue === 'string') {
             
              // 预处理：替换单引号为双引号，去除多余空格和换行
                const processedStr = field.default
                  .replace(/'/g, '"') // 单引号转双引号
                  .replace(/\s+/g, ' ') // 多余空格/换行转为单个空格
                  .trim(); // 去除首尾空格
                
                return JSON.parse(processedStr);
            } 
            // 其他情况，设置为空数组
            else {
              console.warn('初始化uint8_list_or：defaultValue不是字符串也不是数组，使用空数组', defaultValue);
              defaultValue = [];
            }
          } catch (e) {
           console.error('解析uint8_list_or的default失败：', e.message, '，原始字符串：', field.default,'，字段信息：', field);
            defaultValue = [];
          }
        }
      // 存储原始的default数据
      originalDefaults[fieldKey] = deepClone(field.default);
      // 设置默认值

      formData[fieldKey] = defaultValue;
      previousFormData[fieldKey] = formData[fieldKey];

      if (field.type === 'enum' && field.list && field.default) {
        console.log('field:', field);
        const subFields = field.list[field.default];
        console.log('subFields:', subFields);
        if (subFields && Array.isArray(subFields)) {
          subFields.forEach(subFieldObj => {
            Object.entries(subFieldObj).forEach(([subKey, subField]) => {
              let subDefaultValue = subField.default || '';
              
              if (subDefaultValue !== undefined && subDefaultValue !== null) {
                // 处理 ['PWM', 'IIC', 'EXIT'] 类型
                if (['PWM', 'IIC', 'EXIT', 'ADC'].includes(subField.type)) {
                  // 为空字符串时保持空字符串，不转换为数字
                  formData[subKey] = subDefaultValue === '' ? '' : subDefaultValue;
                }
                // 处理 number 类型，保持原转换逻辑
                else if ((subField.type === 'number'|| subField.type.includes('uint')) && typeof subDefaultValue === 'string' && !isNaN(Number(subDefaultValue))) {
                  formData[subKey] = Number(subDefaultValue);
                }
                // 其他类型保持原值
                else {
                  formData[subKey] = subDefaultValue;
		  previousFormData[subKey] = formData[subKey];
                }
              } else {
                // 未定义默认值时，根据类型设置初始值
                // formData[subKey] = ['PWM', 'IIC', 'EXIT'].includes(subField.type) ? '' : 0;
                formData[subKey] = '';
              }
              
              previousFormData[subKey] = formData[subKey];
            });
          });
        } 
      }

      if (['PWM', 'IIC', 'EXIT', 'ADC'].includes(field.type)) {
          console.log('field:', field);
          const parFields =  field.default;
          console.log('parFields:', parFields);
          if (parFields && Array.isArray(parFields)) {
            formData[fieldKey] = parFields;
          } else {
            formData[fieldKey] = defaultValue || '';
          }
      }
     
    

    });
  });





}


// 监听formData变化，更新previousFormData
watch(
  () => toRaw(formData),
  (newVal) => {
    if (isConfigChanged.value) return; // 正在处理变更时不更新
    // 深度克隆并正确处理数字数组
    Object.keys(newVal).forEach(key => {
      previousFormData[key] = deepClone(newVal[key]);
    });
  },
  { deep: true, immediate: true }
);

// 控制分组展开收起 - 默认展开所有分组
const activeGroup = ref(props.config.variables.map((_, index) => index)); 

function toggleGroup(groupIdx) {
  const index = activeGroup.value.indexOf(groupIdx);
  if (index > -1) {
    activeGroup.value.splice(index, 1); // 收起分组
  } else {
    activeGroup.value.push(groupIdx); // 展开分组
  }
}

// ========== 新数据结构处理逻辑 ==========

// 处理 enum 字段变更，同时更新相关的子字段
function handleEnumFieldChange(fieldKey, newValue, field) {
  // 更新主字段值
  formData[fieldKey] = newValue;

  // 如果有嵌套的 list，需要初始化对应的子字段
  if (field.list && field.list[newValue]) {
    const subFields = field.list[newValue];
    if (Array.isArray(subFields)) {
      subFields.forEach(subFieldObj => {
        Object.entries(subFieldObj).forEach(([subKey, subField]) => {
          // 初始化子字段的默认值
          formData[subKey] = subField.default || '';
          previousFormData[subKey] = formData[subKey];
        });
      });
    }
  }

  // 调用通用的变更处理
  handleChange(fieldKey, newValue);
}

// 获取当前选中值对应的子字段
function getSubFields(field, selectedValue) {
  if (!field.list || !field.list[selectedValue]) {
    return [];
  }

  const subFields = field.list[selectedValue];
  if (!Array.isArray(subFields)) {
    return [];
  }

  const result = [];
  subFields.forEach(subFieldObj => {
    Object.entries(subFieldObj).forEach(([key, subField]) => {
      result.push({
        key,
        field: subField
      });
    });
  });

  return result;
}





// 解析uint8_list_or的default（字符串转数组）
function parsedDefaultOptions(field) {
  if (field.type !== 'uint8_list_or') return [];
  try {
    console.log('解析uint8_list_or的field.default:', field.default, '类型:', typeof field.default);
    
    // 如果field.default已经是数组格式，直接返回
    if (Array.isArray(field.default)) {
      // console.log('field.default:', field.default)
      // for (let i = 0; i < field.default.length; i++)
      //   console.log(field.default[i].value)
      return field.default;
    } 
    // 如果是字符串，尝试解析JSON
    else if (typeof field.default === 'string') {
      return JSON.parse(field.default.replace(/\r\n/g, ''));
    } 
    // 其他情况，返回空数组
    else {
      console.warn('解析uint8_list_or：field.default不是字符串也不是数组，使用空数组', field.default);
      return [];
    }
  } catch (e) {
    console.error('解析uint8_list_or的default失败：', e);
    return [];
  }
}

// 处理十六进制输入（实时过滤无效字符）
const handleHexInput = (fieldKey, value) => {
  console.log('Before hex input:', formData[fieldKey]);
  // 仅允许输入十六进制字符（0-9、A-F、a-f），自动转为大写
  const hexValue = value.replace(/[^0-9A-Fa-f]/g, '').toUpperCase();
  const inputValue = formData[fieldKey] || '0';
  if (!inputValue) return; // 提前返回避免后续处理
  console.log('After hex input:', hexValue);
};

// 失焦时校验十六进制范围
const handleHexBlur = (fieldKey) => {
  console.log("fieldKey:", formData[fieldKey])
  const inputValue = formData[fieldKey] || '0';
  // 查找对应的 field 对象
  let targetField = null;
  if (props.config.variables) {
    for (const group of props.config.variables) {
      if (group.list[fieldKey]) {
        targetField = group.list[fieldKey];
        break;
      }
    }
  }
  
  if (!targetField) {
    console.error(`未找到字段 ${fieldKey} 的配置信息`);
    return;
  }
  
  const minHex = targetField.min; // 假设targetField.min是十六进制字符串（如 "00"）
  const maxHex = targetField.max; // 假设targetField.max是十六进制字符串（如 "FF"）
  
  
  // 转换为十进制用于比较范围
  const inputDec = parseInt(inputValue, 16);
  const minDec = parseInt(minHex, 16);
  const maxDec = parseInt(maxHex, 16);
  
  // 范围校验与修正
  if (isNaN(inputDec) || inputDec < minDec) {
    formData[fieldKey] = minHex.replace(/^0x/i, ''); // 小于最小值时强制设为最小值
    ElMessage.warning(`输入值超出范围，最小值为 0x${formData[fieldKey]}`);
  } else if (inputDec > maxDec) {
    formData[fieldKey] = maxHex.replace(/^0x/i, '');// 大于最大值时强制设为最大值
    ElMessage.warning(`输入值超出范围，最大值为 0x${formData[fieldKey]}`);
  }
};


const handleChange = (label_name, newVal, field) => {
  // 处理非数组类型的值
  let valueToSave = newVal;
  // if (field && (field.type.includes('uint') || field.type === 'number') && !Array.isArray(newVal)) {
  if (!Array.isArray(newVal)) {
    valueToSave = typeof newVal === 'string' && !isNaN(Number(newVal))
      ? Number(newVal)
      : newVal;
  }

  console.log('数据变更:', label_name, valueToSave, '权限状态:', props.hasEditPermission);

  // 检查编辑权限
  if (!props.hasEditPermission) {
    // 没有编辑权限时，自动回退到上一次的数据
    console.log('无编辑权限，自动回退数据:', label_name, previousFormData[label_name]);
    // 先关闭可能存在的旧消息
    if (currentMessage) {
      currentMessage.close();
    }

    // 显示权限提示消息
    currentMessage = ElMessage.warning('当前无编辑权限，数据已自动回退');

    // 延迟回退数据，确保用户能看到变化过程
    setTimeout(() => {
      formData[label_name] = previousFormData[label_name];
      isConfigChanged.value = false;
    }, 500);

    return; // 直接返回，不执行后续的API调用
  }

  // 有编辑权限时，正常更新数据并调用API
  formData[label_name] = valueToSave; // 更新表单数据
  isConfigChanged.value = true; // 标记配置已变更

  // 更新default中的enable状态
  if (field && field.type === 'uint8_list_or') {
    const defaultOptions = parsedDefaultOptions(field);
    defaultOptions.forEach(option => {
      option.enable = valueToSave.includes(option.value);
    });
    field.default = defaultOptions;
  }

  let config_value = valueToSave;

  // 对于 uint8_list_or 类型，转换为所需的数组格式
  if (field && field.type === 'uint8_list_or') {
    const defaultOptions = parsedDefaultOptions(field);
    config_value = defaultOptions.map(option => ({
      name: option.name,
      enable: valueToSave.includes(option.value),
      value: option.value
    }));
  } else if (Array.isArray(valueToSave)) {
    config_value = valueToSave.map(String).join(',');
  }
 
  console.log('field:', field)
 if (field && field.type === 'uint8_hex') {
    
    config_value = `0x${String(config_value).replace(/^0x/i, '')}`;
  }

  const config_path = props.nodeName + '/' + label_name;




  // 先关闭可能存在的旧消息
  if (currentMessage) {
    currentMessage.close();
  }

  // 🎯 显示加载对话框
  isConfigChangeRequesting.value = false;
  currentRequestInfo.value = {
    fieldLabel: label_name,
    fieldValue: Array.isArray(valueToSave) ? valueToSave.join(', ') : valueToSave
  };
  console.log("value:", config_value)
  http.post('/code_management/config_params', {
    params: {
      path: config_path,
      value: config_value,
      workspace_path: props.workspacePath,
      branch_status: props.branchStatus,
      label:label_name,
      project_code: props.project_code,
      project_name: props.project_name,
      project_gitlab: props.project_gitlab,
      project_branch: props.project_branch,
      node_level: props.node_level,
      nodeName: props.nodeName

    }
  }).then(response => {
    console.log("配置更改详情：", response.data);

    // 🎯 关闭加载对话框
    isConfigChangeRequesting.value = false;
    currentRequestInfo.value = null;

    if (response.data.config_status === 1) {
      // 关闭之前的消息并显示新消息
      if (currentMessage) {
        currentMessage.close();
      }
      currentMessage = ElMessage.success('配置更新成功');
      // 成功后更新上一次配置
      previousFormData[label_name] = valueToSave;
      isConfigChanged.value = false;
      // 通知父组件配置已变更
      emit('configChanged', formData);
    } else {
      // 关闭之前的消息并显示新消息
    if (currentMessage) {
      currentMessage.close();
    }
      currentMessage = ElMessage.error('配置更新失败，正在恢复上一次值');
      // 失败后恢复配置
      setTimeout(() => {
        formData[label_name] = previousFormData[label_name];

        if (field && field.type === 'uint8_list_or') {
          field.default = deepClone(originalDefaults[label_name]);
        }

        isConfigChanged.value = false;
        if (currentMessage) {
          currentMessage.close();
        }
        currentMessage = ElMessage.warning('已恢复到上一次配置');
      }, 1000);
    }
  }).catch(error => {
    // 🎯 关闭加载对话框
    isConfigChangeRequesting.value = false;
    currentRequestInfo.value = null;

    // 处理请求错误
    if (currentMessage) {
      currentMessage.close();
    }
    currentMessage = ElMessage.error('请求失败，请稍后再试');
    console.error('请求错误:', error);
    // 网络错误时恢复配置
    setTimeout(() => {
      formData[label_name] = previousFormData[label_name];
       if (field && field.type === 'uint8_list_or') {
        field.default = deepClone(originalDefaults[label_name]);
      }
      isConfigChanged.value = false;
      // 关闭之前的消息并显示新消息
    if (currentMessage) {
      currentMessage.close();
    }
      currentMessage = ElMessage.warning('已恢复到上一次配置');
    }, 1000);
  });
}


</script>

<style scoped>
/* 整体容器 */
.dynamic-form {
  background-color: #fff;
  color: #666;
}

/* 背光IC选择项样式（优化布局） */
.form-item {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  transition: all 0.3s ease;
  position: relative;
}

.form-item:hover {
  border-color: #c6e2ff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.08);
}


.form-label {
  flex: 0 0 220px;
  text-align: left;
  margin-right: 20px;
  color: #606266;
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
  display: flex;
  align-items: center;
}

/* 用户自定义项标签特殊样式 */
.user-defined-label {
  color: #666;
  font-weight: 600;
  margin-left: 6px;
}

/* 帮助图标样式 */
.help-icon {
  display: inline-block;
  margin-left: 6px;
  width: 16px;
  height: 16px;
  line-height: 16px;
  text-align: center;
  background-color: #e1f3ff;
  color: #409eff;
  border: 1px solid #b3d8ff;
  border-radius: 50%;
  font-size: 12px;
  font-weight: bold;
  cursor: help;
  transition: all 0.3s ease;
}

.help-icon:hover {
  background-color: #409eff;
  color: #fff;
  border-color: #409eff;
  transform: scale(1.1);
  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.3);
}

/* 统一表单控件布局 */
.input-container {
  flex: 1; /* 占据剩余空间 */
  position: relative;
}

.input-control {
  width: 100%; /* 宽度占满容器 */
  font-size: 14px;
  box-sizing: border-box; /* 确保内边距和边框不影响宽度 */
}


.el-input-number {
  width:100%;
  font-size: 14px;
  margin-left: 0; /* 移除多余的左边距 */
}

/* 变量分组区域（核心：优化箭头样式） */
.variables-container {
  margin-top: 20px; /* 和上方模块拉开距离 */
}


.group-title {
  font-size: 15px;
  color: #303133;
  font-weight: 600;
  cursor: pointer;
  padding: 16px 12px;
  margin: 8px 0;
  display: flex;
  align-items: center;
  border-radius: 6px;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border: 1px solid #e4e7ed;
  transition: all 0.3s ease;
}

.group-title:hover {
  color: #409eff;
  background: linear-gradient(135deg, #ecf5ff 0%, #f0f9ff 100%);
  border-color: #b3d8ff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
  transform: translateY(-1px);
}

.group-title-text {
  font-size: 15px;
  font-weight: 600;
  margin-left: 8px;
}


.group-content {
  margin-left: 24px;
  padding: 16px 0 16px 16px;
  border-left: 2px solid #e4e7ed;
  background: linear-gradient(135deg, #fafbfc 0%, #ffffff 100%);
  border-radius: 0 8px 8px 0;
  position: relative;
}

.group-content::before {
  content: '';
  position: absolute;
  left: -2px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(180deg, #409eff, #67c23a);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.group-content:hover::before {
  opacity: 1;
}

/* 统一分割线样式 */
.group-divider,
.user-defined-divider,
.section-divider {
  height: 0.5px;
  background-color: #ccc;
  margin: 20px 0px;
  width: 100%;
  border: none;
  flex-shrink: 0;
  display: block;
  clear: both;
}

/* 优化后的箭头图标样式 */
.group-arrow {
  transition: transform 0.3s ease;
  color: #909399;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: rgba(144, 147, 153, 0.1);
}

.group-arrow.is-open {
  transform: rotate(90deg);
  color: #409eff;
  background-color: rgba(64, 158, 255, 0.1);
}

.group-title:hover .group-arrow {
  color: #409eff;
  background-color: rgba(64, 158, 255, 0.15);
}

/* 交互态优化 */
.el-select:hover, .el-input-number:hover, .el-input:hover {
  border-color: #409eff;
}

.el-select:focus, .el-input-number:focus, .el-input:focus {
  border-color: #409eff;
  box-shadow: 0 0 0 2px #666;
}

/* 响应式调整 - 小屏幕适配 */
@media (max-width: 768px) {
  .form-label {
    width: 120px; /* 小屏幕下缩小标签宽度 */
  }
}

/* 配置修改弹框样式 */
.chip-request-dialog {
  --el-dialog-border-radius: 12px;
}

.chip-request-dialog .el-dialog {
  border-radius: 12px;
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

.chip-request-dialog .el-dialog__header {
  display: none;
}

.chip-request-dialog .el-dialog__body {
  padding: 0;
}

.request-dialog-content {
  padding: 32px 24px 24px;
  text-align: center;
  background: linear-gradient(135deg, #f8faff 0%, #ffffff 100%);
}

.request-dialog-header {
  margin-bottom: 24px;
}

.request-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 64px;
  height: 64px;
  background: linear-gradient(135deg, #409eff 0%, #fff 100%);
  border-radius: 50%;
  margin-bottom: 16px;
  box-shadow: 0 4px 16px rgba(64, 158, 255, 0.3);
}

.request-icon .el-icon {
  font-size: 28px;
  color: white;
}

.rotating-icon {
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.request-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  line-height: 1.4;
}

.request-dialog-body {
  margin-bottom: 24px;
}

.request-info {
  background: #f4f7ff;
  border: 1px solid #e1e8ff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
}

.info-item {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 8px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.info-value {
  font-size: 14px;
  color: #409eff;
  font-weight: 600;
  background: #ecf5ff;
  padding: 2px 8px;
  border-radius: 4px;
}

.request-message {
  font-size: 14px;
  color: #909399;
  line-height: 1.6;
  padding: 0 8px;
}

.request-progress {
  margin-top: 20px;
}

.request-progress .el-progress {
  margin-bottom: 0;
}

.request-progress .el-progress__text {
  display: none;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .chip-request-dialog .el-dialog {
    width: 90% !important;
    margin: 0 auto;
  }

  .request-dialog-content {
    padding: 24px 16px 16px;
  }

  .request-icon {
    width: 56px;
    height: 56px;
  }

  .request-icon .el-icon {
    font-size: 24px;
  }

  .request-title {
    font-size: 18px;
  }
}

/* 子字段样式 */
.sub-field {
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 8px;
  margin-top: 12px;
  margin-left: 0 !important;
  position: relative;
  overflow: hidden;
}

.sub-field::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
}

.sub-field .form-label {
  color: #606266;
  font-size: 13px;
  font-weight: 500;
}

.sub-field:hover {
  background: linear-gradient(135deg, #ecf5ff 0%, #f0f9ff 100%);
  border-left-color: #67c23a;
}
/* 在原有样式基础上添加 */
:deep(.el-input__prefix) {
  color: #ccc; /* 0x前缀颜色 */
}
.text-gray-400 {
  color: #999;
  font-size: 12px;
}









</style>