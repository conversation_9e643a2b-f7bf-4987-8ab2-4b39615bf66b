<template>
  <!-- 提示弹框 -->
  <TipDialog v-if="showTipDialog" />
  <div v-else v-custom-loading="loading">
    <!-- 顶部工具栏组件 -->
    <Toolbar
      :sdk-version="SdkVersion"
      :form="form"
      :space-options="spaceOptions"
      :branch-options="branchOptions"
      :loading="loading"
      :has-edit-permission="hasEditPermission"
      @update:form="(newForm) => Object.assign(form, newForm)"
      @gitlab-click="handleGitlabClick"
      @download="handleDownload"
      @save="handleSave"
      @publish="handlePublish"
      @merge-test="mergetest"
      @confirm-action="confirmAction"
    />
      <!-- 主要内容区域 -->
      <div class="main-content">
        <!-- 左侧容器 -->
        <div class="left-container">
          <!-- 左侧第一行：分为两列 -->
          <div class="left-row-1">
            <!-- 芯片配置组件 - 占30% -->
            <div class="chip-config-section">
              <ChipConfig
                v-model:chip-form="chipForm"
                :chip-options="chipOptions"
                :chip-options-loading="chipOptionsLoading"
                @chip-model-change="handleChipModelChange"
                @vcc-voltage-change="handleVccVoltageChange"
                @clock-freq-change="handleClockFreqChange"
              />
            </div>
            <!-- 右侧80%区域 - 芯片预览 -->
            <div class="other-config-section">
              <ChipPreview
                :pin-count="chipPreviewData.pinCount"
                :chip-model="chipPreviewData.chipModel"
                :pins="chipPreviewData.pins"
                :type-style-config="chipPreviewData.typeStyleConfig"
                :highlighted-pin="highlightedPin"
                @pin-click="handlePinClick"
                @pin-edit="handlePinEdit"
              />
            </div>
          </div>

          <!-- 左侧第二行 -->
          <div class="left-row-2">
            <PinConfigTable
              :pin-table-data="pinTableData"
              :search-key="searchKey"
              :io-config="ioConfig"
              :current-page="currentPage"
              :page-size="pageSize"
              :available-io-types="availableIoTypes"
              :type-style-config="chipPreviewData.typeStyleConfig"
              @update:search-key="searchKey = $event"
              @update:io-config="ioConfig = $event"
              @update:current-page="currentPage = $event"
              @update:page-size="pageSize = $event"
              @row-click="handlePinTableRowClick"
              @edit-pin="handlePinEdit"
            />
          </div>
        </div>

        <!-- 右侧容器 -->
        <div class="right-container">
          <PinEditorContainer
            :pinData="pinEditorData"
            :loading="pinEditorLoading"
            :availableIoTypes="availableIoTypes"
            :repositoryInfo="repositoryInfo"
            :pinInfo="pinEditorData.pinInfoData"
            @field-change="handleFieldChange"
            @module-change="handleModuleChange"
            @request-module-config="handleRequestModuleConfig"
            @module-config-change="handleModuleConfigChange"
            />

        </div>
      </div>
  </div>


</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted, watch } from 'vue';
import http from '@/utils/http/http';
import { ElMessageBox } from 'element-plus';
import messageManager from '@/utils/messageManager';
import { useProjectStore } from '@/stores/project.js';
import Toolbar from '@/views/code_management/Toolbar.vue';
import ChipConfig from './components/ChipConfig.vue';
import ChipPreview from './components/ChipPreview.vue';
import PinConfigTable from './components/PinConfigTable.vue';
import PinEditorContainer from './components/PinEditorContainer.vue';   
import TipDialog from '../None.vue';
const projectStore = useProjectStore();

// 项目相关变量
let project_code = '';
let project_name = '';

const form = reactive({
  gitlab: '',
  project_branch: ''
});
const spaceOptions = ref([]);
const branchOptions = ref([]);
const branch_create = ref(true);

// 工作空间和分支状态
const workspace = ref('');
const branch_status = ref('');
let workspace_path = '';


// 初始化时从全局状态恢复数据
const initializeFromStore = () => {
  const codeManagement = projectStore.codeManagement;
  form.gitlab = codeManagement.gitlab || '';
  form.project_branch = codeManagement.project_branch || '';
  spaceOptions.value = codeManagement.spaceOptions || [];
  branchOptions.value = codeManagement.branchOptions || [];
  SdkVersion.value = codeManagement.sdkVersion || '';
};

const SdkVersion = ref('');
const loading = ref(false);
const showTipDialog = ref(true);

// 芯片相关数据
const chip = ref(null);
const color = ref([]);
const io = ref([]);
const pinCount = ref(0);
const table = ref([])
const chipOptions = ref([]);
const chipOptionsLoading = ref(false);
const chipForm = reactive({
  chipModel: '',
  vccVoltage: 3.3,
  clockFreq: 160,
  ioConfig: []
});

// 芯片预览相关数据
const highlightedPin = ref(null);
const chipPreviewData = reactive({
  pinCount: 0,
  chipModel: '',
  pins: [],
  typeStyleConfig: {}
});

// 权限控制 - 基于branch_create状态
const hasEditPermission = computed(() => branch_create.value);

// PinConfigTable 组件所需数据
const pinTableData = ref([]);
const searchKey = ref('');
const ioConfig = ref([]);
const currentPage = ref(1);
const pageSize = ref(10);
const availableIoTypes = ref([]);

// 引脚功能类型缓存 - 存储每个引脚的功能类型选择
const pinTypeCache = ref({});

// 引脚配置缓存 - 以引脚号为主键，缓存每个引脚的完整配置信息
const pinConfigCache = ref({});

// 模块配置缓存 - 以"引脚ID_功能类型"为键，缓存对应的模块配置信息
const moduleConfigCache = ref({});

// 引脚完整状态缓存 - 保存每个引脚的完整配置状态（功能类型 + 模块配置 + 表单数据）
const pinCompleteStateCache = ref({});

// 引脚编辑器数据
const pinEditorData = ref({});
const pinEditorLoading = ref(false);

// 仓库信息
const repositoryInfo = computed(() => ({
  project_code: project_code,
  project_name: project_name,
  project_gitlab: form.gitlab,
  project_branch: form.project_branch
}));







// 单击仓库
const handleGitlabClick = () => {
  // 判断有无存在的ElMessage， 存在则关闭
  if (!project_code) {
    messageManager.warning('请选择项目！')
  }
}

// 芯片配置事件处理函数
const handleChipModelChange = (value) => {
  console.log('芯片型号变更:', value);
  chipForm.chipModel = value;
};

const handleVccVoltageChange = (value) => {
  console.log('VCC电压变更:', value);
  chipForm.vccVoltage = value;
};

const handleClockFreqChange = (value) => {
  console.log('时钟频率变更:', value);
  chipForm.clockFreq = value;
};

// 监听 chipForm 变化（调试用）
watch(chipForm, (newForm) => {
  console.log('父组件: chipForm 变化:', newForm);
  // 同步更新芯片预览的芯片型号
  if (newForm.chipModel) {
    chipPreviewData.chipModel = newForm.chipModel;
  }
}, { deep: true });

// 引脚相关事件处理函数
const handlePinClick = async (pinId) => {
  console.log('引脚点击:', pinId);

  // 保存当前引脚的状态（如果有的话）
  if (highlightedPin.value && highlightedPin.value !== pinId) {
    savePinCompleteState(highlightedPin.value);
  }

  highlightedPin.value = pinId;

  // 自动请求引脚编辑器数据
  await getPinEditorData(pinId);
};

const handlePinEdit = async (pinData) => {
  console.log('引脚编辑事件触发:', pinData);

  const pinId = pinData.pinId || pinData.pinNumber;

  // 保存当前引脚的状态（如果有的话）
  if (highlightedPin.value && highlightedPin.value !== pinId) {
    savePinCompleteState(highlightedPin.value);
  }

  // 清除历史高亮效果，设置当前引脚高亮
  highlightedPin.value = pinId;

  console.log('设置引脚高亮 - 引脚ID:', pinId, '类型:', typeof pinId);
  console.log('当前highlightedPin值:', highlightedPin.value);

  // 自动请求引脚编辑器数据
  await getPinEditorData(pinId);
};

const handlePinTableRowClick = async (rowData) => {
  console.log('表格行点击:', rowData);

  const pinId = rowData.pinId || rowData.pinNumber;

  // 保存当前引脚的状态（如果有的话）
  if (highlightedPin.value && highlightedPin.value !== pinId) {
    savePinCompleteState(highlightedPin.value);
  }

  highlightedPin.value = pinId;

  console.log('表格行点击 - 引脚ID:', pinId, '类型:', typeof pinId);
  console.log('当前highlightedPin值:', highlightedPin.value);

  // 自动请求引脚编辑器数据，刷新类型数据和颜色
  await getPinEditorData(pinId);
};

// 清除引脚高亮
const clearPinHighlight = () => {
  highlightedPin.value = null;
  console.log('清除引脚高亮');
};

// 缓存管理函数
// 保存引脚配置到缓存
const savePinConfigToCache = (pinId, configData) => {
  console.log(`💾 保存引脚 ${pinId} 配置到缓存:`, configData);
  pinConfigCache.value[pinId] = {
    ...configData,
    lastUpdated: Date.now()
  };
  console.log(`📦 当前引脚配置缓存状态:`, Object.keys(pinConfigCache.value));
};

// 从缓存获取引脚配置
const getPinConfigFromCache = (pinId) => {
  const cached = pinConfigCache.value[pinId];
  if (cached) {
    console.log(`📖 从缓存获取引脚 ${pinId} 配置:`, cached);
    return cached;
  }
  console.log(`📖 引脚 ${pinId} 无缓存配置`);
  return null;
};

// 保存模块配置到缓存（按引脚+功能类型组合）
const saveModuleConfigToCache = (pinId, moduleType, configData) => {
  const cacheKey = `${pinId}_${moduleType}`;
  console.log(`💾 保存引脚 ${pinId} 的模块类型 ${moduleType} 配置到缓存:`, configData);
  moduleConfigCache.value[cacheKey] = {
    config: configData,
    pinId: pinId,
    moduleType: moduleType,
    lastUpdated: Date.now()
  };
  console.log(`📦 当前模块配置缓存键:`, Object.keys(moduleConfigCache.value));
};

// 从缓存获取模块配置（按引脚+功能类型组合）
const getModuleConfigFromCache = (pinId, moduleType) => {
  const cacheKey = `${pinId}_${moduleType}`;
  const cached = moduleConfigCache.value[cacheKey];
  if (cached && cached.config && Array.isArray(cached.config) && cached.config.length > 0) {
    console.log(`📖 从缓存获取引脚 ${pinId} 的模块类型 ${moduleType} 配置:`, cached.config);
    return cached.config;
  }
  console.log(`📖 引脚 ${pinId} 的模块类型 ${moduleType} 无有效缓存配置`);
  return null;
};

// 清除特定引脚+功能类型的模块配置缓存
const clearModuleConfigCache = (pinId, moduleType) => {
  const cacheKey = `${pinId}_${moduleType}`;
  if (moduleConfigCache.value[cacheKey]) {
    delete moduleConfigCache.value[cacheKey];
    console.log(`🗑️ 已清除引脚 ${pinId} 的模块类型 ${moduleType} 缓存`);
  }
};

// 清除所有缓存（用于测试）
const clearAllCache = () => {
  pinConfigCache.value = {};
  moduleConfigCache.value = {};
  pinTypeCache.value = {};
  pinCompleteStateCache.value = {};
  console.log('🗑️ 已清除所有缓存');
};

// 保存引脚完整状态到缓存
const savePinCompleteState = (pinId) => {
  if (!pinEditorData.value || !pinId) {
    console.log('⚠️ 无法保存引脚状态：缺少数据或引脚ID');
    return;
  }

  const currentState = {
    pinId: pinId,
    pinName: pinEditorData.value.pinName || `PIN${pinId}`,
    pinType: pinEditorData.value.pinType || '',
    moduleConfig: pinEditorData.value.moduleConfig || [],
    formData: { ...pinEditorData.value }, // 保存完整的表单数据
    lastUpdated: Date.now(),
    isConfigured: !!(pinEditorData.value.pinType && pinEditorData.value.pinType !== '')
  };

  pinCompleteStateCache.value[pinId] = currentState;
  console.log(`💾 保存引脚 ${pinId} 完整状态:`, currentState);
  console.log(`📦 当前已缓存的引脚:`, Object.keys(pinCompleteStateCache.value));
};

// 从缓存恢复引脚完整状态
const restorePinCompleteState = (pinId) => {
  const cachedState = pinCompleteStateCache.value[pinId];
  if (cachedState && cachedState.isConfigured) {
    console.log(`📖 恢复引脚 ${pinId} 完整状态:`, cachedState);
    return cachedState;
  }
  console.log(`📖 引脚 ${pinId} 无已配置的缓存状态`);
  return null;
};

// 显示缓存状态（用于调试）
const showCacheStatus = () => {
  console.log('📊 缓存状态报告:');
  console.log('  - 引脚配置缓存:', Object.keys(pinConfigCache.value));
  console.log('  - 模块配置缓存 (引脚ID_功能类型):', Object.keys(moduleConfigCache.value));
  console.log('  - 引脚类型缓存:', Object.keys(pinTypeCache.value));
  console.log('  - 引脚完整状态缓存:', Object.keys(pinCompleteStateCache.value));

  // 详细显示模块配置缓存
  if (Object.keys(moduleConfigCache.value).length > 0) {
    console.log('📋 模块配置缓存详情:');
    Object.entries(moduleConfigCache.value).forEach(([key, value]) => {
      console.log(`    ${key}: ${value.config?.length || 0} 个配置字段`);
    });
  }

  // 详细显示引脚完整状态缓存
  if (Object.keys(pinCompleteStateCache.value).length > 0) {
    console.log('📋 引脚完整状态缓存详情:');
    Object.entries(pinCompleteStateCache.value).forEach(([pinId, state]) => {
      console.log(`    引脚${pinId}: ${state.pinType || '未配置'} (${state.isConfigured ? '已配置' : '未配置'})`);
    });
  }
};

// 处理功能类型变化
const handleModuleChange = async (moduleData) => {
  console.log('主组件 - 功能类型变化:', moduleData);
  console.log('当前芯片预览数据:', chipPreviewData);
  console.log('当前引脚配置表数据:', pinTableData.value);

  const { pinId, newModule, formData } = moduleData;

  if (!pinId) {
    console.error('pinId为空，无法更新引脚信息');
    return;
  }

  if (!newModule) {
    console.error('newModule为空，无法更新引脚信息');
    return;
  }

  console.log(`开始更新引脚 ${pinId} 的类型为: ${newModule}`);

  // 0. 缓存引脚的功能类型选择
  pinTypeCache.value[pinId] = newModule;
  console.log(`🔄 缓存引脚 ${pinId} 的功能类型: ${newModule}`);
  console.log('📦 当前完整缓存状态:', JSON.stringify(pinTypeCache.value, null, 2));

  // 0.1 缓存引脚的基本配置信息
  if (formData) {
    savePinConfigToCache(pinId, {
      pinId: pinId,
      pinName: formData.pin_name || `PIN${pinId}`,
      pinType: newModule,
      formData: { ...formData },
      moduleType: newModule
    });
  }

  // 1. 更新芯片预览中对应引脚的颜色
  updateChipPreviewPinColor(pinId, newModule);

  // 2. 更新引脚配置表中的数据
  updatePinConfigTableData(pinId, newModule);

  // 3. 更新当前引脚信息
  updateCurrentPinInfo(pinId, newModule);

  // 注意：模块配置请求现在由DynamicPinEditor组件在功能类型有值时自动触发

  // 保存当前引脚的完整状态
  savePinCompleteState(pinId);

  console.log('所有更新完成');
};

// 处理字段变化
const handleFieldChange = (fieldData) => {
  // 兼容旧的调用方式
  if (typeof fieldData === 'string') {
    const fieldKey = fieldData;
    const fieldValue = arguments[1];
    console.log('🔄 index.vue - 收到字段变化事件(旧格式):', fieldKey, fieldValue);

    // 如果是功能类型字段变化，触发模块配置请求
    if (fieldKey === 'module') {
      console.log('🎯 index.vue - 功能类型字段变化，触发模块配置请求');
      handleRequestModuleConfig(fieldValue);
    }
    return;
  }

  // 新的调用方式
  console.log('🔄 index.vue - 收到字段变化事件(新格式):', fieldData);

  const { pinId, fieldKey, fieldValue, formData } = fieldData;

  if (pinId && formData) {
    // 实时更新缓存的引脚配置
    savePinConfigToCache(pinId, {
      pinId: pinId,
      pinName: formData.pin_name || `PIN${pinId}`,
      pinType: formData.module || '',
      formData: { ...formData },
      moduleType: formData.module || '',
      lastUpdatedField: fieldKey,
      lastUpdatedValue: fieldValue
    });

    console.log(`💾 实时缓存引脚 ${pinId} 的字段变化: ${fieldKey} = ${fieldValue}`);
  }

  // 如果是功能类型字段变化，触发模块配置请求
  if (fieldKey === 'module') {
    console.log('🎯 index.vue - 功能类型字段变化，触发模块配置请求');
    handleRequestModuleConfig(fieldValue);
  } else if (pinId && formData && formData.module) {
    // 如果是模块配置字段变化，更新该引脚+功能类型的模块配置缓存
    const currentModuleConfig = pinEditorData.value?.moduleConfig || [];
    if (currentModuleConfig.length > 0) {
      console.log(`💾 更新引脚 ${pinId} 功能类型 ${formData.module} 的模块配置缓存`);
      saveModuleConfigToCache(pinId, formData.module, currentModuleConfig);
    }
  }

  // 保存当前引脚的完整状态（如果有引脚ID和表单数据）
  if (pinId && formData) {
    savePinCompleteState(pinId);
  }
};

// 处理请求模块配置
const handleRequestModuleConfig = async (moduleType) => {
  console.log('🎯 收到请求模块配置事件:', moduleType);

  // 获取当前引脚ID
  const currentPinId = pinEditorData.value?.pinId;
  if (!currentPinId) {
    console.error('❌ 当前引脚ID为空，无法请求模块配置');
    return;
  }

  try {
    // 调用requestChipModulConfig获取模块配置，传递引脚ID
    const result = await requestChipModulConfig(moduleType, currentPinId);

    if (result && result.config) {
      const moduleConfig = result.config;
      const fromCache = result.fromCache;

      console.log(`✅ 获取到模块配置 (${fromCache ? '来自缓存' : '来自API'}):`, moduleConfig);
      console.log('📊 模块配置字段数量:', moduleConfig.length);

      if (moduleConfig.length === 0) {
        console.log('ℹ️ 该功能类型暂无模块配置字段，设置为空数组');
      }

      // 确保pinEditorData存在
      if (pinEditorData.value) {
        // 设置模块配置数据，并标记是否来自缓存
        pinEditorData.value.moduleConfig = moduleConfig;
        // 只有非空配置才标记为来自缓存，空配置始终标记为非缓存
        pinEditorData.value.moduleConfigFromCache = fromCache && moduleConfig.length > 0;

        console.log('✅ 模块配置已设置到pinEditorData.value.moduleConfig');
        console.log('📋 当前pinEditorData.value.moduleConfig:', pinEditorData.value.moduleConfig);
        console.log('🏷️ 配置来源:', fromCache ? '缓存' : 'API');

        // 强制触发响应式更新
        pinEditorData.value = { ...pinEditorData.value };
        console.log('🔄 强制触发响应式更新');
      } else {
        console.error('❌ pinEditorData.value为空，无法设置模块配置');
      }
    } else {
      console.warn('⚠️ 模块配置数据格式不正确，设置为空数组:', result);
      // 即使数据格式不正确，也设置为空数组，避免报错
      if (pinEditorData.value) {
        pinEditorData.value.moduleConfig = [];
        pinEditorData.value.moduleConfigFromCache = false;
        pinEditorData.value = { ...pinEditorData.value };
      }
    }
  } catch (error) {
    console.error('❌ 请求模块配置失败:', error);
  }
};

// 处理模块配置变化
const handleModuleConfigChange = async (configChangeData) => {
  console.log('🔄 收到模块配置变化事件:', configChangeData);

  const { pinId, pinName, moduleType, fieldKey, fieldValue, fieldLabel, formData } = configChangeData;

  // 检查必要参数
  if (!pinId || !moduleType || !fieldKey || fieldValue === undefined) {
    console.warn('⚠️ 模块配置变化参数不完整，跳过处理');
    return;
  }

  // 检查工作空间和分支状态
  if (!workspace.value || !branch_status.value) {
    console.warn('⚠️ 工作空间或分支状态为空，跳过模块配置变化处理');
    return;
  }

  // 检查芯片名称
  if (!chipForm.chipModel) {
    console.warn('⚠️ 芯片名称为空，跳过模块配置变化处理');
    return;
  }

  // 获取通道名称的值，如果formData中有name字段则使用，否则使用fieldLabel作为默认值
  const channelName = formData?.name || fieldLabel;

  try {
    console.log('🚀 发送模块配置变化请求到服务器');
    console.log('📋 请求参数:', {
      workspace_path: workspace.value,
      branch_status: branch_status.value,
      chip_name: chipForm.chipModel,
      pin_number: pinId,
      function_type: moduleType,
      name: channelName,
      change_label: fieldLabel,
      change_value: fieldValue
    });

    const response = await http.get('/code_management/chip_change', {
      params: {
        workspace_path: workspace.value,
        branch_status: branch_status.value,
        chip_name: chipForm.chipModel,
        pin_number: pinId,
        function_type: moduleType,
        name: channelName,
        change_label: fieldLabel,
        change_value: fieldValue
      }
    });

    console.log('✅ 模块配置变化请求响应:', response.data);

    if (response.data && response.data.status === 1) {
      console.log('✅ 模块配置变化处理成功');
      messageManager.success('配置修改成功');
      // 可以在这里添加成功后的处理逻辑
    } else {
      console.warn('⚠️ 模块配置变化处理失败:', response.data?.message || '未知错误');
      messageManager.error('配置修改失败: '+ (response.data?.message || '未知错误'));
    }
  } catch (error) {
    console.error('❌ 发送模块配置变化请求失败:', error);
    // 这里不显示错误消息，避免频繁弹窗影响用户体验
  }
};

// 更新芯片预览中引脚颜色
const updateChipPreviewPinColor = (pinId, newModule) => {
  if (chipPreviewData.pins && chipPreviewData.pins.length > 0) {
    const pinIndex = chipPreviewData.pins.findIndex(pin =>
      pin.pinId?.toString() === pinId?.toString()
    );

    if (pinIndex !== -1) {
      // 创建新的引脚对象来确保响应式更新
      const updatedPin = {
        ...chipPreviewData.pins[pinIndex],
        type: newModule,
        pinType: newModule,
        displayType: newModule
      };

      // 使用数组替换来触发响应式更新
      chipPreviewData.pins.splice(pinIndex, 1, updatedPin);

      console.log(`更新芯片预览引脚 ${pinId} 类型为: ${newModule}`);
      console.log('更新后的引脚数据:', updatedPin);
      console.log('更新后的完整pins数组:', chipPreviewData.pins);
    } else {
      console.warn(`未找到引脚 ${pinId} 在芯片预览数据中`);
    }
  } else {
    console.warn('芯片预览数据为空或未初始化');
  }
};

// 更新引脚配置表数据
const updatePinConfigTableData = (pinId, newModule) => {
  if (pinTableData.value && pinTableData.value.length > 0) {
    const pinIndex = pinTableData.value.findIndex(pin =>
      pin.pinId?.toString() === pinId?.toString()
    );

    if (pinIndex !== -1) {
      // 更新引脚类型和状态
      pinTableData.value[pinIndex].pinType = newModule;
      pinTableData.value[pinIndex].status = '已配置';
      console.log(`更新引脚配置表引脚 ${pinId} 类型为: ${newModule}, 状态为: 已配置`);
    }
  }
};

// 更新当前引脚信息
const updateCurrentPinInfo = (pinId, newModule) => {
  if (pinEditorData.value && pinEditorData.value.pinId?.toString() === pinId?.toString()) {
    pinEditorData.value.pinType = newModule;
    console.log(`更新当前引脚信息 ${pinId} 类型为: ${newModule}`);
  }
};

// 请求芯片模块配置
const requestChipModulConfig = async (moduleType, pinId = null) => {
  try {
    console.log(`🔄 开始请求芯片模块配置, 功能类型: ${moduleType}, 引脚ID: ${pinId}`);

    // 如果有引脚ID，首先检查该引脚的缓存
    if (pinId) {
      const cachedConfig = getModuleConfigFromCache(pinId, moduleType);
      if (cachedConfig) {
        console.log(`📖 使用引脚 ${pinId} 的缓存模块配置: ${moduleType}`);
        return {
          config: cachedConfig,
          fromCache: true,
          pinId: pinId
        };
      }
    }

    const response = await http.get('/code_management/chip_modul', {
      params: {
        module: moduleType
      }
    });

    console.log("芯片模块配置完整响应:", response);
    console.log("芯片模块配置数据:", response.data);

    if (response.data && response.data.status === 1) {
      // 处理成功响应
      const responseData = response.data.data || {};
      console.log('🎉 芯片模块配置API响应数据:', responseData);

      // 检查数据结构，提取实际的配置数组
      let modulConfig = [];
      if (Array.isArray(responseData)) {
        // 如果直接是数组
        modulConfig = responseData;
      } else if (responseData.module && Array.isArray(responseData.module)) {
        // 如果是 {module: Array} 结构
        modulConfig = responseData.module;
      } else {
        console.warn('⚠️ 未知的模块配置数据结构:', responseData);
        // 返回空数组而不是null，避免报错
        modulConfig = [];
      }

      console.log('✅ 提取的模块配置数组:', modulConfig);
      console.log('📊 配置字段数量:', modulConfig.length);

      // 如果是空数组，给出友好提示
      if (Array.isArray(modulConfig) && modulConfig.length === 0) {
        console.log('ℹ️ 该功能类型暂无模块配置字段');
      } else if (Array.isArray(modulConfig) && modulConfig.length > 0) {
        // 打印每个配置字段的详细信息
        modulConfig.forEach((field, index) => {
          console.log(`📋 字段${index + 1}:`, {
            key: field.key,
            label: field.label,
            type: field.type,
            options: field.options?.length || 0
          });
        });
      }

      // 如果有引脚ID且模块配置不为空，才保存到该引脚的缓存中
      if (pinId && Array.isArray(modulConfig) && modulConfig.length > 0) {
        console.log(`💾 保存非空模块配置到缓存: 引脚${pinId}, 类型${moduleType}, 字段数${modulConfig.length}`);
        saveModuleConfigToCache(pinId, moduleType, modulConfig);
      } else if (pinId && Array.isArray(modulConfig) && modulConfig.length === 0) {
        console.log(`⚠️ 模块配置为空，清除可能存在的旧缓存: 引脚${pinId}, 类型${moduleType}`);
        clearModuleConfigCache(pinId, moduleType);
      }

      return {
        config: modulConfig,
        fromCache: false,
        pinId: pinId
      };
    } else {
      console.error('❌ 芯片模块配置API返回状态不正确:', response.data);
      messageManager.error('获取芯片模块配置失败: ' + (response.data?.message || '未知错误'));
      return null;
    }
  } catch (error) {
    console.error('请求芯片模块配置失败:', error);
    messageManager.error('请求芯片模块配置失败: ' + error.message);
    return null;
  }
};

// 获取引脚编辑器数据
const getPinEditorData = async (pinId) => {
  try {
    pinEditorLoading.value = true;
    console.log('🔄 开始获取引脚编辑器数据, 引脚ID:', pinId);
    console.log('🔄 开始获取引脚编辑器数据, chip:', chip.value);

    // 首先检查是否有缓存的完整状态
    const cachedCompleteState = restorePinCompleteState(pinId);
    if (cachedCompleteState) {
      console.log('🎯 发现引脚完整状态缓存，直接恢复:', cachedCompleteState);

      // 直接使用缓存的完整状态
      pinEditorData.value = {
        ...cachedCompleteState.formData,
        pinId: pinId,
        pinName: cachedCompleteState.pinName,
        pinType: cachedCompleteState.pinType,
        moduleConfig: cachedCompleteState.moduleConfig || [],
        moduleConfigFromCache: true
      };

      // 同步更新芯片预览和引脚配置表
      if (cachedCompleteState.pinType) {
        updateChipPreviewPinColor(pinId, cachedCompleteState.pinType);
        updatePinConfigTableData(pinId, cachedCompleteState.pinType);
      }

      pinEditorLoading.value = false;
      console.log('✅ 从缓存恢复引脚数据完成');
      return;
    }
    const response = await http.get('/code_management/chip_config', {
      params: {
        pinId: pinId,
        chip: chip.value
      }
    });

    console.log("获取引脚编辑器数据完整响应:", response);
    console.log("获取引脚编辑器数据:", response.data);

    if (response.data && response.data.status === 1) {
      // 处理成功响应
      const editorData = response.data.data || {};
      console.log('引脚编辑器数据处理成功:', editorData);

      // 从table数据中找到对应的引脚信息
      let pinInfo = {};
      if (editorData.table && Array.isArray(editorData.table)) {
        pinInfo = editorData.table.find(pin =>
          pin.pin_id?.toString() === pinId?.toString() ||
          pin.pin?.toString() === pinId?.toString()
        ) || {};
      }

      console.log('找到的引脚信息:', pinInfo);

      // 检查是否有缓存的引脚配置
      const cachedPinConfig = getPinConfigFromCache(pinId);
      if (cachedPinConfig) {
        console.log(`📖 发现引脚 ${pinId} 的缓存配置:`, cachedPinConfig);
      }

      // 处理功能类型数据，确保module是数组
      let moduleOptions = [];
      if (pinInfo.module) {
        if (Array.isArray(pinInfo.module)) {
          moduleOptions = pinInfo.module;
        } else {
          moduleOptions = [pinInfo.module];
        }
      }

      // 获取pin_info数据
      let pinInfoData = editorData.pin_info || [];
      console.log('获取到的pin_info数据:', pinInfoData);
      console.log('pin_info数据类型:', typeof pinInfoData);
      console.log('pin_info是否为数组:', Array.isArray(pinInfoData));

      // 确保pinInfoData是数组
      if (!Array.isArray(pinInfoData)) {
        pinInfoData = [];
      }

      // 检查是否已经包含功能类型字段，如果没有则添加
      const hasModuleField = pinInfoData.some(field => field.key === 'module');
      if (!hasModuleField && moduleOptions.length > 0) {
        console.log('添加功能类型字段到pin_info');
        pinInfoData.push({
          key: 'module',
          display: '功能类型',
          value: moduleOptions
        });
      }

      // 检查缓存中是否有该引脚的功能类型选择
      const cachedPinType = pinTypeCache.value[pinId];
      console.log(`🔍 检查引脚 ${pinId} 的缓存类型:`, cachedPinType);
      console.log('📦 当前完整缓存状态:', JSON.stringify(pinTypeCache.value, null, 2));

      // 处理pin_info数据，如果有缓存的功能类型，更新到pin_info中
      let processedPinInfo = pinInfoData;
      if (Array.isArray(pinInfoData) && cachedPinType) {
        console.log(`✅ 为引脚 ${pinId} 应用缓存的功能类型: ${cachedPinType}`);
        processedPinInfo = pinInfoData.map(field => {
          if (field.key === 'module') {
            const updatedField = {
              ...field,
              value: field.value, // 保持原有的选项列表
              cachedValue: cachedPinType // 添加缓存值
            };
            console.log(`🎯 更新module字段:`, updatedField);
            return updatedField;
          }
          return field;
        });
      } else {
        console.log(`ℹ️ 引脚 ${pinId} 没有缓存的功能类型或pin_info数据无效`);
      }

      // 更新引脚编辑器数据
      pinEditorData.value = {
        ...editorData,
        pinId: pinInfo.pin_id || pinInfo.pin || pinId,
        pinName: pinInfo.pin_name || pinInfo.name || `PIN${pinId}`,
        pinType: cachedPinType || '', // 优先使用缓存的值，否则为空
        moduleOptions: moduleOptions, // 功能类型选项
        chipModel: chipForm.chipModel,
        originalPinInfo: pinInfo, // 保存原始引脚信息
        pinInfoData: processedPinInfo // 传递处理后的pin_info数据到引脚编辑器
      };

      console.log('更新后的引脚编辑器数据:', pinEditorData.value);

      // 如果有缓存的引脚配置，恢复表单数据
      if (cachedPinConfig && cachedPinConfig.formData) {
        console.log(`🔄 恢复引脚 ${pinId} 的缓存表单数据:`, cachedPinConfig.formData);
        // 将缓存的表单数据合并到引脚编辑器数据中
        pinEditorData.value = {
          ...pinEditorData.value,
          cachedFormData: cachedPinConfig.formData
        };
      }

      // 同步更新芯片预览和引脚配置表的数据
      const currentPinType = pinEditorData.value.pinType;
      if (currentPinType && currentPinType !== '') {
        console.log(`同步更新引脚 ${pinId} 的类型为: ${currentPinType}`);

        // 更新芯片预览中的引脚颜色和类型
        updateChipPreviewPinColor(pinId, currentPinType);

        // 更新引脚配置表中的数据，状态设为已配置
        updatePinConfigTableData(pinId, currentPinType);

        // 如果有该引脚+功能类型的缓存模块配置，自动加载
        const cachedModuleConfig = getModuleConfigFromCache(pinId, currentPinType);
        if (cachedModuleConfig && Array.isArray(cachedModuleConfig) && cachedModuleConfig.length > 0) {
          console.log(`🔄 自动加载引脚 ${pinId} 的缓存模块配置: ${currentPinType}`, cachedModuleConfig);
          pinEditorData.value.moduleConfig = cachedModuleConfig;
          pinEditorData.value.moduleConfigFromCache = true;
          // 强制触发响应式更新
          pinEditorData.value = { ...pinEditorData.value };
        } else {
          console.log(`ℹ️ 引脚 ${pinId} 的功能类型 ${currentPinType} 无有效缓存模块配置`);
          // 没有缓存时，不设置默认的模块配置
          pinEditorData.value.moduleConfig = [];
          pinEditorData.value.moduleConfigFromCache = false;
        }

        console.log('引脚数据同步更新完成');
      }

      return editorData;
    } else {
      console.error('API返回状态不正确:', response.data);
      messageManager.error('获取引脚编辑器数据失败: ' + (response.data?.message || '未知错误'));
      return null;
    }
  } catch (error) {
    console.error('获取引脚编辑器数据失败:', error);
    messageManager.error('获取引脚编辑器数据失败: ' + error.message);
    return null;
  } finally {
    pinEditorLoading.value = false;
  }
};

// 监听窗口大小变化（保留用于未来扩展）
const handleResize = () => {
  // 可以在这里添加响应式布局逻辑
};

// 组件挂载时初始化数据
onMounted(async () => {
  try {
    loading.value = true;

    // 从全局状态初始化数据
    initializeFromStore();

    // 监控项目信息变化
    const info = projectStore.project_info || {};
    project_code = info.projectCode || '';
    project_name = info.name || '';

    console.log('project_code:', project_code);
    console.log('project_name:', project_name);
    console.log('从全局状态恢复的仓库信息:', form.gitlab);
    console.log('从全局状态恢复的分支信息:', form.project_branch);

    if (project_code=="" && project_name=="") {
      console.info('项目信息为空，请先选择项目');
      showTipDialog.value = true;
    } else {
      console.info('项目信息已选择，开始初始化');
      showTipDialog.value = false;
      // 如果没有仓库信息，则获取仓库列表
      if (!form.gitlab || spaceOptions.value.length === 0) {
        await get_space();
      } else {
        // 如果有仓库信息但没有分支信息，则获取分支列表
        if (!form.project_branch || branchOptions.value.length === 0) {
          await get_branch();
        } else {
          // 如果都有，直接提交分支信息
          await submit_branch_info();
        }
      }
    }



    // 添加窗口大小变化监听
    window.addEventListener('resize', handleResize);

    // 在开发环境中暴露调试函数到全局
    if (process.env.NODE_ENV === 'development') {
      window.debugCache = {
        showStatus: showCacheStatus,
        clearAll: clearAllCache,
        getPinConfig: getPinConfigFromCache,
        getModuleConfig: (pinId, moduleType) => getModuleConfigFromCache(pinId, moduleType),
        saveModuleConfig: (pinId, moduleType, config) => saveModuleConfigToCache(pinId, moduleType, config),
        clearModuleConfig: (pinId, moduleType) => clearModuleConfigCache(pinId, moduleType),
        savePinState: (pinId) => savePinCompleteState(pinId),
        restorePinState: (pinId) => restorePinCompleteState(pinId),
        pinConfigCache: pinConfigCache,
        moduleConfigCache: moduleConfigCache,
        pinTypeCache: pinTypeCache,
        pinCompleteStateCache: pinCompleteStateCache
      };
      console.log('🔧 调试工具已暴露到 window.debugCache');
    }

  } catch (error) {
    console.error('组件初始化错误:', error);
    messageManager.error('组件初始化失败，请刷新页面重试');
  } finally {
    loading.value = false;
  }
});

// 监控项目信息变化
watch(() => projectStore.project_info, (newval) => {
  if (newval) {
    const newProjectCode = newval.projectCode || '';
    const newProjectName = newval.name || '';

    // 只有当项目代码真正发生变化时才重新获取仓库信息
    if (newProjectCode !== project_code) {
      console.log('项目切换:', project_code, '->', newProjectCode);

      project_code = newProjectCode;
      project_name = newProjectName;

      // 检查项目是否选择
      if (project_code=="" && project_name=="") {
        showTipDialog.value = true;
      } else {
        showTipDialog.value = false;
        // 清空全局状态和本地状态
        projectStore.clearCodeManagement();
        form.gitlab = '';
        form.project_branch = '';
        spaceOptions.value = [];
        branchOptions.value = [];
        get_space();
      }
    }
  }
});

// 监控仓库信息变化
watch(() => form.gitlab, (newval, oldval) => {
  if (newval !== oldval) {
    // 同步更新全局状态
    projectStore.updateCodeManagement({ gitlab: newval });
    form.project_branch = '';
    get_branch();
  }
});

// 监控分支信息变化
watch(() => form.project_branch, (newval, oldval) => {
  if (newval !== oldval) {
    // 同步更新全局状态
    projectStore.updateCodeManagement({ project_branch: newval });
    submit_branch_info();
  }
});


// 清理监听器
onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
});


// 获取仓库列表
const get_space = async () => {
  try {
    loading.value = true;
    console.log('🔄 调用get_space - 开始获取仓库信息，项目代码:', project_code);
    console.trace('get_space调用堆栈:'); // 添加调用堆栈跟踪

    const response = await http.get('/code_management/space_options', {
      params: {
        project_code: project_code
      }
    });

    console.log("获取已创建的仓库信息:", response.data);

    if (response.data.status === 1) {
      spaceOptions.value = response.data.space_options || [];
      console.log('仓库列表:', spaceOptions.value);

      // 同步更新全局状态
      projectStore.setSpaceOptions(spaceOptions.value);

      // 设置默认仓库为第一个
      if (spaceOptions.value.length > 0 && !form.gitlab) {
        form.gitlab = spaceOptions.value[0];
        console.log('设置默认仓库:', form.gitlab);
        // 注意：这里不需要手动调用get_branch()，因为watch监听器会自动触发
      } else if (spaceOptions.value.length === 0) {
        messageManager.warning('该项目暂无可用仓库');
        showTipDialog.value = true;
      }
    } else {
      console.error('获取仓库信息失败:', response.data.message);
      messageManager.error(response.data.message || '获取仓库信息失败');
    }
  } catch (error) {
    console.error('获取仓库信息失败:', error.message);
    messageManager.error('获取仓库信息失败: ' + error.message);
  } finally {
    loading.value = false;
  }
};

// 获取分支列表
const get_branch = async () => {
  if (!form.gitlab) {
    console.warn('仓库地址为空，无法获取分支');
    return;
  }

  try {
    console.log('🔄 调用get_branch - 开始获取分支信息，仓库:', form.gitlab);
    console.trace('get_branch调用堆栈:'); // 添加调用堆栈跟踪

    const response = await http.get('/code_management/branch_options', {
      params: {
        project_code: project_code,
        project_gitlab: form.gitlab
      }
    });

    if (response.data.status === 1) {
      branchOptions.value = response.data.branch_options || [];
      console.log('分支列表:', branchOptions.value);

      // 同步更新全局状态
      projectStore.setBranchOptions(branchOptions.value);

      // 设置默认分支为第一个
      if (branchOptions.value.length > 0 && !form.project_branch) {
        form.project_branch = branchOptions.value[0];
        console.log('设置默认分支:', form.project_branch);
        // 注意：这里不需要手动调用submit_branch_info()，因为watch监听器会自动触发
      } else if (branchOptions.value.length === 0) {
        messageManager.warning('该仓库暂无可用分支');
        showTipDialog.value = true;
      }
    } else {
      console.error('获取分支信息失败:', response.data.message);
      messageManager.error(response.data.message || '获取分支信息失败');
    }
  } catch (error) {
    console.error('获取分支信息失败:', error.message);
    messageManager.error('获取分支信息失败: ' + error.message);
  }
};

// 提交分支信息并获取SDK版本
const submit_branch_info = async () => {
  if (!form.gitlab || !form.project_branch) {
    console.warn('仓库或分支信息不完整，无法提交');
    return;
  }

  try {
    loading.value = true;
    console.log('🔄 调用submit_branch_info - 提交分支信息:', {
      project_code,
      project_name,
      gitlab: form.gitlab,
      branch: form.project_branch
    });
    // console.trace('submit_branch_info调用堆栈:'); // 添加调用堆栈跟踪

    const response = await http.get('/code_management/branch_submit', {
      params: {
        project_code: project_code,
        project_name: project_name,
        project_gitlab: form.gitlab,
        project_branch: form.project_branch
      },
      timeout: 60000
    });

    console.log("分支提交响应:", response.data);
    console.log(response.data.table)
    console.log(response.data.color)
    if (response.data.config_status === 1) {
      SdkVersion.value = response.data.sdk_version;
      console.log('SDK版本:', SdkVersion.value);

      // 保存工作空间和分支状态信息
      workspace_path = response.data.work_space;
      workspace.value = response.data.work_space;
      branch_status.value = response.data.branch_status;
      console.log('工作空间:', workspace.value);
      console.log('分支状态:', branch_status.value);

      // 同步更新全局状态
      projectStore.setSdkVersion(SdkVersion.value);

      // 更新分支创建权限状态
      branch_create.value = response.data.branch_create === true || response.data.branch_create === 'true';
      console.log('分支创建权限:', branch_create.value);

      // 调用获取芯片信息接口
      get_chipinfo()

      // 可以在这里添加其他成功后的操作
      messageManager.success('项目配置加载成功');
    } else {
      const errorMsg = response.data.message || '分支提交失败';
      console.error('分支提交失败:', errorMsg);
      messageManager.error(errorMsg);
    }
  } catch (error) {
    console.error('分支提交失败:', error.message);
    messageManager.error('分支提交失败: ' + error.message);
  } finally {
    loading.value = false;
  }
};

// 获取芯片信息
const get_chipinfo = async () => {
  try {
    chipOptionsLoading.value = true;
    console.log('🔄 开始获取芯片信息');
    console.log('请求参数:', {
      project_code: project_code,
      project_name: project_name,
      project_gitlab: form.gitlab,
      project_branch: form.project_branch
    });

    const response = await http.get('/code_management/chip_info', {
      params: {
        project_code: project_code,
        project_name: project_name,
        project_gitlab: form.gitlab,
        project_branch: form.project_branch
      }
    });

    console.log("获取芯片信息完整响应:", response);
    console.log("获取芯片信息数据:", response.data);

    console.log('芯片型号:', response.data.data.chip);
    console.log('芯片引脚颜色:', response.data.data.color);
    console.log('芯片引脚模块:', response.data.data.io);
    console.log('芯片引脚配置:', response.data.data.table);


    if (response.data && response.data.status === 1) {
      chip.value = response.data.data?.chip || response.data.chip;
      console.log("提取的芯片数据:", chip.value);

    } else {
      console.error('API返回状态不正确:', response.data);
      messageManager.error('获取芯片信息失败: ' + (response.data?.message || '未知错误'));
      return;
    }

    // 处理芯片选项数据
    if (chip.value) {
      console.log('芯片数据类型:', typeof chip.value);
      console.log('芯片数据是否为数组:', Array.isArray(chip.value));

      if (Array.isArray(chip.value)) {
        // 如果是数组，转换为选项格式
        chipOptions.value = chip.value.map(item => ({
          label: item.name || item.model || item.label || item,
          value: item.model || item.value || item.name || item
        }));
        console.log('处理后的芯片选项(数组):', chipOptions.value);

        // 设置默认选中第一个芯片
        if (chipOptions.value.length > 0) {
          const firstChip = chip.value[0];
          const firstOption = chipOptions.value[0];

          // 使用选项中的 value 作为 chipModel，确保匹配
          const newChipModel = firstOption.value;
          const newVccVoltage = firstChip.vccVoltage || 3.3;
          const newClockFreq = firstChip.clockFreq || 160;

          console.log('准备设置芯片数据(数组):', {
            chipModel: newChipModel,
            vccVoltage: newVccVoltage,
            clockFreq: newClockFreq,
            firstOption: firstOption
          });

          chipForm.chipModel = newChipModel;
          chipForm.vccVoltage = newVccVoltage;
          chipForm.clockFreq = newClockFreq;
          console.log('设置后的 chipForm(数组):', chipForm);
        }
      } else if (typeof chip.value === 'object') {
        // 如果是单个对象，转换为数组格式
        const optionValue = chip.value.model || chip.value.value || chip.value.name || 'default';
        chipOptions.value = [{
          label: chip.value.name || chip.value.model || chip.value.label || 'Default Chip',
          value: optionValue
        }];
        console.log('处理后的芯片选项(对象):', chipOptions.value);

        const newVccVoltage = chip.value.vccVoltage || 3.3;
        const newClockFreq = chip.value.clockFreq || 160;

        console.log('准备设置芯片数据(对象):', {
          chipModel: optionValue,
          vccVoltage: newVccVoltage,
          clockFreq: newClockFreq
        });

        chipForm.chipModel = optionValue;
        chipForm.vccVoltage = newVccVoltage;
        chipForm.clockFreq = newClockFreq;
        console.log('设置后的 chipForm(对象):', chipForm);
      } else if (typeof chip.value === 'string') {
        // 如果是字符串，直接使用
        chipOptions.value = [{
          label: chip.value,
          value: chip.value
        }];
        console.log('处理后的芯片选项(字符串):', chipOptions.value);
        chipForm.chipModel = chip.value;
        console.log('设置芯片型号(字符串):', chipForm.chipModel);
      }
    } else {
      console.warn('芯片数据为空或未定义');
      chipOptions.value = [];
    }


    // 处理芯片预览数据
    // 1. 引脚数量使用 response.data.data.table.length
    if (response.data.data && response.data.data.table && Array.isArray(response.data.data.table)) {
      chipPreviewData.pinCount = response.data.data.table.length;
      console.log("芯片引脚的个数：", chipPreviewData.pinCount);

      // 处理引脚数据
      chipPreviewData.pins = response.data.data.table.map((pinData, index) => {
        // 引脚编号直接使用 pin_id 字段
        const pinNumber = pinData.pin_id || (index + 1);
        // 引脚名称直接使用 pin_name 字段
        const pinName = pinData.pin_name || `PIN${pinNumber}`;

        // 使用model字段作为引脚类型，如果model为空，则设置为"其他"
        const pinType = pinData.model ? pinData.model : '其他';

        // console.log(`ChipPinDiagram - 引脚 PIN${pinNumber} 类型: ${pinType}, 原始model值: ${pinData.model}`);

        return {
          pinId: pinNumber.toString(),
          pinNumber: pinNumber,
          name: pinName,
          type: pinType,
          pinType: pinType, // 同时设置pinType字段，确保兼容性
          displayType: pinType,
          originalData: pinData
        };
      });

      console.log('处理后的芯片引脚数据:', chipPreviewData.pins);

      // 同时处理PinConfigTable所需的数据
      pinTableData.value = response.data.data.table.map((pinData, index) => {
        // 引脚编号直接使用 pin_id 字段
        const pinNumber = pinData.pin_id || (index + 1);

        // 引脚名称直接使用 pin_name 字段
        const pinName = pinData.pin_name || `PIN${pinNumber}`;

        // 当引脚类型为null时，显示"未配置"
        const pinType = !pinData.model || pinData.model === 'Null' ? '未配置' : pinData.model;

        // 可选类型直接使用 module 字段，如果是数组则展示所有选项，如果是字符串则作为单个选项
        let availableTypes = [];
        if (pinData.module) {
          if (Array.isArray(pinData.module)) {
            availableTypes = pinData.module;
          } else {
            availableTypes = [pinData.module];
          }
        }

        // // 调试信息
        // console.log(`引脚 ${pinNumber} 数据处理:`, {
        //   pin_name: pinData.pin_name,
        //   module: pinData.module,
        //   processedName: pinName,
        //   processedAvailableTypes: availableTypes
        // });

        // 状态通过status字段确认是否配置
        const status = pinData.status ? '已配置' : '未配置';

        return {
          pinId: pinNumber.toString(),
          pinName: pinName,
          pinType: pinType,
          pinNumber: pinNumber,
          model: pinData.model || '',
          module: pinData.module || [],
          availableTypes: availableTypes,
          status: status,
          description: pinData.description || '',
          originalData: pinData
        };
      });

      // 对引脚配置表数据按引脚编号进行升序排列
      pinTableData.value.sort((a, b) => {
        const pinIdA = parseInt(a.pinId) || 0;
        const pinIdB = parseInt(b.pinId) || 0;
        return pinIdA - pinIdB;
      });

      console.log('处理并排序后的PinConfigTable数据:', pinTableData.value);
    } else {
      chipPreviewData.pinCount = 0;
      chipPreviewData.pins = [];
      pinTableData.value = [];
      console.warn('未找到 response.data.data.table 数据');
    }

    // 处理io数据生成availableIoTypes
    if (response.data.data.io && Array.isArray(response.data.data.io)) {
      console.log('原始IO数据:', response.data.data.io);

      // 转换IO数据为PinConfigTable组件期望的格式
      availableIoTypes.value = response.data.data.io.map(ioItem => {
        // 处理不同的数据格式
        if (typeof ioItem === 'string') {
          return {
            value: ioItem,
            label: ioItem,
            description: ''
          };
        } else if (typeof ioItem === 'object') {
          return {
            value: ioItem.value || ioItem.name || ioItem.type || ioItem.label,
            label: ioItem.label || ioItem.name || ioItem.type || ioItem.value,
            description: ioItem.description || ioItem.desc || ''
          };
        }
        return {
          value: String(ioItem),
          label: String(ioItem),
          description: ''
        };
      });

      // 初始化ioConfig为空数组，复选框全部不勾选
      ioConfig.value = [];

      console.log('处理后的IO类型数据:', availableIoTypes.value);
      console.log('初始化的IO配置:', ioConfig.value);
    } else {
      availableIoTypes.value = [];
      ioConfig.value = [];
      console.warn('未找到 response.data.data.io 数据');
    }

    // 设置芯片型号
    if (chipForm.chipModel) {
      chipPreviewData.chipModel = chipForm.chipModel;
    } else if (response.data.data && response.data.data.chip) {
      chipPreviewData.chipModel = response.data.data.chip;
    }

    // 2. 图例颜色使用 response.data.data.color 中的数据
    console.log('原始color数据:', response.data.data.color);

    if (response.data.data && response.data.data.color) {
      console.log('检测到颜色数据，类型:', typeof response.data.data.color);

      // 如果是数组格式，直接传递给 ChipPinDiagram 组件处理
      if (Array.isArray(response.data.data.color)) {
        console.log('使用数组格式的颜色配置');
        chipPreviewData.typeStyleConfig = response.data.data.color;
      } else if (typeof response.data.data.color === 'object') {
        // 如果是对象格式，也直接传递
        console.log('使用对象格式的颜色配置');
        chipPreviewData.typeStyleConfig = response.data.data.color;
      }

      console.log('最终传递的图例颜色配置:', chipPreviewData.typeStyleConfig);
    } else {
      // 使用默认颜色配置
      chipPreviewData.typeStyleConfig = {
        'VCC': { color: '#ff4757', backgroundColor: '#ffe8e8' },
        'GND': { color: '#2f3542', backgroundColor: '#e8e8e8' },
        'IO': { color: '#3742fa', backgroundColor: '#e8f0ff' },
        'CLK': { color: '#2ed573', backgroundColor: '#e8ffe8' },
        'RST': { color: '#ffa502', backgroundColor: '#fff5e8' },
        'ADC': { color: '#ff6b81', backgroundColor: '#ffe8ed' },
        'PWM': { color: '#a55eea', backgroundColor: '#f3e8ff' },
        'UART': { color: '#26de81', backgroundColor: '#e8fff3' },
        'SPI': { color: '#fd79a8', backgroundColor: '#ffe8f1' },
        'I2C': { color: '#00b894', backgroundColor: '#e8fff8' },
        'OTHER': { color: '#B0BEC5', backgroundColor: '#f5f5f5' },
        '其他': { color: '#B0BEC5', backgroundColor: '#f5f5f5' },
        '未配置': { color: '#ECEFF1', backgroundColor: '#ECEFF1' }
      };
      console.log('未找到 response.data.data.color，使用默认图例颜色配置');
    }

    // messageManager.success('芯片信息获取成功');
  } catch (error) {
    console.error('获取芯片信息失败:', error);
    messageManager.error('获取芯片信息失败: ' + error.message);
  } finally {
    chipOptionsLoading.value = false;
  }
};

// 功能按钮处理函数
// download 操作
const handleDownload = () => {
  console.log('开始下载配置');
  messageManager.success('配置下载成功');
};

// commit 操作
const handleSave = async () => {
  ElMessageBox.prompt('请输入 commit 信息', '保存配置', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    inputErrorMessage: '请输入 commit 信息',
    inputValidator: (value) => value != null && value.trim() !== ''
  }).then(({ value }) => {
    console.log('Commit信息:', value);
    messageManager.success('配置已保存');
  }).catch(() => {
    console.log('取消保存');
  });
};

// push 操作
const handlePublish = () => {
  ElMessageBox.confirm(
    '确定要发布当前配置吗？发布后将生效并覆盖现有配置。',
    '发布确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    console.log('确认发布');
    messageManager.success('配置已发布');
  }).catch(() => {
    console.log('取消发布');
  });
};

// merge 确认操作
const confirmAction = (mergeBranch) => {
  ElMessageBox.confirm(
    '确定要merge当前分支到目标分支吗？',
    '发布确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    console.log('确认合并到:', mergeBranch);
    messageManager.success('分支合并成功');
  }).catch(() => {
    console.log('取消合并');
  });
};

// 测试merge功能（由工具栏组件处理弹窗）
const mergetest = () => {
  // 弹窗逻辑现在在Toolbar组件中处理
  console.log('准备合并操作');
};

</script>

<style scoped>
/* 顶部工具栏 */


/* 顶部导航样式 */
.top-navigation {
  background: #fff;
  border-bottom: 2px solid #e4e7ed;
  padding: 0 20px;
  margin-bottom: 0;
}

.nav-tabs {
  display: flex;
  gap: 0;
}

.nav-tab {
  padding: 16px 24px;
  cursor: pointer;
  border-bottom: 3px solid transparent;
  color: #606266;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
}

.nav-tab:hover {
  color: #409eff;
  background-color: #f5f7fa;
}

.nav-tab.active {
  color: #409eff;
  border-bottom-color: #409eff;
  background-color: #ecf5ff;
}

.nav-tab i {
  font-size: 16px;
}

/* Merge弹窗样式 */
.merge-dialog {
  border-radius: 8px;
}

/* 修复弹窗定位问题 - 覆盖全局样式 */
.merge-dialog.el-dialog {
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  margin: 0 !important;
  z-index: 2000 !important;
}

/* 确保弹窗遮罩层正常显示 */
.el-overlay {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  z-index: 1999 !important;
}

.merge-dialog .el-dialog__header {
  padding: 20px 20px 10px;
  border-bottom: 1px solid #ebeef5;
}

.merge-dialog .el-dialog__body {
  padding: 20px;
}

.dialog-content {
  text-align: center;
  padding: 10px 0;
}

.dialog-icon {
  font-size: 48px;
  color: #409eff;
  margin-bottom: 16px;
}

.dialog-description {
  font-size: 16px;
  color: #606266;
  margin-bottom: 20px;
  line-height: 1.5;
}

.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 12px;
  padding: 10px 0;
}

.dialog-footer .el-button {
  min-width: 80px;
}

/* 主要内容区域布局 */
.main-content {
  display: flex;
  align-items: stretch; /* 确保子容器拉伸到相同高度 */
  gap: 20px;
  margin-top: 20px;
  height: calc(100vh - 200px); /* 根据实际需要调整 */
  /* 调试边框 */
  /* border: 2px solid red; */
}

/* 左侧容器 */
.left-container {
  flex: 1;
  height: 100%; /* 确保高度填满父容器 */
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 左侧第一行 */
.left-row-1 {
  display: flex;
  gap: 20px;
  flex: 1;
}

/* 芯片配置区域 - 占30% */
.chip-config-section {
  flex: 0 0 30%;
}

/* 芯片预览区域 - 占80% */
.other-config-section {
  flex: 1;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  height: 100%;
  overflow: hidden;
}

/* 左侧第二行 */
.left-row-2 {
  flex: 1;
  background: #f8f9fa;
  /* border: 1px solid #e9ecef; */
  border-radius: 8px;
  /* padding: 20px; */
  display: flex;
  flex-direction: column;
  height: 100%;
  min-width: 0;
  width: 100%;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

/* 右侧容器 */
.right-container {
  flex: 0 0 30%; /* 右侧容器占30%宽度 */
  width: 100%;
  height: 100%; /* 确保高度填满父容器 */
  min-height: 100%; /* 确保最小高度 */
  display: flex;
  flex-direction: column;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  background: #fff; /* 添加背景色 */
  align-self: stretch; /* 强制拉伸到父容器高度 */
}

/* 占位内容样式 */
.placeholder-content {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #6c757d;
  font-size: 16px;
}

.placeholder-content p {
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-content {
    gap: 15px;
  }

  .left-row-1 {
    flex-direction: column;
    gap: 15px;
  }

  .chip-config-section {
    width: 100% !important;
  }

  .other-config-section {
    width: 100% !important;
  }
}

@media (max-width: 768px) {
  .main-content {
    flex-direction: column;
    align-items: stretch; /* 保持拉伸对齐 */
    gap: 10px;
    height: auto;
    min-height: calc(100vh - 200px);
  }

  .left-container {
    width: 100%;
    height: auto; /* 移动端允许自动高度 */
  }

  .right-container {
    flex: none;
    width: 100%;
    height: auto; /* 移动端允许自动高度 */
  }

  .left-row-1 {
    flex-direction: column;
    gap: 10px;
  }
}

</style>