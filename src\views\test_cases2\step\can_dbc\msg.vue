<template>
    <div class="dbc-selector">
        <!-- 左侧：消息选择区域 -->
        <div class="left-panel">
            <div class="message-section">
                <div class="section-header">
                    <h4>选择消息</h4>
                    <div class="header-actions">
                        <el-input v-model="messageSearchText" placeholder="搜索消息ID或名称" style="width: 180px;" clearable
                            @input="handleMessageSearch">
                            <template #prefix>
                                <el-icon>
                                    <Search />
                                </el-icon>
                            </template>
                        </el-input>
                        <span class="count">{{ filteredMessages.length }} / {{ messages.length }}</span>
                    </div>
                </div>
                <el-table :data="filteredMessages" height="500" highlight-current-row
                    @current-change="handleMessageSelect" ref="messageTableRef" v-loading="messageSearching">

                    <el-table-column prop="frame_id" label="ID" width="80" align="center">
                        <template #default="{ row }">
                            <code
                                v-html="highlightText('0x' + row.frame_id.toString(16).toUpperCase().padStart(3, '0'), messageSearchText)"></code>
                        </template>
                    </el-table-column>

                    <el-table-column prop="name" label="消息名称" min-width="120" show-overflow-tooltip align="center">
                        <template #default="{ row }">
                            <span v-html="highlightText(row.name, messageSearchText)"></span>
                        </template>
                    </el-table-column>

                    <el-table-column prop="length" label="DLC" width="80" align="center" />
                    <!-- <el-table-column prop="comment" label="备注" min-width="100" show-overflow-tooltip>
                        <template #default="{ row }">
                            <span v-html="highlightText(row.comment || '', messageSearchText)"></span>
                        </template>
                    </el-table-column> -->
                </el-table>
            </div>
        </div>

        <!-- 右侧：信号编辑区域 -->
        <div class="right-panel">
            <div class="signal-section">
                <div class="section-header">
                    <h4>编辑信号值
                        <span v-if="selectedMessage" class="message-name">
                           - 0x{{ selectedMessage.frame_id.toString(16).toUpperCase().padStart(3, '0') }} - {{ selectedMessage.name }}
                        </span>
                    </h4>
                    <div class="header-actions">
                        <el-input v-model="signalSearchText" placeholder="搜索信号名称" style="width: 160px;" clearable
                            @input="handleSignalSearch" :disabled="!selectedMessage">
                            <template #prefix>
                                <el-icon>
                                    <Search />
                                </el-icon>
                            </template>
                        </el-input>
                        <el-button @click="resetAllValues" :disabled="!selectedMessage">
                            重置
                        </el-button>
                        <span class="count">{{ filteredSignals.length }} / {{ editableSignals.length }}</span>
                    </div>
                </div>
                <el-table :data="filteredSignals" height="500" ref="signalTableRef"
                    v-loading="!selectedMessage || signalSearching" style="width: 820px;">
                    <!-- 信号基本信息 -->
                    <el-table-column prop="name" label="信号名称" min-width="200" show-overflow-tooltip fixed align="center">
                        <template #default="{ row }">
                            <span v-html="highlightText(row.name, signalSearchText)"></span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="start" label="起始位" width="100" align="center" />
                    <el-table-column prop="length" label="长度" width="100" align="center" />

                    <!-- 信号值编辑 -->
                    <el-table-column label="实际值" width="150" align="center">
                        <template #default="{ row, $index }">
                            <el-input-number v-model="row.actualValue" :controls="false"
                                :precision="getValuePrecision(row)" :step="1" :min="row.minimum" :max="row.maximum"
                                style="width: 100%;" />
                        </template>
                    </el-table-column>

                    <!-- 原始值显示 -->
                    <el-table-column label="原始值" width="100" align="center">
                        <template #default="{ row }">
                            <el-tag type="info">
                                {{ calculateRawValue(row) }}
                            </el-tag>
                        </template>
                    </el-table-column>

                    <el-table-column label="取值说明" width="200" show-overflow-tooltip align="center">
                        <template #default="{ row }">
                            <el-text type="info">
                                {{ row.choices || '-' }}
                            </el-text>
                        </template>
                    </el-table-column>

                    <!-- 信号属性 -->
                    <!-- <el-table-column prop="scale" label="因子" width="70" align="center" />
                    <el-table-column prop="offset" label="偏移" width="70" align="center" />
                    <el-table-column prop="unit" label="单位" width="70" align="center" /> -->

                    <!-- 值范围 -->
                    <el-table-column label="范围" width="150" align="center">
                        <template #default="{ row }">
                            <el-text type="info">
                                {{ row.minimum }}~{{ row.maximum }}
                            </el-text>
                        </template>
                    </el-table-column>

                    <!-- <el-table-column prop="comment" label="备注" min-width="100" show-overflow-tooltip>
                        <template #default="{ row }">
                            <span v-html="highlightText(row.comment || '', signalSearchText)"></span>
                        </template>
                    </el-table-column> -->
                </el-table>
            </div>
        </div>
    </div>
    <!-- 操作按钮 -->
    <div class="action-section">
        <div class="buttons">
            <el-button @click="clearAllSearches" v-if="messageSearchText || signalSearchText">
                清除搜索
            </el-button>
            <el-button @click="handleCancel">取消</el-button>
            <el-button type="primary" @click="handleConfirm" :disabled="!selectedMessage">
                确认编辑
            </el-button>
        </div>
    </div>
</template>

<script setup>
import { computed, ref, watch, inject, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { Search } from '@element-plus/icons-vue';
import { isNull, debounce } from 'lodash';
import http from '@/utils/http/http.js';

const props = defineProps({
    index: {
        type: Number,
        required: true
    },
    message: {
        type: Object,
        default: () => (null)
    }
});

const dbcData = inject('dbcData');

const messageTableRef = ref(null);
const signalTableRef = ref(null);
const selectedMessage = ref(null);
const editableSignals = ref([]);
let initFlag = true;

// 搜索相关状态
const messageSearchText = ref('');
const signalSearchText = ref('');
const messageSearching = ref(false);
const signalSearching = ref(false);

const messages = computed(() => {
    return dbcData.value.content?.messages || [];
});

// 过滤后的消息列表
const filteredMessages = computed(() => {
    if (!messageSearchText.value.trim()) {
        return messages.value;
    }

    const searchText = messageSearchText.value.toLowerCase().trim();
    return messages.value.filter(message => {
        // 搜索消息名称
        const nameMatch = message.name?.toLowerCase().includes(searchText);

        // 搜索消息ID（支持十进制和十六进制）
        const frameIdStr = message.frame_id?.toString();
        const frameIdHex = '0x' + message.frame_id?.toString(16).toUpperCase();
        const frameIdHexPadded = '0x' + message.frame_id?.toString(16).toUpperCase().padStart(3, '0');

        const idMatch = frameIdStr?.includes(searchText) ||
            frameIdHex.toLowerCase().includes(searchText) ||
            frameIdHexPadded.toLowerCase().includes(searchText);

        return nameMatch || idMatch;
    });
});

// 过滤后的信号列表
const filteredSignals = computed(() => {
    if (!signalSearchText.value.trim()) {
        return editableSignals.value;
    }

    const searchText = signalSearchText.value.toLowerCase().trim();
    return editableSignals.value.filter(signal => {
        // 搜索信号名称
        const nameMatch = signal.name?.toLowerCase().includes(searchText);

        return nameMatch;
    });
});

const emits = defineEmits(['confirm', 'cancel']);

// 防抖搜索处理
const handleMessageSearch = debounce(() => {
    messageSearching.value = true;
    setTimeout(() => {
        messageSearching.value = false;
    }, 200);
}, 300);

const handleSignalSearch = debounce(() => {
    signalSearching.value = true;
    setTimeout(() => {
        signalSearching.value = false;
    }, 200);
}, 300);

// 高亮搜索文本
const highlightText = (text, searchText) => {
    if (!text || !searchText.trim()) {
        return text || '';
    }

    const regex = new RegExp(`(${searchText.trim()})`, 'gi');
    return text.replace(regex, '<mark style="background-color: #fff566; padding: 0 2px;">$1</mark>');
};

// 清除所有搜索
const clearAllSearches = () => {
    messageSearchText.value = '';
    signalSearchText.value = '';
    ElMessage.success('已清除所有搜索条件');
};

// 初始化信号编辑数据
const initEditableSignals = (signals) => {
    return signals.map(signal => ({
        ...signal,
        actualValue: signal.initial,
    }));
};

// 处理消息选择
const handleMessageSelect = (message) => {
    selectedMessage.value = message;
    // 清除信号搜索
    signalSearchText.value = '';

    if (message && message.signals) {
        if (props.message?.msg && initFlag) {
            initFlag = false;
            http.post("/can_dbc/message2signal", {
                dbc_id: dbcData.value.id,
                message: props.message,
            }).then((res) => {
                const sig_c = res.data.data;
                editableSignals.value = initEditableSignals(message.signals);
                editableSignals.value.forEach(signal => {
                    signal.actualValue = sig_c[signal.name] || signal.initial;
                });
            }).catch((error) => {
                console.error('获取信号数据时发生错误:', error);
            });
        } else {
            editableSignals.value = initEditableSignals(message.signals);
        }
    } else {
        editableSignals.value = [];
    }
};

// 计算原始值（基于因子和偏移）
const calculateRawValue = (signal) => {
    if (!signal.scale || signal.scale === 0) return signal.actualValue;
    return Math.round((signal.actualValue - signal.offset) / signal.scale);
};

// 获取值精度
const getValuePrecision = (signal) => {
    if (!signal.is_float) {
        return 0; // 整数
    } else {
        return null;
    }
};

// 重置所有值
const resetAllValues = () => {
    editableSignals.value.forEach(signal => {
        signal.actualValue = signal.initial;
    });
    ElMessage.success('已重置所有信号值');
};

// 确认编辑
const handleConfirm = () => {
    if (!selectedMessage.value) {
        ElMessage.warning('请先选择一个消息');
        return;
    }

    const result = {
        ...selectedMessage.value,
        signals: editableSignals.value.map(signal => ({
            ...signal
        }))
    };

    http.post("/can_dbc/signal2message", {
        dbc_id: dbcData.value.id,
        message: result,
    }).then((res) => {
        let message = res.data.data;
        let result = {
            ...selectedMessage.value,
            signals: null,
            msg: message,
        }
        emits('confirm', props.index, result);
    }).catch((error) => {
        console.error('信号编辑失败:', error);
    });
};

// 取消编辑
const handleCancel = () => {
    emits('cancel');
};

// 监听数据变化，默认选择第一个消息
watch(messages, (newMessages) => {
    if (newMessages.length > 0 && !selectedMessage.value) {
        nextTick(() => {
            if (isNull(props.message)) {
                if (filteredMessages.value.length > 0) {
                    messageTableRef.value?.setCurrentRow(filteredMessages.value[0]);
                }
            } else {
                const index = newMessages.findIndex(msg => msg.frame_id === props.message.frame_id);
                if (index !== -1) {
                    messageTableRef.value?.setCurrentRow(newMessages[index]);
                } else {
                    ElMessage.warning('未找到指定的消息');
                }
            }
        });
    }
}, { immediate: true });

// 监听搜索结果变化，自动选择第一个
watch(filteredMessages, (newFilteredMessages) => {
    if (messageSearchText.value && newFilteredMessages.length > 0 && !selectedMessage.value) {
        nextTick(() => {
            messageTableRef.value?.setCurrentRow(newFilteredMessages[0]);
        });
    }
});
</script>

<style lang="scss" scoped>
.dbc-selector {
    display: flex;
    height: 650px;
    gap: 16px;
    padding: 16px;
    background: #fff;
    box-sizing: border-box;
}

.left-panel {
    flex: 0 0 400px;
    display: flex;
    flex-direction: column;
}

// 右侧面板 - 信号编辑
.right-panel {
    flex: 1;
    /* 占据剩余空间 */
    display: flex;
    flex-direction: column;
}

.message-section,
.signal-section {
    display: flex;
    flex-direction: column;
    border: 1px solid #e4e7ed;
    border-radius: 6px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 14px 16px;
    /* 增加内边距 */
    background: #f5f7fa;
    border-bottom: 1px solid #e4e7ed;
    flex-shrink: 0;

    h4 {
        color: #303133;

        .message-name {
            color: #409eff;
            font-weight: normal;
        }
    }

    .header-actions {
        display: flex;
        align-items: center;
        gap: 12px;
        flex-wrap: wrap;
    }

    .count {
        color: #909399;
        background: #e4e7ed;
        padding: 4px 10px;
        /* 增加内边距 */
        border-radius: 12px;
        white-space: nowrap;
    }
}

.action-section {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 16px;
    /* 增加内边距 */
    border-top: 1px solid #e4e7ed;
    background: #f9fafc;
    flex-shrink: 0;
    height: 60px;
    /* 固定高度 */

    .selection-info {
        flex: 1;

        .search-info {
            color: #e6a23c;
            font-weight: 500;
        }
    }

    .buttons {
        display: flex;
        gap: 12px;
        flex-wrap: wrap;
    }
}

// 搜索高亮样式
:deep(mark) {
    background-color: #fff566;
    padding: 0 2px;
    border-radius: 2px;
    font-weight: 500;
}
</style>