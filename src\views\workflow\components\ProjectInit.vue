<template>
  <div class="project-init-form">
    <el-form :model="form" label-width="120px">
      
      <el-form-item label="Project Name" required>
        <el-input v-model="form.project_name" placeholder="hw-collectx" />
      </el-form-item>

      <el-form-item label="Agent" required>
        <el-select v-model="form.agent" placeholder="请选择Jenkins节点" style="width: 100%" :loading="agentLoading">
          <el-option
            v-for="node in jenkinNodes"
            :key="node.name"
            :label="node.display_name"
            :value="node.name"
            :disabled="node.status === 'offline'"
          >
            <span style="float: left">{{ node.display_name }}</span>
            <span style="float: right; color: #8492a6; font-size: 13px">
              <el-tag v-if="node.status === 'online'" type="success" size="small">在线</el-tag>
              <el-tag v-else-if="node.status === 'offline'" type="danger" size="small">离线</el-tag>
              <el-tag v-else type="warning" size="small">未知</el-tag>
            </span>
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="项目组" required style="margin-bottom: 50px;">
        <el-select v-model="form.engineering_group" placeholder="请选择工程组" style="width: 100%">
          <el-option
            label="Python组"
            value="Python"
          />
          <el-option
            label="MCU组"
            value="MCU"
          />
          <el-option
            label="VDS组"
            value="VDS"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="分支" required style="margin-bottom: 24px;">
        <el-input v-model="form.branch" placeholder="master" />
      </el-form-item>

      <el-form-item label="启用WebHook" style="margin-bottom: 24px;">
        <el-switch v-model="form.enable_webhook" />
        <span style="margin-left: 12px; color: #606266; font-size: 14px;">
          启用后，GitLab推送到此分支时将自动触发构建
        </span>
      </el-form-item>

      <el-divider content-position="left">Environment</el-divider>
      
      <el-form-item label="PROJECT_NUMBER" required>
        <el-input v-model="form.environment.PROJECT_NUMBER" placeholder="WPTSN11" />
      </el-form-item>

      <el-form-item label="PROJECT_TYPE" required>
        <el-input v-model="form.environment.PROJECT_TYPE" placeholder="Tool" />
      </el-form-item>

      <el-divider content-position="left">Parameters</el-divider>
      
      <el-table :data="form.parameters" style="width: 100%" border>
        <el-table-column prop="name" label="Name" width="140">
          <template #default="scope">
            <el-input v-model="scope.row.name" placeholder="参数名" />
          </template>
        </el-table-column>
        <el-table-column prop="default_value" label="Default Value" width="160">
          <template #default="scope">
            <el-input v-model="scope.row.default_value" placeholder="默认值" />
          </template>
        </el-table-column>
        <el-table-column prop="description" label="Description">
          <template #default="scope">
            <el-input v-model="scope.row.description" placeholder="描述" />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="80">
          <template #default="scope">
            <el-button type="danger" size="small" @click="removeParameter(scope.$index)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <el-button type="primary" plain size="small" @click="addParameter" style="margin-top: 8px;">新增参数</el-button>

      <el-divider />
      
      <el-form-item style="margin-bottom: 8px;">
        <el-button type="primary" @click="handleSave">保存配置</el-button>
        <el-button @click="$emit('cancel')">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { reactive, ref, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import http from '@/utils/http/http'

const props = defineProps({
  initialConfig: { type: Object, default: () => ({}) }
})
const emit = defineEmits(['save', 'cancel'])

// Jenkins节点相关状态
const jenkinNodes = ref([])
const agentLoading = ref(false)

// 默认表单数据
const defaultFormData = {
  project_name: 'hw-collectx',
  agent: 'slave_win10',
  engineering_group: 'Python',
  branch: 'master',
  enable_webhook: false,
  environment: {
    PROJECT_NUMBER: 'WPTSN11',
    PROJECT_TYPE: 'Tool'
  },
  parameters: []
}

// 安全地合并初始配置，深度合并防止覆盖
const mergeConfig = (defaultConfig, initialConfig) => {
  return {
    project_name: initialConfig.project_name || defaultConfig.project_name,
    agent: initialConfig.agent || defaultConfig.agent,
    engineering_group: initialConfig.engineering_group || defaultConfig.engineering_group,
    branch: initialConfig.branch || defaultConfig.branch,
    enable_webhook: initialConfig.enable_webhook !== undefined ? initialConfig.enable_webhook : defaultConfig.enable_webhook,
    environment: {
      PROJECT_NUMBER: initialConfig.environment?.PROJECT_NUMBER || defaultConfig.environment.PROJECT_NUMBER,
      PROJECT_TYPE: initialConfig.environment?.PROJECT_TYPE || defaultConfig.environment.PROJECT_TYPE
    },
    parameters: initialConfig.parameters || defaultConfig.parameters
  }
}

// 安全地合并初始配置
const form = reactive(mergeConfig(defaultFormData, props.initialConfig))

// 获取Jenkins节点列表
const fetchJenkinsNodes = async () => {
  agentLoading.value = true
  try {
    const response = await http.get('/auto_jenkins/jenkins/nodes/')
    if (response.data.success) {
      jenkinNodes.value = response.data.data
      // 如果当前选择的agent不在列表中，且列表有可用节点，自动选择第一个在线节点
      const currentAgent = form.agent
      const validAgents = jenkinNodes.value.filter(node => node.status === 'online')
      if (!jenkinNodes.value.find(node => node.name === currentAgent) && validAgents.length > 0) {
        form.agent = validAgents[0].name
      }
    } else {
      ElMessage.error('获取Jenkins节点失败: ' + response.data.message)
      // 失败时添加默认节点，保证功能可用
      jenkinNodes.value = [{ name: 'slave_win10', display_name: 'slave_win10', status: 'unknown' }]
    }
  } catch (error) {
    console.error('获取Jenkins节点失败:', error)
    ElMessage.error('获取Jenkins节点失败')
    // 失败时添加默认节点，保证功能可用
    jenkinNodes.value = [{ name: 'slave_win10', display_name: 'slave_win10', status: 'unknown' }]
  } finally {
    agentLoading.value = false
  }
}

// 组件挂载时获取节点列表
onMounted(() => {
  fetchJenkinsNodes()
})

// 监听 props 变化，更新表单数据
watch(() => props.initialConfig, (newConfig) => {
  const mergedConfig = mergeConfig(defaultFormData, newConfig)
  Object.assign(form, mergedConfig)
}, { deep: true, immediate: false })

// 添加参数
const addParameter = () => {
  form.parameters.push({
    name: '',
    default_value: '',
    description: ''
  })
}

// 删除参数
const removeParameter = (index) => {
  form.parameters.splice(index, 1)
}

// 保存配置
const handleSave = () => {
  // 验证必填项
  if (!form.project_name.trim()) {
    ElMessage.error('请填写项目名称')
    return
  }
  
  if (!form.agent.trim()) {
    ElMessage.error('请填写Agent')
    return
  }
  
  if (!form.engineering_group.trim()) {
    ElMessage.error('请选择工程组')
    return
  }
  
  if (!form.branch.trim()) {
    ElMessage.error('请填写分支名称')
    return
  }
  
  if (!form.environment.PROJECT_NUMBER.trim()) {
    ElMessage.error('请填写PROJECT_NUMBER')
    return
  }
  
  if (!form.environment.PROJECT_TYPE.trim()) {
    ElMessage.error('请填写PROJECT_TYPE')
    return
  }
  
  emit('save', form)
}
</script>

<style scoped>
.project-init-form {
  padding: 20px;
}
</style>