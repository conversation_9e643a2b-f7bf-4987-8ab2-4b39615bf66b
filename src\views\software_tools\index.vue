<template>
    <div class="test-activity-tabs">
            <el-tabs v-model="activeTabName" class="custom-tabs" @tab-click="handleTabClick">
            <el-tab-pane label="程序类型" name="type"></el-tab-pane>
            <el-tab-pane label="程序版本" name="version"></el-tab-pane>
            </el-tabs>
            <div v-if="activeTabName==='type'">
                <program></program>
            </div>
            <div v-if="activeTabName==='version'">   
            <versions></versions>
            </div>
    </div>
</template>

<script setup>
import { ref } from 'vue';
import program from '@/views/programs/index.vue'
import versions from '@/views/programs/versions/index.vue'
import { useRouter } from 'vue-router';

const router = useRouter();



const activeTabName = ref('type');

const handleTabClick = (tab) => {
  console.log('点击的标签：', tab.props.name);
  // 可在此处添加切换标签时的逻辑，如请求对应数据等
};
</script>

<style scoped>
 
.test-activity-tabs {
  width: 100%;
  margin: 0px 20px;
}

.custom-tabs {
    margin-bottom: 25px;
}

.el-tabs__nav {
  border-bottom: none;
}

.el-tabs__item {
  padding: 0 16px;
  font-size: 14px;
  color: #606266;
  margin-right: 24px;
}

.el-tabs__item.is-active {
  color: #409eff;
  border-bottom: 2px solid #409eff;
}
</style>