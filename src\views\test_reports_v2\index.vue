<template>
<div style="display: flex; flex-direction: column; height: calc(100vh - 198px);">
    <div class="tool-bar-container">
       
        <div style="margin-left: auto; display: flex; gap: 10px;">
            <el-tooltip class="box-item" effect="dark" content="重置已添加的筛选条件" placement="top-start">
                <el-button text bg @click="handleReset">重置</el-button>
            </el-tooltip>
            <filterButton @click="onFilterStatusChange" :count="filterCount" />
            <el-button icon="Refresh" text bg @click="handleRefresh">刷新</el-button>
        </div>
    </div>

    <div class="filter-container" v-if="showFilterContainer">
        <el-input v-model="form.name_re" placeholder="请输入计划名称" @keyup.enter="onFilter" clearable>
            <template #append>
                <el-button icon="Search" @click="onFilter"></el-button>
            </template>
        </el-input>
        <el-input v-model="form.m_version" placeholder="请输入测试版本" @keyup.enter="onFilter" clearable>
            <template #append>
                <el-button icon="Search" @click="onFilter"></el-button>
            </template>
        </el-input>
    </div>

    <el-table :data="tableData" stripe border style="width: 100%;flex: 1;" class="table-container">

    <el-table-column prop="name" label="计划名称" min-width="200" align="center"></el-table-column>
    
    <el-table-column prop="project_name" label="所属项目" min-width="300" align="center"></el-table-column>
    
    <el-table-column prop="m_version" label="测试版本" min-width="300" align="center"></el-table-column>

    <el-table-column prop="test_type_name" label="测试类型" min-width="200" align="center"></el-table-column>

    <el-table-column label="测试结果" min-width="100" align="center">
        <template #default="{ row }">
            <el-tag v-if="row.result_two == null" type="warning">未知</el-tag>
            <el-tag v-else-if="row.result_two" type="success">通过</el-tag>
            <el-tag v-else type="danger">不通过</el-tag>
        </template>
    </el-table-column>
    
    <el-table-column prop="plan_use" label="计划用途" min-width="150" align="center">
        <template #default="{ row }">
            <el-tag v-if="row.plan_use == 'FULL_FUNCTIONALITY_TEST'" type="success">全功能测试</el-tag>
            <el-tag v-else-if="row.plan_use == 'VERSION_REGRESSION_TEST'" type="success">版本回归测试</el-tag>
            <el-tag v-else-if="row.plan_use == 'SPECIFIC_VALIDATION_TEST'" type="success">专项验证测试</el-tag>
            <el-tag v-else-if="row.plan_use == 'PROBLEM_VALIDATION_TEST'" type="success">问题验证测试</el-tag>
            <el-tag v-else-if="row.plan_use == 'DURABILITY_TEST'" type="primary">耐久测试</el-tag>
            <el-tag v-else type="danger">未知</el-tag>
        </template>
    </el-table-column>
    
    <el-table-column label="操作" min-width="70" fixed="right" align="center">
        <template #default="{ row }">
            <div style="display: flex; justify-content: center; gap: 10px;">
                <el-button type="primary" size="small" @click="handleDetail(row)">详情</el-button>
            </div>
        </template>
    </el-table-column>

</el-table>

    <div class="pagination-container">
        <el-pagination :page-sizes="[1, 10, 15, 20, 25, 50, 100]" layout="prev, pager, next, jumper, total, sizes"
            v-model:current-page="form.page" v-model:page-size="form.pagesize" :total="total" background
            @change="onPageChange" />
    </div>
</div>
</template>


<script setup>
import { ref, reactive, watch, onMounted, onActivated } from 'vue';
import http from '@/utils/http/http.js';
import { useRouter, useRoute } from 'vue-router';
import { useProjectStore } from '@/stores/project.js';
import { useAccessStat } from '@/utils/accessStat';

useAccessStat('/test_reports_v2/list', '测试报告列表');

const router = useRouter();
const route = useRoute();
let projectStore = useProjectStore();
let filterCount = ref(0);
const tableData = ref([]);

let form = reactive({
    page: 1,
    pagesize: 15,
    project_number: '',
});

let total = ref(0);

let showFilterContainer = ref(false);

function onFilterStatusChange() {
    showFilterContainer.value = !showFilterContainer.value;
};

function update_table() {
    http.get('/v2/test_reports', { params: form }).then(res => {
        tableData.value = res.data.data.results;
        total.value = res.data.data.count;
    });

    filterCount.value = Object.keys(form).filter(key => !['page', 'pagesize', 'project_number'].includes(key)).reduce((count, key) => {
        if (form[key] == '' || form[key] == undefined || form[key] == null || form[key].length == 0) {
            return count;
        } else {
            return count + 1;
        }
    }, 0)
};

function handleReset() {
    form = reactive({
        page: 1,
        pagesize: 10,
        project_number: form.project_number,
    });
    
    update_table();
};

function onPageChange() {
    update_table();
};

function onFilter() {
    form.page = 1;
    form.pagesize = 10;
    update_table();
};

function handleDetail(row) {
    router.push(`/test_reports_v2/${row.id}`);
};

function handleRefresh() {
    update_table();
};

watch(() => projectStore.project_info, () => {
    form.project_number = projectStore.project_info.projectCode;
    update_table();
});

onMounted(() => {
    form.project_number = projectStore.project_info.projectCode;
    if (route.query.version_name) {
        form.m_version = route.query.version_name;
    }
    update_table();
});

onActivated(() => {
    form.project_number = projectStore.project_info.projectCode;
    if (route.query.version_name) {
        form.m_version = route.query.version_name;
    }
    update_table();
});


</script>


<style lang="scss" scoped>
.search-container {
    width: 100%;
    display: flex;
    justify-content: center;
    justify-items: center;
    height: 40px;
}

.search-input {
    width: 50%;
    max-width: 500px;
}

.search-button {
    border: 1px solid #dcdfe6;
}

.filter-container {
    width: 100%;
    display: flex;
    justify-content: flex-start;

    margin-bottom: 10px;

    .el-input,
    .el-select {
        width: 50%;
        max-width: 300px;
        margin-right: 10px;
    }
}

.tool-bar-container {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    justify-items: center;

    .el-select {
        width: 500px;
        margin-left: 10px;
    }

}

.el-aside {
    width: 200px;
    height: 100% !important;

    margin-top: 20px;
}

.el-main {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);

    margin-top: 0px;

    .table-container {
        flex: 1;
        overflow: auto;
    }
}
.pagination-container {
  margin-top: auto; /* 自动顶到底部 */
  align-self: flex-start; /* 左对齐 */
  padding-top: 15px;
  width: 100%;
}

</style>