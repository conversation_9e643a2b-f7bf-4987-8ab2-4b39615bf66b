<template>
  <div class="hwupload-form">
    <el-form :model="form" label-width="140px">
      
      <el-form-item label="启用 HWUpload" required>
        <el-switch v-model="form.enable_hwupload" />
        <span style="margin-left: 12px; color: #606266; font-size: 14px;">
          启用后，构建完成时会自动上传到产品开发平台
        </span>
      </el-form-item>

      <template v-if="form.enable_hwupload">
        <el-form-item label="发布版本" required>
          <el-input v-model="form.release_version" placeholder="例如: v1.0.0" />
          <div style="margin-top: 8px; color: #909399; font-size: 12px;">
            支持环境变量，例如: ${MCU_Folder_Name}
          </div>
        </el-form-item>

        <el-form-item label="版本用途" required>
          <el-select v-model="form.version_purpose" placeholder="请选择版本用途" style="width: 100%">
            <el-option label="正式版本" value="release" />
            <el-option label="测试版本" value="test" />
            <el-option label="内部版本" value="internal" />
            <el-option label="预发布版本" value="pre-release" />
            <el-option label="开发版本" value="development" />
          </el-select>
        </el-form-item>

        <el-form-item label="HWUpload 路径">
          <el-input v-model="form.hwupload_path" placeholder="默认: E:\jenkins\HWUpload.exe" />
          <div style="margin-top: 8px; color: #909399; font-size: 12px;">
            HWUpload.exe 的完整路径，留空使用默认路径
          </div>
        </el-form-item>

        <el-form-item label="上传目录">
          <el-input v-model="form.upload_directory" placeholder="默认: Version" />
          <div style="margin-top: 8px; color: #909399; font-size: 12px;">
            要上传的构建产物目录，相对于工作空间
          </div>
        </el-form-item>

        <el-form-item label="版本文件路径">
          <el-input v-model="form.version_file" placeholder="默认: Version\version.properties" />
          <div style="margin-top: 8px; color: #909399; font-size: 12px;">
            版本属性文件的路径，相对于工作空间
          </div>
        </el-form-item>

        <el-form-item label="等待上传完成">
          <el-switch v-model="form.wait_for_completion" />
          <span style="margin-left: 12px; color: #606266; font-size: 14px;">
            是否等待上传完成后再继续下一步
          </span>
        </el-form-item>
      </template>

      <el-divider />
      
      <el-form-item style="margin-bottom: 8px;">
        <el-button type="primary" @click="handleSave">保存配置</el-button>
        <el-button @click="$emit('cancel')">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  initialConfig: { type: Object, default: () => ({}) }
})
const emit = defineEmits(['save', 'cancel'])

// 默认表单数据
const defaultFormData = {
  enable_hwupload: true,
  release_version: '${MCU_Folder_Name}',
  version_purpose: 'release',
  hwupload_path: 'E:\\jenkins\\HWUpload.exe',
  upload_directory: 'Version',
  version_file: 'Version\\version.properties',
  wait_for_completion: false
}

// 表单数据
const form = reactive({
  ...defaultFormData,
  ...props.initialConfig
})

// 监听 props 变化，更新表单数据
watch(() => props.initialConfig, (newConfig) => {
  Object.assign(form, defaultFormData, newConfig)
}, { deep: true, immediate: false })

// 保存配置
const handleSave = () => {
  // 验证必填项
  if (form.enable_hwupload) {
    if (!form.release_version.trim()) {
      ElMessage.error('请填写发布版本')
      return
    }
    
    if (!form.version_purpose.trim()) {
      ElMessage.error('请选择版本用途')
      return
    }
  }
  
  emit('save', form)
}
</script>

<style scoped>
.hwupload-form {
  padding: 20px;
}

/* 让表单项靠左对齐 */
.hwupload-form :deep(.el-form) {
  text-align: left;
}

/* 表单项整体靠左 */
.hwupload-form :deep(.el-form-item) {
  margin-bottom: 20px;
  text-align: left;
  display: flex;
  align-items: flex-start;
}

/* 标签靠左对齐，固定宽度 */
.hwupload-form :deep(.el-form-item__label) {
  text-align: left !important;
  justify-content: flex-start !important;
  width: 140px !important;
  min-width: 140px;
  padding-right: 12px;
  font-weight: 500;
  color: #606266;
}

/* 表单内容区域靠左 */
.hwupload-form :deep(.el-form-item__content) {
  text-align: left;
  flex: 1;
  margin-left: 0 !important;
}

/* 输入框、选择器等组件宽度调整 */
.hwupload-form :deep(.el-input),
.hwupload-form :deep(.el-select) {
  width: 100%;
  max-width: 300px;
}

/* Switch组件特殊处理 */
.hwupload-form :deep(.el-switch) {
  margin-right: 12px;
}

/* 按钮组靠左 */
.hwupload-form :deep(.el-form-item:last-child) {
  margin-top: 30px;
}

.hwupload-form :deep(.el-button) {
  margin-right: 12px;
}
</style>
