<template>
    <el-collapse v-model="activeNames">
        <el-collapse-item title="基本信息" name="1">
            <el-form :model="form" label-width="auto" :rules="rules" status-icon ref="formRef">


                <el-row :gutter="20" style="max-width: 1400px;">

                    <el-col :span="12">
                        <el-form-item label="计划名称" prop="name">
                            <el-input v-model="form.name" placeholder="请输入计划名称"></el-input>
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="测试机台" prop="machine_number">
                            <el-select v-model="form.machine_number" placeholder="请选择测试机台" filterable>
                                <el-option v-for="item in machines" :label="item.label" :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>

                </el-row>

                <el-row :gutter="20" style="max-width: 1400px;">

                    <el-col :span="12">
                        <el-form-item label="执行状态" prop="exec_status">
                            <el-select v-model="form.exec_status" placeholder="请选择执行状态">
                                <el-option label="未执行" value="0"></el-option>
                                <el-option label="已执行" value="1"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="异常停止" prop="abnormal_stop">
                            <el-select v-model="form.abnormal_stop">
                                <el-option label="是" value="1"></el-option>
                                <el-option label="否" value="0"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row :gutter="20" style="max-width: 1400px;">

                    <el-col :span="12">
                        <el-form-item label="测试人员">
                            <Organizaiton v-model="form.tester_email" :cache-data="testerInitData" ref="testerRef" @change="onTesterChange" />
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="计划描述">
                            <el-input v-model="form.desc" type="textarea" :rows="3" placeholder="请输入计划描述"></el-input>
                        </el-form-item>
                    </el-col>

                </el-row>

            </el-form>
        </el-collapse-item>
        <el-collapse-item title="测试用例" name="2">
            <div>
                <div class="tool-bar-container">
                    <el-button icon="Plus" type="primary" plain @click="handleAdd">添加测试用例</el-button>
                    <el-button icon="Minus" type="primary" plain @click="handlePatchDelete">批量删除</el-button>
                </div>
                <el-table ref="tableRef" :data="testCaseData" stripe border style="width: 100%" class="table-container"
                    row-key="id">
                    <el-table-column type="selection" width="50" fixed="left" />
                    <el-table-column label="序号" width="100" align="center">
                        <template #default="{ row, $index }">
                            {{ $index + 1 }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="name" label="用例名称" width="200">
                        <template #default="{ row }">
                            <el-link type="primary" @click="onDetail(row)" :underline="false">{{ row.name }}</el-link>
                        </template>
                    </el-table-column>
                    <el-table-column prop="number" label="用例ID" width="300"></el-table-column>
                    <el-table-column label="用例状态" width="100">
                        <template #default="{ row }">
                            <el-tag v-if="row.status == 'PENDING'" type="primary">待评审</el-tag>
                            <el-tag v-else-if="row.status == 'REVIEWING'" type="warning">评审中</el-tag>
                            <el-tag v-else-if="row.status == 'APPROVED'" type="success">评审通过</el-tag>
                            <el-tag v-else-if="row.status == 'REJECTED'" type="danger">评审不通过</el-tag>
                            <el-tag v-else-if="row.status == 'DEPRECATED'" type="info">废弃</el-tag>
                            <el-tag v-else type="danger">未知</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" min-width="150" fixed="right" align="left">
                        <template #default="{ row }">
                            <el-button type="primary" size="small" @click="handleDelete(row)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </el-collapse-item>
    </el-collapse>


    <el-dialog v-if="dialogAddVisible" v-model="dialogAddVisible" title="添加测试用例" width="1000"
        :close-on-click-modal="false">
        <AddTestCase :project_number="project_number" :durability="durability == '1' ? true : false"
            @confirm="onAddConfirm" @cancel="onAddCancel" />
    </el-dialog>

    <el-drawer v-model="drawerDetailVisible" :with-header="false" size="60%" :destroy-on-close="true">
        <TestCaseDetail :id="r_id" :test_case="test_case" />
    </el-drawer>

</template>

<script setup>
import { ref, onMounted, watch, computed } from 'vue'
import AddTestCase from './addTestCase.vue';
import Sortable from 'sortablejs';
import http from '@/utils/http/http.js';
import TestCaseDetail from '@/components/test_case.vue';
import Organizaiton from '@/components/Organization/index.vue';

const activeNames = ref(['1', '2']);
const tableRef = ref(null);
const dialogAddVisible = ref(false);
const machines = ref([]);
const testCaseData = ref([]);
const formRef = ref(null);
const drawerDetailVisible = ref(false);
const r_id = ref(null);
const test_case = ref({});
const testerRef = ref(null);
const testerInitData = ref([]);

const model = defineModel();

const props = defineProps({
    initData: {
        type: Object,
        default: () => ({}),
    },
    project_number: {
        type: String,
        default: '',
    },
    durability: {
        type: String,
        default: '0',
    }
});

const project_number = computed(() => {
    return props.project_number;
});
const durability = computed(() => props.durability);

const form = ref({
    name: '',
    desc: '',
    machine_number: '',
    test_cases: [],
    exec_status: '0',
    abnormal_stop: '0',
    tester_email: null,
    tester_name: null,
});

function onTesterChange(value) {
    console.log(value);
    form.value.tester_name = value.realName || null;
}

watch(() => props.initData, (val) => {
    Object.assign(form.value, val);
    form.value.exec_status = val.exec_status ? '1' : '0';
    form.value.abnormal_stop = val.abnormal_stop ? '1' : '0';
    form.value.test_cases = val.test_cases?.map(item => item.id);
    form.value.tester_email = val.tester_email;
    form.value.tester_name = val.tester_name;
    testCaseData.value = val.test_cases || [];
    testerInitData.value = val.tester_email ? [{ realName: val.tester_name, username: val.tester_email, label: val.tester_name, value: val.tester_email }] : [];
}, { immediate: true });

const rules = ref({
    name: [
        { required: true, message: '请输入计划名称', trigger: 'blur' }
    ],
    project_number: [
        { required: true, message: '请选择所属项目', trigger: 'change' }
    ],
    durability: [
        { required: true, message: '请选择是否是耐久测试', trigger: 'change' }
    ],
    machine_number: [
        { required: true, message: '请选择测试机台', trigger: 'change' }
    ],
    exec_status: [
        { required: true, message: '请选择执行状态', trigger: 'change' }
    ],
    abnormal_stop: [
        { required: true, message: '请选择是否异常停止', trigger: 'change' }
    ],
})

const handleAdd = () => {
    dialogAddVisible.value = true;
}


const onAddConfirm = (data) => {
    dialogAddVisible.value = false;

    const newItems = data.filter(item => testCaseData.value.findIndex(i => i.id === item.id) === -1);
    testCaseData.value = testCaseData.value.concat(newItems);
}

const onAddCancel = () => {
    dialogAddVisible.value = false;
}

const handlePatchDelete = () => {
    const selectedRows = tableRef.value.getSelectionRows();
    testCaseData.value = testCaseData.value.filter(item => !selectedRows.includes(item));
}

const handleDelete = (row) => {
    testCaseData.value = testCaseData.value.filter(item => item !== row);
}

watch(testCaseData, () => {
    form.value.test_cases = testCaseData.value.map(item => item.id);
}, { deep: true })

watch(form, () => {
    model.value = form.value;
}, { immediate: true });

const validate = (callback) => {
    formRef.value?.validate(callback);
};

function onDetail(row) {
    r_id.value = row.id;
    drawerDetailVisible.value = true;
};

defineExpose({
    validate,
});

onMounted(() => {

    const sortable = new Sortable(tableRef.value.$el.querySelector('tbody'), {
        handle: '.el-table__row',
        animation: 150,
        onEnd: ({ newIndex, oldIndex }) => {
            const movedItem = testCaseData.value.splice(oldIndex, 1)[0];
            testCaseData.value.splice(newIndex, 0, movedItem);
        },
    });

    http.get('/machines', { params: { pagesize: 10000 } }).then(res => {
        let data = res.data.data.results;
        machines.value = data.map(item => {
            return { label: item.name + "(" + item.m_number + ")", value: item.m_number };
        });
    });

})


</script>

<style scoped>
.table-container {
    height: 300px;
}

:deep(.el-collapse-item__arrow) {
    order: -1;
    margin: 0 8px 0 0;
}

:deep(.el-collapse-item__header) {
    font-size: 16px;
}
</style>