import { createRouter, createWebHistory } from 'vue-router'
import Layout from '@/layouts/default/index.vue'
import { useNavigationStore } from '@/stores/navigation'

let routes = [
  {
    path: '/',
    name: 'Root',
    component: Layout,
    redirect: '/workbench',
  },
  {
    path: '/machine_calendar',
    name: 'MachineCalendar',
    component: Layout,
    redirect: '/machine_calendar/welcome',
    children: [
      {
        path: 'welcome',
        name: 'Welcome',
        component: () => import('@/views/welcome/index.vue'),
      },
    ]
  },
  {
    path: '/test_cases',
    name: 'TestCase',
    component: Layout,
    redirect: '/test_cases/list',
    children: [
      {
        path: 'list',
        name: 'TestCaseList',
        meta: { keepAlive: true },
        component: () => import('@/views/test_cases/index.vue'),
      },
      {
        path: 'detail/:id',
        name: 'TestCaseDetail',
        component: () => import('@/views/test_cases/detail.vue'),
      },
      {
        path: 'types',
        name: 'TestCaseType',
        component: () => import('@/views/test_cases/types/index.vue'),
        meta: { keepAlive: true }
      },
      {
        path: 'status',
        name: 'TestCaseStatus',
        component: () => import('@/views/test_cases/status/index.vue'),
        meta: { keepAlive: true }
      },
    ]
  },
  {
    path: '/es_test_cases',
    name: 'ESTestCase',
    component: Layout,
    redirect: '/es_test_cases/nio_list',
    children: [
      {
        path: 'nio_list',
        name: 'ESTestCaseNIOList',
        meta: { keepAlive: true },
        component: () => import('@/views/es_test_cases/nio/index.vue'),
      },
      {
        path: 'voyah_list',
        name: 'ESTestCaseVoyahList',
        meta: { keepAlive: true },
        component: () => import('@/views/es_test_cases/voyah/index.vue'),
      },
    ]
  },
  {
    path: '/public_test_cases',
    name: 'PublicTestCase',
    component: Layout,
    redirect: '/public_test_cases/list',
    children: [
      {
        path: 'list',
        name: 'PublicTestCaseList',
        component: () => import('@/views/public_test_cases/index.vue'),
        meta: { keepAlive: true }
      },
    ]
  },
  {
    path: '/test_cases2',
    name: 'TestCase2',
    component: Layout,
    redirect: '/test_cases2/list',
    children: [
      {
        path: 'list',
        name: 'TestCaseList2',
        component: () => import('@/views/test_cases2/index.vue'),
        meta: { keepAlive: true }
      },
      {
        path: 'add',
        name: 'TestCaseAdd2',
        component: () => import('@/views/test_cases2/add.vue'),
      }
    ]
  },
  {
    path: '/test_plans',
    name: 'TestPlan',
    component: Layout,
    redirect: '/test_plans/list',
    children: [
      {
        path: 'list',
        name: 'TestPlanList',
        component: () => import('@/views/test_plans/index.vue'),
        meta: { keepAlive: true }
      },
      {
        path: 'add',
        name: 'TestPlanAdd',
        component: () => import('@/views/test_plans/add.vue'),
      },
    ]
  },
  {
    path: '/workbench',
    name: 'Workbench',
    component: Layout,
    redirect: '/workbench/person',
    children: [
      {
        path: 'person',
        name: 'WorkbenchPerson',
        component: () => import('@/views/workbench/person.vue')
      },
      {
        path: 'team',
        name: 'WorkbenchTeam',
        component: () => import('@/views/workbench/team.vue')
      },
    ]
  },
  {
    path: '/test_plans_v2',
    name: 'TestPlanV2',
    component: Layout,
    redirect: '/test_plans_v2/list',
    children: [
      {
        path: 'list',
        name: 'TestPlanListV2',
        component: () => import('@/views/test_plans_v2/index.vue'),
        meta: { keepAlive: true }
      },
      {
        path: 'add',
        name: 'TestPlanAddV2',
        component: () => import('@/views/test_plans_v2/add.vue'),
      },
    ]
  },
  {
    path: '/public/test_plans_v2',
    name: 'PublicTestPlanV2',
    redirect: '/public/test_plans_v2/list',
    children: [
      {
        path: 'list',
        name: 'PublicTestPlanListV2',
        component: () => import('@/views/test_plans_v2/index.vue'),
        meta: { keepAlive: true }
      },
      {
        path: 'add',
        name: 'PublicTestPlanAddV2',
        component: () => import('@/views/test_plans_v2/add.vue'),
      },
    ]
  },
  {
    path: '/test_records',
    name: 'TestRecord',
    component: Layout,
    redirect: '/test_records/list',
    children: [
      {
        path: 'list',
        name: 'TestRecordList',
        component: () => import('@/views/test_records/index.vue'),
        meta: { keepAlive: true }
      },
      {
        path: 'items',
        name: 'TestRecordItems',
        component: () => import('@/views/test_records/test_record_items/index.vue'),
        meta: { keepAlive: true }
      },
      {
        path: 'items/:id',
        name: 'TestRecordItemDetail',
        component: () => import('@/views/test_records/test_record_items/detail.vue'),
      }
    ]
  },
  {
    path: '/test_records_v2',
    name: 'TestRecordV2',
    component: Layout,
    redirect: '/test_records_v2/list',
    meta: { keepAlive: true },
    children: [
      {
        path: 'list',
        name: 'TestRecordListV2',
        component: () => import('@/views/test_records_v2/index.vue'),
        meta: { keepAlive: true }
      },
      {
        path: 'items',
        name: 'TestRecordItemsV2',
        component: () => import('@/views/test_records_v2/test_record_items/index.vue'),
        meta: { keepAlive: true }
      },
      {
        path: 'items/:id',
        name: 'TestRecordItemDetailV2',
        component: () => import('@/views/test_records_v2/test_record_items/detail.vue'),
      }
    ]
  },
  {
    path: '/product_versions',
    name: 'ProductVersions',
    component: Layout,
    redirect: '/product_versions/list',
    children: [
      {
        path: 'list',
        name: 'ProductVersionList',
        component: () => import('@/views/product_versions/index.vue'),
        meta: { keepAlive: true }
      }
    ]
  },
  {
    path: '/process_monitor',
    name: 'ProcessMonitor',
    component: Layout,
    redirect: '/process_monitor/list',
    children: [
      {
        path: 'list',
        name: 'ProcessMonitorList',
        component: () => import('@/views/process_monitor/index.vue'),
        meta: { keepAlive: false }
      }
    ]
  },
  {
    path: '/am_devices',
    name: 'AMDevice',
    component: Layout,
    redirect: '/am_devices/list',
    children: [
      {
        path: 'list',
        name: 'AMDeviceList',
        component: () => import('@/views/am_devices/index.vue'),
        meta: { notRequireAuth: true, keepAlive: false }
      },
      {
        path: ':id',
        name: 'AMDeviceDetail',
        component: () => import('@/views/am_devices/detail.vue'),
        meta: { notRequireAuth: true, keepAlive: false }
      }
    ]
  },
  {
    path: '/am_stations',
    name: 'AMStation',
    component: Layout,
    redirect: '/am_stations/list',
    children: [
      {
        path: 'list',
        name: 'AMStationList',
        component: () => import('@/views/am_stations/index.vue'),
        meta: { notRequireAuth: true, keepAlive: false }
      }
    ]
  },
  {
    path: '/inspection_items',
    name: 'InspectionItem',
    component: Layout,
    redirect: '/inspection_items/list',
    children: [
      {
        path: 'list',
        name: 'InspectionItemList',
        component: () => import('@/views/inspection-items/index.vue'),
        meta: { keepAlive: false }
      }
    ]
  },
  {
    path: '/custom_cmds',
    name: 'CustomCmds',
    component: Layout,
    redirect: '/custom_cmds/list',
    children: [
      {
        path: 'list',
        name: 'CustomCmdList',
        component: () => import('@/views/custom_cmds/index.vue')
      }
    ]
  },
  {
    path: '/pack_tools/records',
    name: 'PackToolRecord',
    component: Layout,
    redirect: '/pack_tools/records/list',
    children: [
      {
        path: 'list',
        name: 'PackToolRecordList',
        component: () => import('@/views/pack_tools/records/index.vue'),
        meta: { keepAlive: false }
      }
    ]
  },
  {
    path: '/software_tools',
    name: 'SoftwareTool',
    component: Layout,
    redirect: '/software_tools/diff_tool_records',
    children: [
      {
        path: 'diff_tool_records',
        name: 'DiffToolRecord',
        component: () => import('@/views/software_tools/diff_tools/index.vue'),
        meta: { keepAlive: false }
      }
    ]
  },
  {
    path: '/test_reports',
    name: 'TestReport',
    component: Layout,
    redirect: '/test_reports/list',
    children: [
      {
        path: 'list',
        name: 'TestReportList',
        component: () => import('@/views/test_reports/index.vue'),
        meta: { keepAlive: false }
      },
      {
        path: ':id',
        name: 'TestReportDetail',
        component: () => import('@/views/test_reports/detail.vue'),
      }
    ]
  },
  {
    path: '/test_reports_v2',
    name: 'TestReportV2',
    component: Layout,
    redirect: '/test_reports_v2/list',
    children: [
      {
        path: 'list',
        name: 'TestReportListV2',
        component: () => import('@/views/test_reports_v2/index.vue'),
        meta: { keepAlive: false }
      },
      {
        path: ':id',
        name: 'TestReportDetailV2',
        component: () => import('@/views/test_reports_v2/detail.vue'),
      }
    ]
  },
  {
    path: '/requirements',
    name: 'Requirement',
    component: Layout,
    redirect: '/requirements/list',
    children: [
      {
        path: 'list',
        name: 'RequirementList',
        component: () => import('@/views/requirements/index.vue'),
        meta: { keepAlive: true }
      },
      {
        path: ':id',
        name: 'RequirementDetail',
        component: () => import('@/views/requirements/detail.vue'),
      }
    ]
  },
  {
    path: '/programs',
    name: 'Program',
    component: Layout,
    redirect: '/programs/list',
    children: [
      {
        path: 'list',
        name: 'ProgramList',
        component: () => import('@/views/programs/index.vue'),
        meta: { keepAlive: true }
      },
    ]
  },
  {
    path: '/programs/versions',
    name: 'ProgramVersion',
    component: Layout,
    redirect: '/programs/versions/list',
    children: [
      {
        path: 'list',
        name: 'ProgramVersionList',
        component: () => import('@/views/programs/versions/index.vue'),
        meta: { keepAlive: true }
      },
    ]
  },
  {
    path: '/devices',
    name: 'Device',
    component: Layout,
    redirect: '/devices/list',
    children: [
      {
        path: 'list',
        name: 'DeviceList',
        component: () => import('@/views/device/index.vue'),
        meta: { keepAlive: true }
      },
    ]
  },
  {
    path: '/device_types',
    name: 'DeviceType',
    component: Layout,
    redirect: '/device_types/list',
    children: [
      {
        path: 'list',
        name: 'DeviceTypeList',
        component: () => import('@/views/deviceType/index.vue'),
        meta: { keepAlive: true }
      },
    ]
  },
  {
    path: '/test_case_tags',
    name: 'TestCaseTag',
    component: Layout,
    redirect: '/test_case_tags/list',
    children: [
      {
        path: 'list',
        name: 'TestCaseTagList',
        component: () => import('@/views/test_case_tags/index.vue'),
        meta: { keepAlive: true }
      },
    ]
  },
  {
    path: '/testers',
    name: 'Tester',
    component: Layout,
    redirect: '/testers/list',
    children: [
      {
        path: 'list',
        name: 'TesterList',
        component: () => import('@/views/testers/index.vue'),
        meta: { keepAlive: true }
      },
      {
        path: ':id/grade',
        name: 'TesterGrade',
        component: () => import('@/views/testers/grade.vue')
      },
    ]
  },
  // {
  //   path: '/test_case_tags',
  //   name: 'TestCaseTag',
  //   component: Layout,
  //   redirect: '/test_case_tags/list',
  //   children: [
  //     {
  //       path: 'list',
  //       name: 'Skilltypes',
  //       component: () => import('@/views/testers/skill_types/index.vue'),
  //       meta: { keepAlive: true }
  //     },
  //   ]
  // },
  {
    path: '/tester_roles',
    name: 'TesterRole',
    component: Layout,
    redirect: '/tester_roles/list',
    children: [
      {
        path: 'list',
        name: 'TesterRoleList',
        component: () => import('@/views/testers/roles/index.vue'),
        meta: { keepAlive: true }
      },
      {
        path: ':id/skills',
        name: 'TesterRoleSkills',
        component: () => import('@/views/testers/roles/skillConf.vue')
      },
    ]
  },
  {
    path: '/tester_skills',
    name: 'TesterSkill',
    component: Layout,
    redirect: '/tester_skills/list',
    children: [
      {
        path: 'list',
        name: 'TesterSkillList',
        component: () => import('@/views/testers/skills/index.vue'),
        meta: { keepAlive: true }
      },
    ]
  },
  {
    path: '/tester_skill_types',
    name: 'TesterSkillType',
    component: Layout,
    redirect: '/tester_skill_types/list',
    children: [
      {
        path: 'list',
        name: 'TesterSkillTypeList',
        component: () => import('@/views/testers/skill_types/index.vue'),
        meta: { keepAlive: true }
      },
    ]
  },
  {
    path: '/issues',
    name: 'Issue',
    component: Layout,
    redirect: '/issues/list',
    children: [
      {
        path: 'list',
        name: 'IssueList',
        component: () => import('@/views/issue/index.vue'),
        meta: { keepAlive: true }
      },
    ]
  },
  {
    path: '/projects',
    name: 'Project',
    component: Layout,
    redirect: '/projects/detail',
    children: [
      {
        path: 'list',
        name: 'ProjectList',
        component: () => import('@/views/project/index.vue'),
        meta: { keepAlive: true }
      },
      {
        path: 'list2',
        name: 'ProjectList2',
        component: () => import('@/views/project2/index.vue'),
        meta: { keepAlive: true }
      },
      {
        path: 'detail',
        name: 'ProjectDetail',
        component: () => import('@/views/project2/detail.vue'),
      }
    ]
  },
  {
    path: '/test_products',
    name: 'TestProduct',
    component: Layout,
    redirect: '/test_products/list',
    children: [
      {
        path: 'list',
        name: 'TestProductList',
        component: () => import('@/views/test_products/index.vue'),
        meta: { keepAlive: true }
      }
    ]
  },
  {
    path: '/projects_repository',
    name: 'ProjectRepository',
    component: Layout,
    redirect: '/projects_repository/project_index',
    children: [
      {
        path: 'list',
        name: 'ProjectList',
        component: () => import('@/views/project_repository/index.vue'),
        meta: { keepAlive: true }
      },
      {
        path: 'project_index',
        name: 'ProjectIndex',
        component: () => import('@/views/project_repository/project_index.vue'),
      },
      {
        path: 'sdk',
        name: 'sdkIndex',
        component: () => import('@/views/project_repository/project_sdk.vue'),
      }
    ]
  },
  {
    path: '/functions',
    name: 'Function',
    component: Layout,
    redirect: '/functions/list',
    children: [
      {
        path: 'list',
        name: 'FunctionList',
        component: () => import('@/views/function/index.vue'),
        meta: { keepAlive: true }
      },
    ]
  },
  {
    path: '/stress_tests',
    name: 'Stress_test',
    component: Layout,
    redirect: '/stress_tests/list',
    children: [
      {
        path: 'list',
        name: 'Stress_testList',
        component: () => import('@/views/stress_test/index.vue'),
        meta: { keepAlive: true }
      },
    ]
  },
  {
    path: '/functions2',
    name: 'Function2',
    component: Layout,
    redirect: '/functions2/list',
    children: [
      {
        path: 'list',
        name: 'Function2List',
        component: () => import('@/views/function2/index.vue'),
        meta: { keepAlive: true }
      },
      {
        path: 'list_view',
        name: 'Function2ListView',
        component: () => import('@/views/function2/list_view.vue'),
        meta: { keepAlive: true }
      }
    ]
  },
  {
    path: '/machines',
    name: 'Machine',
    component: Layout,
    redirect: '/machines/list',
    children: [
      {
        path: 'list',
        name: 'MachineList',
        component: () => import('@/views/machine/index.vue'),
        meta: { keepAlive: true }
      },
      {
        path: ':id',
        name: 'MachineDetail',
        component: () => import('@/views/machine/detail.vue'),
      },
      {
        path: 'reservations',
        name: 'MachineReservation',
        component: () => import('@/views/machine/reservation/index.vue'),
        meta: { keepAlive: true }
      },
      {
        path: 'usage_records',
        name: 'MachineUsageRecord',
        component: () => import('@/views/machine/usageRecord/index.vue'),
      },
    ]
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/login/index.vue'),
    meta: { notRequireAuth: true },
  },
  {
    path: '/diff_packages/:token',
    name: 'DiffPackage',
    component: () => import('@/views/software_tools/diff_tools/diff_package.vue'),
    meta: { notRequireAuth: true },
  },
  {
    path: '/pack_packages/:token',
    name: 'Package',
    component: () => import('@/views/pack_tools/records/pack_package.vue'),
    meta: { notRequireAuth: true },
  },
  {
    path: '/proxy_login',
    name: 'proxyLogin',
    component: () => import('@/views/proxyLogin/login.vue'),
    meta: { notRequireAuth: true },
  },
  {
    path: '/test',
    name: 'Test',
    component: Layout,
    redirect: '/test/index',
    children: [
      {
        path: 'index',
        name: 'TestIndex',
        component: () => import('@/views/test.vue'),
      },
    ]
  },
  {
    path: '/to_do',
    name: 'ToDo',
    component: Layout,
    redirect: '/to_do/index',
    children: [
      {
        path: 'index',
        name: 'ToDoIndex',
        component: () => import('@/views/toDo.vue'),
      },
    ]
  },
  {
    path: '/product_types',
    name: 'product_types',
    component: Layout,
    redirect: '/product_types/index',
    children: [
      {
        path: 'index',
        name: 'ProductTypeIndex',
        component: () => import('@/views/product_types/index.vue'),
      },
    ]
  },

  {
    path: '/image_resources',
    name: 'image_resources',
    component: Layout,
    redirect: '/image_resources/index',
    children: [
      {
        path: 'index',
        name: 'ImageResourceIndex',
        component: () => import('@/views/image_resources/index.vue'),
      },
    ]
  },
  {
    path: '/test_prototypes',
    name: 'test_prototypes',
    component: Layout,
    redirect: '/test_prototypes/index',
    children: [
      {
        path: 'index',
        name: 'testprototypesIndex',
        component: () => import('@/views/test_prototypes/index.vue'),
      },
    ]
  },
  {
    path:'/cicd',
    name:'cicd',
    component:Layout,
    redirect:'/cicd/index',
    children:[
      {
        path:'index',
        name:'CICDIndex',
        component:() => import('@/views/cicd/index.vue'),
      },
      {
        path:'records',
        name:'CICDRecords',
        component:() => import('@/views/cicd/records/index.vue'),
      },
    ]
  },

  {
    path:'/code_management',
    name:'code_management',
    component:Layout,
    redirect:'/code_management/chip',
    children:[
      {
        path:'chip',
        name:'codeManagementGpio',
        // component:() => import('@/views/code_management/gpio/index.vue'),
         component:() => import('@/views/code_management/chip/index.vue'),
      },
      {
        path:'config',
        name:'codeManagementConfig',
        component:() => import('@/views/code_management/config/index.vue'),
      }
    ]
  },
  {
    path:'/test_submission',
    name:'test_submission',
    component:Layout,
    redirect:'/test_submission/index',
    children:[
      {
        path:'index',
        name:'TestSubmissionIndex',
        component:() => import('@/views/test_submission/index.vue'),
      },
    ]
  },
  {
    path: '/workflow',
    name: 'Workflow',
    component: Layout,
    redirect: '/workflow/editor',
    children: [
      {
        path: 'editor',
        name: 'WorkflowEditor',
        component: () => import('@/views/workflow/index.vue'),
        meta: { keepAlive: true }
      }
    ]
  }

];

if (import.meta.env.MODE === 'production') {
  routes.push({
    path: '/login',
    name: 'Login',
    component: () => import('@/views/proxyLogin/index.vue'),
    meta: { notRequireAuth: true },
  });
}

routes.push({
  path: '/:catchAll(.*)',
  name: 'NotFound',
  redirect: '/',
  meta: { notRequireAuth: true },
});

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
})

router.beforeEach((to, from, next) => {

  const navigationStore = useNavigationStore()
  
  // 1. 当离开代码管理模块的子路由时，保存当前子模块
  if (from.name && from.name.startsWith('code_')) {
    navigationStore.setActiveCodeModule(from.name)
    console.log('保存活跃代码模块:', from.name)
  } else if (from.name && from.name === 'code_management') {
    // 如果直接从代码管理模块根路由离开，清除活跃状态
    navigationStore.clearActiveCodeModule()
  }
  
  // 2. 如果是访问代码管理模块根路由，且有保存的子模块，跳转到对应子模块
  if (to.name === 'code_management' && navigationStore.activeCodeModule) {
    console.log('重定向到保存的代码子模块:', navigationStore.activeCodeModule)
    next({ name: navigationStore.activeCodeModule })
    return
  }
  
  // 3. 处理登录验证逻辑
  const notRequireAuth = to.matched.some(record => record.meta.notRequireAuth);
  const token = sessionStorage.getItem('token');

  if (!notRequireAuth && !token) {
    localStorage.setItem('redirectUrl', to.fullPath);
    next('/login');
  } else {
    next();
  }
});

export default router
