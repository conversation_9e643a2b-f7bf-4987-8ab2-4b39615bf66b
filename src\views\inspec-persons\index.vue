<template>
    <div style="display: flex; flex-direction: column; height: calc(100vh - 200px); margin-right: 20px;">

        <div class="tool-bar-container">
            <el-button icon="Plus" type="primary" @click="handleAdd">新增</el-button>
            <div style="margin-left: auto; display: flex; gap: 10px;">
                <el-tooltip class="box-item" effect="dark" content="重置已添加的筛选条件" placement="top-start">
                    <el-button text bg @click="handleReset">重置</el-button>
                </el-tooltip>
                <filterButton @click="onFilterStatusChange" :count="filterCount" />
                <el-button icon="Refresh" text bg @click="handleRefresh">刷新</el-button>
            </div>
        </div>

        <div class="filter-container" v-if="showFilterContainer">
            <el-input v-model="form.user_name" placeholder="请输入姓名" @keyup.enter="onFilter" clearable>
                <template #append>
                    <el-button icon="Search" @click="onFilter"></el-button>
                </template>
            </el-input>
            <el-select v-model="form.status" placeholder="请选择状态" clearable @change="onFilter">
                <el-option label="在岗" :value="true"></el-option>
                <el-option label="不在岗" :value="false"></el-option>
            </el-select>
        </div>

        <el-table :data="tableData" stripe border row-key="id" style="width: 100%; table-layout: fixed; flex: 1; overflow: auto;">

            <el-table-column prop="user_name" label="姓名" min-width="200" align="center"></el-table-column>
            <el-table-column label="状态" min-width="200" align="center">
                <template #default="{ row }">
                    <el-tag :type="row.is_available ? 'success' : 'danger'" size="small">
                        {{ row.is_available ? '在岗' : '不在岗' }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="操作" min-width="290" fixed="right" align="center">
                <template #default="{ row }">
                    <div style="display: flex; justify-content: center; gap: 10px;">
                        <el-button type="primary" size="small" @click="handleEdit(row)">编辑</el-button>
                        <el-button type="danger" size="small" @click="handleDelete(row)">删除</el-button>
                    </div>
                </template>
            </el-table-column>

        </el-table>

        <div class="pagination-container">
            <el-pagination :page-sizes="[1, 10, 15, 20, 25, 50, 100]" layout="prev, pager, next, jumper, total, sizes"
                :total="total" v-model:current-page="form.page" v-model:page-size="form.pagesize" background
                @change="onPageChange" />
        </div>

        <el-dialog v-if="dialogAddVisible" v-model="dialogAddVisible" title="添加轮班人员" width="800"
            :close-on-click-modal="false">
            <Add @confirm="onAddConfirm" @cancel="onAddCancel" :parent="parent" />
        </el-dialog>

        <el-dialog v-if="dialogEditVisible" v-model="dialogEditVisible" title="编辑轮班人员" width="800"
            :close-on-click-modal="false">
            <Edit @confirm="onEditAffirm" @cancel="onEditCancel" :r_id="r_id" />
        </el-dialog>
    </div>

</template>


<script setup>
import { ref, reactive, onMounted } from 'vue';
import http from '@/utils/http/http.js';
import Add from './add.vue';
import Edit from './edit.vue';

const tableData = ref([]);
const total = ref(0);
const filterCount = ref(0);

let r_id = ref(0);

const dialogAddVisible = ref(false);

const dialogEditVisible = ref(false);

const parent = ref(null);

let form = reactive({
    page: 1,
    pagesize: 10,
});

let showFilterContainer = ref(false);

function update_table() {
    http.get('/inspecs/persons', { params: form }).then(res => {
        let items = res.data.data.results;
        tableData.value = items;
        total.value = res.data.data.count;
    });

    filterCount.value = Object.keys(form).filter(key => !['page', 'pagesize'].includes(key)).reduce((count, key) => {
        if (form[key] == '' || form[key] == undefined || form[key] == null || form[key].length == 0) {
            return count;
        } else {
            return count + 1;
        }
    }, 0)
};

function onFilter() {
    update_table();
};

function onFilterStatusChange() {
    showFilterContainer.value = !showFilterContainer.value;
};

function onPageChange() {
    update_table();
};

function handleReset() {
    Object.keys(form).forEach(key => {
        delete form[key];
    });
    form.page = 1;
    form.pagesize = 10;
    update_table();
};

function handleAdd() {
    parent.value = null;
    dialogAddVisible.value = true;
};

function handleEdit(row) {
    r_id.value = row.id;
    dialogEditVisible.value = true;
};

function handleDelete(row) {
    ElMessageBox.confirm(
        `确定删除${row.user_name}轮班人员吗?`,
        '提示',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }
    ).then(() => {
        http.delete(`/inspecs/persons/${row.id}`).then(res => {
            ElMessage({
                message: '删除成功.',
                type: 'success',
            });
            update_table();
        }).catch(err => {
            ElMessageBox.alert(
                err.response.data.msg,
                '警告',
                {
                    confirmButtonText: '确定',
                    type: 'warning',
                }
            )
        });
    }).catch(() => {
        ElMessage({
            type: 'info',
            message: '已取消删除'
        });
    });
};


function onAddConfirm() {
    dialogAddVisible.value = false;
    update_table();
};

function onAddCancel() {
    dialogAddVisible.value = false;
};

function onEditAffirm() {
    dialogEditVisible.value = false;
    update_table();
};

function onEditCancel() {
    dialogEditVisible.value = false;
};

function handleRefresh() {
    update_table();
};

onMounted(() => {
    update_table();
});

</script>


<style scoped>
.search-container {
    width: 100%;
    display: flex;
    justify-content: center;
    justify-items: center;
    height: 40px;
}

.search-input {
    width: 50%;
    max-width: 500px;
}

.search-button {
    border: 1px solid #dcdfe6;
}

.filter-container {
    width: 100%;
    display: flex;
    justify-content: flex-start;

    margin-bottom: 10px;

    .el-input, .el-select {
        width: 50%;
        max-width: 300px;
        margin-right: 10px;
    }
}

.tool-bar-container {
    display: flex;
    justify-content: flex-start;

}

.mind_map {
    margin-left: 10px;


}

.pagination-container {
  margin-top: auto; /* 自动顶到底部 */
  align-self: flex-start; /* 左对齐 */
  padding-top: 15px;
  width: 100%;
}

</style>