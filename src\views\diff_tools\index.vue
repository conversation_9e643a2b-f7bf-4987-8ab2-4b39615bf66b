<template>
    <div style="display: flex; flex-direction: column; height: calc(100vh - 123px); margin: 0px 20px;">
    <div class="tool-bar-container">
        <el-button icon="Plus" type="primary" @click="handleAdd">差分包生成</el-button>
        <div style="margin-left: auto; display: flex; gap: 10px;">
            <el-tooltip class="box-item" effect="dark" content="重置已添加的筛选条件" placement="top-start">
                <el-button text bg @click="handleReset">重置</el-button>
            </el-tooltip>
            <filterButton @click="onFilterStatusChange" :count="filterCount" />
            <el-button icon="Refresh" text bg @click="handleRefresh">刷新</el-button>
        </div>
    </div>

    <div class="filter-container" v-if="showFilterContainer">
        <el-input v-model="form.diff_package_name_re" placeholder="请输入差分包名称" @keyup.enter="onFilter" clearable>
            <template #append>
                <el-button icon="Search" @click="onFilter"></el-button>
            </template>
        </el-input>
        <el-input v-model="form.diff_package_version_re" placeholder="请输入差分包版本" @keyup.enter="onFilter" clearable>
            <template #append>
                <el-button icon="Search" @click="onFilter"></el-button>
            </template>
        </el-input>
        <el-input v-model="form.diff_package_md5_re" placeholder="请输入差分包MD5" @keyup.enter="onFilter" clearable>
            <template #append>
                <el-button icon="Search" @click="onFilter"></el-button>
            </template>
        </el-input>
    </div>

    
    <el-table :data="tableData" stripe border style="width: 100%;flex: 1;" class="table-container">
        <el-table-column label="所属项目" min-width="300" align="center">
            <template #default="{ row }">
                <span>{{ row.project_name }}({{ row.project_number }})</span>
            </template>
        </el-table-column>
        <el-table-column label="差分包" min-width="200" align="center">
            <template #default="{ row }">
                <el-link type="primary" :underline="false" :href="row.diff_package_path" target="_blank">{{ row.diff_package_name }}</el-link>
            </template>
        </el-table-column>
        <el-table-column prop="diff_package_version" label="差分包版本号" min-width="200" align="center"></el-table-column>
        <el-table-column prop="old_package_version" label="基准软件包版本号" min-width="200" align="center"></el-table-column>
        <el-table-column prop="new_package_version" label="目标软件包版本号" min-width="200" align="center"></el-table-column>
        <el-table-column prop="operator_name" label="操作人" min-width="150" align="center"></el-table-column>
        <el-table-column label="验证状态" min-width="150" align="center">
            <template #default="{ row }">
                <el-tag v-if="row.diff_package_status == 0" type="info">未验证</el-tag>
                <el-tag v-else-if="row.diff_package_status == 1" type="success">验证通过</el-tag>
                <el-tag v-else-if="row.diff_package_status == 2" type="danger">验证不通过</el-tag>
            </template>
        </el-table-column>
        <el-table-column prop="create_time" label="创建时间" min-width="200" align="center"></el-table-column>
        <el-table-column label="操作" min-width="220" fixed="right" align="center">
            <template #default="{ row }">
                <div style="display: flex; justify-content: center;">
                    <el-button type="primary" size="small" @click="handleDetail(row)">详情</el-button>
                    <el-button type="primary" size="small" @click="handleVerify(row)" v-if="row.diff_package_status == 0 && row.operator_email == userStore.user_info.email">验证</el-button>
                    <el-button type="primary" size="small" @click="handleDelete(row)" v-if="row.operator_email == userStore.user_info.email || row.operator_email == '<EMAIL>'">删除</el-button>
                    <el-button v-if="row.diff_package_status == 1" type="primary" size="small" @click="handleShare(row)">共享</el-button>
                </div>
            </template>
        </el-table-column>
    </el-table>
    <div class="pagination-container">
        <el-pagination :page-sizes="[1, 10, 15, 20, 25, 50, 100]" layout="prev, pager, next, jumper, total, sizes"
            v-model:current-page="form.page" v-model:page-size="form.pagesize" :total="total" background
            @change="onPageChange" />
    </div>
</div>


    

    <el-dialog v-if="dialogAddVisible" v-model="dialogAddVisible" title="差分包生成" width="800"
        :close-on-click-modal="false">
        <Add @confirm="onAddConfirm" @cancel="onAddCancel" />
    </el-dialog>

    <el-drawer v-model="drawerDetailVisible" :with-header="false" size="50%" :destroy-on-close="true">
        <RecordDetail :id="r_id" />
    </el-drawer>

    <el-dialog v-model="dialogVerifyVisible" width="500" title="差分包验证" center>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="dialogVerifyVisible = false">取消</el-button>
                <el-button @click="onVeryfyNg">
                    验证不通过
                </el-button>
                <el-button type="primary" @click="onVeryfyPass">
                    验证通过
                </el-button>
            </div>
        </template>
    </el-dialog>

    <el-dialog v-if="dialogShareVisible" v-model="dialogShareVisible" width="500" title="差分包分享">
        <ShareDialog :r_id="r_id" @cancel="dialogShareVisible = false" />
    </el-dialog>

</template>


<script setup>

import { ref, reactive, onMounted, watch, onActivated } from 'vue';
import http from '@/utils/http/http.js';
import Add from './add.vue';
import RecordDetail from './detail.vue';
import { useProjectStore } from '@/stores/project.js';
import filterButton from '@/components/filterButton.vue';
import { useUserStore } from '@/stores/user.js';
import ShareDialog from './share.vue';

let userStore = useUserStore();
let projectStore = useProjectStore();
const tableData = ref([]);
const dialogAddVisible = ref(false);
const r_id = ref(-1);
const drawerDetailVisible = ref(false);
const dialogVerifyVisible = ref(false);
const filterCount = ref(0);
const dialogShareVisible = ref(false);

let form = reactive({
    page: 1,
    pagesize: 15,
});

let total = ref(0);

let showFilterContainer = ref(false);

function onFilterStatusChange() {
    showFilterContainer.value = !showFilterContainer.value;
};

function update_table() {
    http.get('/diff_tool/records', { params: form }).then(res => {
        tableData.value = res.data.data.results;
        total.value = res.data.data.count;
    });

    filterCount.value = Object.keys(form).filter(key => !['page', 'pagesize'].includes(key)).reduce((count, key) => {
        if (form[key] == '' || form[key] == undefined || form[key] == null || form[key].length == 0) {
            return count;
        } else {
            return count + 1;
        }
    }, 0)
};

function handleReset() {
    form = reactive({
        page: 1,
        pagesize: 10,
        project_number: form.project_number,
    });
    update_table();
};

function onPageChange() {
    update_table();
};

function onFilter() {
    form.page = 1;
    form.pagesize = 10;
    update_table();
};

function handleAdd() {
    dialogAddVisible.value = true;
};

function onAddConfirm() {
    dialogAddVisible.value = false;
    update_table();
};

function onAddCancel() {
    dialogAddVisible.value = false;
};

function handleDetail(row) {
    r_id.value = row.id;
    drawerDetailVisible.value = true;
};

function handleVerify(row) {
    r_id.value = row.id;
    dialogVerifyVisible.value = true;
};

function handleDelete(row) {
    ElMessageBox.confirm(
        '确定删除吗?',
        '提示',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }
    ).then(() => {
        http.delete(`/diff_tool/records/${row.id}`).then(res => {
            ElMessage({
                message: '删除成功.',
                type: 'success',
            });
            update_table();
        }).catch(err => {
            ElMessage({
                type: 'error',
                message: err.response.data.msg
            });
        });
    }).catch(() => {
        ElMessage({
            type: 'info',
            message: '已取消删除'
        });
    });
};

function handleShare(row) {
    r_id.value = row.id;
    dialogShareVisible.value = true;
};

function handleRefresh() {
    update_table();
};

function onVeryfyPass() {
    http.post('/diff_tool/records/verify', {
        id: r_id.value,
        diff_package_status: 1,
    }).then(res => {
        update_table();
        dialogVerifyVisible.value = false;
    }).catch(err => {
        ElMessageBox.alert(
            err.response.data.msg,
            '警告',
            {
                confirmButtonText: '确定',
                type: 'warning',
            }
        )
    });
};

function onVeryfyNg() {
    http.post('/diff_tool/records/verify', {
        id: r_id.value,
        diff_package_status: 2,
    }).then(res => {
        update_table();
        dialogVerifyVisible.value = false;
    }).catch(err => {
        ElMessageBox.alert(
            err.response.data.msg,
            '警告',
            {
                confirmButtonText: '确定',
                type: 'warning',
            }
        )
    });
};

watch(() => projectStore.project_info, () => {
    form.project_number = projectStore.project_info.projectCode;
    update_table();
});

onMounted(() => {
    form.project_number = projectStore.project_info.projectCode;
    update_table();

    const open_id = userStore.user_info.open_id;
        http.post('/auto_test/access_stat', {
            user_id: open_id,
            page_url: '/software_tools/diff_tool_records',
            page_title: '差分记录',
        })
});

onActivated(() => {
    const open_id = userStore.user_info.open_id;
        http.post('/auto_test/access_stat', {
            user_id: open_id,
            page_url: '/software_tools/diff_tool_records',
            page_title: '差分记录',
        })
});

</script>


<style lang="scss" scoped>
.tool-bar-container {
    display: flex;
    /* margin-bottom: 5px; */
}

.search-container {
    width: 100%;
    display: flex;
    justify-content: center;
    justify-items: center;
    height: 40px;
}

.search-input {
    width: 50%;
    max-width: 500px;
}

.search-button {
    border: 1px solid #dcdfe6;
}

.filter-container {
    width: 100%;
    display: flex;
    justify-content: flex-start;

    margin-bottom: 10px;

    .el-input {
        width: 50%;
        max-width: 300px;
        margin-right: 10px;
    }
}

.preserve-whitespace {
    white-space: pre-wrap;
    text-align: left;
    /* 或者使用 pre */
}

.pagination-container {
  margin-top: auto; /* 自动顶到底部 */
  align-self: flex-start; /* 左对齐 */
  padding-top: 15px;
  width: 100%;
}

</style>