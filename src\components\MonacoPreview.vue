<template>
   <div class="editor-wrapper">
    <h3 class="editor-title">代码预览</h3>
    <div ref="container" class="monaco-editor"></div>
  </div>
</template>

<script setup>
import * as monaco from 'monaco-editor';
import { onMounted, onBeforeUnmount, ref, watch } from 'vue';

const props = defineProps({
  code: { type: String, default: '' },       // 要显示的代码
  filename: { type: String, default: '' },   // 可选，自动识别语言用
  readOnly: { type: Boolean, default: true }
});

const container = ref(null);
let editorInstance = null;

// 根据文件名或代码内容自动识别语言
function detectLanguage(code, filename) {
  if (filename) {
    const ext = filename.split('.').pop().toLowerCase();
    if (ext === 'py') return 'python';
    if (ext === 'c') return 'c';
    if (ext === 'cpp' || ext === 'cc' || ext === 'cxx') return 'cpp';
    if (ext === 'js') return 'javascript';
    if (ext === 'ts') return 'typescript';
    if (ext === 'json') return 'json';
    if (ext === 'html') return 'html';
    if (ext === 'css') return 'css';
    if (ext === 'h') return 'cpp';
  }

  // 内容特征检测（简单判断）
  if (/^\s*#include\s+<.*>/.test(code) || /\bint\s+main\s*\(/.test(code)) return 'c';
  if (/^\s*def\s+\w+\s*\(/.test(code) || /\bprint\s*\(/.test(code)) return 'python';
  if (/^\s*function\s+\w+\s*\(/.test(code)) return 'javascript';
  if (/^{[\s\S]*}$/.test(code.trim())) return 'json';

  return 'plaintext';
}

onMounted(() => {
  const lang = detectLanguage(props.code, props.filename);

  editorInstance = monaco.editor.create(container.value, {
    value: props.code,
    language: lang,
    theme: 'vs-light', // 可改 vs-dark
    readOnly: props.readOnly,
    automaticLayout: true,
    minimap: { enabled: false }
  });
});

watch(() => props.code, (newCode) => {
  if (editorInstance) {
    editorInstance.setValue(newCode);
  }
});

onBeforeUnmount(() => {
  if (editorInstance) {
    editorInstance.dispose();
  }
});
</script>

<style scoped>

.editor-wrapper {
  /* 你可以加点样式让标题和编辑器间距更好看 */
  margin-bottom: 16px;
}

.editor-title {
  font-size: 18px;
  font-weight: 600;
  color: #1d2129;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.editor-title::before {
  content: '';
  width: 4px;
  height: 18px;
  background-color: #409eff;
  border-radius: 2px;
}

.monaco-editor {
  width: 100%;
  min-height: 1200px;
  border: 1px solid #dcdcdc;
  border-radius: 6px;
}
</style>
