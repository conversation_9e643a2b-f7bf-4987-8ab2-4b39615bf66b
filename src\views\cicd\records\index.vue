<template>
    <div style="display: flex; flex-direction: column; height: calc(102vh - 250px);">
        <div class="tool-bar-container">
            <el-button icon="Plus" type="primary" @click="handleAdd">新建流程</el-button>
            <div style="margin-left: auto; display: flex; gap: 10px;">
                <div>
                    <el-button icon="Setting" text bg @click="isSettingVisible = true">设置</el-button>
                    <!-- 设置弹窗 -->
                    <el-popover
                        v-model:visible="isSettingVisible"
                        width="180"
                        trigger="manual"
                        placement="bottom"
                    >
                        <template #reference>
                            <!-- 设置按钮作为触发器 -->
                            <div style="display: inline-block;"></div>
                        </template>
                        <!-- 操作按钮 -->
                        <div class="column-popper-title">
                            <div style="display: flex; align-items: center; justify-content: space-between;">
                                <el-checkbox
                                    :model-value="tableColumns.every(item => item.show)"
                                    :indeterminate="tableColumns.some(item => item.show) && tableColumns.some(item => !item.show)"
                                    label="列展示"
                                    @change="selectAllColumn"
                                />
                                <el-button text @click="resetColumns" style="margin-right: -10px;">重置</el-button>
                            </div>
                        </div>
                        <!-- 列设置内容 -->
                        <div class="column-content" style="max-height: 200px; overflow-y: auto;">
                            <div class="column-item" v-for="column in tableColumns" :key="column.key">
                                <el-checkbox v-model="column.show" :label="column.name" :disabled="column.disabled"></el-checkbox>
                            </div>
                        </div>
                    </el-popover>
                </div>
                <el-tooltip class="box-item" effect="dark" content="重置已添加的筛选条件" placement="top-start">
                    <el-button text bg @click="handleReset">重置</el-button>
                </el-tooltip>
                <filterButton @click="onFilterStatusChange" :count="filterCount" />
                <el-button icon="Refresh" text bg @click="handleRefresh">刷新</el-button>
            </div>
        </div>
        
        <div class="filter-container" v-if="showFilterContainer">
            <el-input v-model="form.project_name_re" placeholder="请输入项目名称" @keyup.enter="onFilter" clearable>
                <template #append>
                    <el-button icon="Search" @click="onFilter"></el-button>
                </template>
            </el-input>
            <el-input v-model="form.description_re" placeholder="请输入流程配置描述" @keyup.enter="onFilter" clearable>
                <template #append>
                    <el-button icon="Search" @click="onFilter"></el-button>
                </template>
            </el-input>
        </div>

        <el-table :data="tableData" stripe border style="width: 100%;flex: 1;" class="table-container" v-loading="loading">
            <el-table-column v-if="tableColumns[0].show" prop="project_name" label="所属项目" min-width="200" align="center"></el-table-column>
            
            <el-table-column v-if="tableColumns[1].show" prop="description" label="流程配置" min-width="300" align="center"></el-table-column>

            <el-table-column v-if="tableColumns[2].show" prop="created_by" label="创建人员" min-width="120" align="center"></el-table-column>
            
            <el-table-column v-if="tableColumns[3].show" prop="created_at" label="创建时间" min-width="180" align="center" :formatter="timeFormatter"></el-table-column>
            
            <el-table-column v-if="tableColumns[4].show" label="操作" min-width="150" fixed="right" align="center">
                <template #default="{ row }">
                    <div style="display: flex; justify-content: center; gap: 10px;">
                        <el-button type="primary" size="small" @click="handleEdit(row)">编辑</el-button>
                        <el-button type="danger" size="small" @click="handleDelete(row)">删除</el-button>
                    </div>
                </template>
            </el-table-column>
        </el-table>
        
        <div class="pagination-container">
            <el-pagination
                :page-sizes="[10, 15, 20, 25, 50, 100]"
                layout="prev, pager, next, jumper, total, sizes"
                v-model:current-page="form.page"
                v-model:page-size="form.pagesize"
                :total="total"
                background
                @change="onPageChange"
            />
        </div>

        <!-- 新建流程弹窗 -->
        <el-dialog v-model="dialogCreateVisible" title="新建流程" width="600px" :close-on-click-modal="false">
            <el-form :model="createForm" :rules="createRules" ref="createFormRef" label-width="100px">
                <el-form-item label="选择项目" prop="project">
                    <el-select
                        v-model="createForm.project"
                        placeholder="请选择项目"
                        style="width: 100%"
                        filterable
                        :loading="projectsLoading"
                    >
                        <el-option
                            v-for="project in projectsList"
                            :key="project.id"
                            :label="getProjectDisplayName(project)"
                            :value="project"
                        >
                            <span>{{ getProjectDisplayName(project) }}</span>
                        </el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="流程名称" prop="name">
                    <el-input
                        v-model="createForm.name"
                        placeholder="请输入流程名称，如：WPTSN12代码打包工作流"
                        maxlength="100"
                        show-word-limit
                    />
                </el-form-item>

                <el-form-item label="流程描述" prop="description">
                    <el-input
                        v-model="createForm.description"
                        type="textarea"
                        :rows="3"
                        placeholder="请输入流程描述（可选）"
                        maxlength="255"
                        show-word-limit
                    />
                </el-form-item>
            </el-form>

            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="dialogCreateVisible = false">取消</el-button>
                    <el-button type="primary" @click="handleCreateConfirm" :loading="createLoading">
                        确认创建
                    </el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import http from '@/utils/http/http.js';
import filterButton from '@/components/filterButton.vue';
import { useAccessStat } from '@/utils/accessStat';

useAccessStat('/cicd/records', '流程记录列表');

const router = useRouter();

// 表格列配置
const tableColumns = ref([
    { key: 'project_name', name: '所属项目', show: true, disabled: false },
    { key: 'description', name: '流程配置', show: true, disabled: false },
    { key: 'created_by', name: '创建人员', show: true, disabled: false },
    { key: 'created_at', name: '创建时间', show: true, disabled: false },
    { key: 'actions', name: '操作', show: true, disabled: true }
]);

// 数据状态
const tableData = ref([]);
const loading = ref(false);
const total = ref(0);

// 筛选相关
const showFilterContainer = ref(false);
const filterCount = ref(0);
const isSettingVisible = ref(false);

// 新建流程相关
const dialogCreateVisible = ref(false);
const createLoading = ref(false);
const projectsLoading = ref(false);
const projectsList = ref([]);
const createFormRef = ref(null);

// 新建流程表单
const createForm = reactive({
    project: null,
    name: '',
    description: ''
});

// 表单验证规则
const createRules = {
    project: [
        { required: true, message: '请选择项目', trigger: 'change' }
    ],
    name: [
        { required: true, message: '请输入流程名称', trigger: 'blur' },
        { min: 2, max: 100, message: '流程名称长度在 2 到 100 个字符', trigger: 'blur' }
    ]
};

// 表单数据
const form = reactive({
    page: 1,
    pagesize: 20,
    project_name_re: '',
    description_re: ''
});

// 时间格式化
const timeFormatter = (row, column, cellValue) => {
    if (!cellValue) return '';
    return new Date(cellValue).toLocaleString('zh-CN');
};

// 获取数据
const fetchData = async () => {
    loading.value = true;
    try {
        const params = {
            page: form.page,
            pagesize: form.pagesize
        };
        
        // 添加筛选条件
        if (form.project_name_re) {
            params.project_name_re = form.project_name_re;
        }
        if (form.description_re) {
            params.description_re = form.description_re;
        }
        
        const response = await http.get('/api/pipelines/records', { params });
        
        if (response.data.success) {
            tableData.value = response.data.data || [];
            total.value = response.data.total || 0;
        } else {
            ElMessage.error('获取数据失败: ' + (response.data.message || '未知错误'));
        }
    } catch (error) {
        console.error('获取流程记录失败:', error);
        ElMessage.error('获取数据失败: ' + (error.message || '网络错误'));
        // 模拟数据用于演示
        tableData.value = [
            {
                id: '1',
                project_name: 'Cetus 21.4版项目(RESCN14)',
                description: 'RESCN14编译打包工作流',
                created_by: '阿刚',
                created_at: '2025-06-24 13:55:05'
            },
            {
                id: '2', 
                project_name: 'HWCP(WPTSN12)',
                description: 'WPTSN12代码打包工作流',
                created_by: '杨叶辉',
                created_at: '2025-06-24 13:55:05'
            },
            {
                id: '3',
                project_name: '产品自动化测试平台(WPTSN11)',
                description: 'WPTSN11编译打包工作流',
                created_by: '王宇',
                created_at: '2025-06-24 13:55:05'
            }
        ];
        total.value = 3;
    } finally {
        loading.value = false;
    }
};

// 格式化项目名称显示，添加项目编号
const getProjectDisplayName = (project) => {
    if (!project) return '';

    // 如果有number字段，显示为"项目名称(编号)"格式
    if (project.number) {
        return `${project.name}(${project.number})`;
    }

    // 如果没有number字段但有id，使用id
    if (project.id) {
        return `${project.name}(${project.id})`;
    }

    // 如果都没有，只显示名称
    return project.name;
};

// 获取项目列表
const fetchProjects = async () => {
    projectsLoading.value = true;
    try {
        const response = await http.get('/projects/p/all', {
            timeout: 10000
        });

        if (response.data && response.data.data && response.data.data.results) {
            projectsList.value = response.data.data.results.map(project => ({
                id: project.id,
                name: project.name,
                number: project.number || project.id, // 保存项目编号
                description: project.description || '',
                git_url: project.git_url || '',
                // 保留原始数据
                ...project
            }));
        }
    } catch (error) {
        console.error('获取项目列表失败:', error);
        ElMessage.error('获取项目列表失败: ' + (error.message || '网络错误'));
    } finally {
        projectsLoading.value = false;
    }
};

// 新建流程
const handleAdd = () => {
    // 重置表单
    createForm.project = null;
    createForm.name = '';
    createForm.description = '';

    // 获取项目列表
    fetchProjects();

    // 显示弹窗
    dialogCreateVisible.value = true;
};

// 确认创建流程
const handleCreateConfirm = async () => {
    if (!createFormRef.value) return;

    try {
        // 表单验证
        await createFormRef.value.validate();

        createLoading.value = true;

        // 跳转到流程编辑器，传递项目信息和流程名称
        const queryParams = {
            mode: 'create',
            projectId: createForm.project.id,
            projectName: createForm.project.name,
            projectNumber: createForm.project.number,
            flowName: createForm.name,
            flowDescription: createForm.description
        };

        // 关闭弹窗
        dialogCreateVisible.value = false;

        // 跳转到工作流编辑器
        router.push({
            name: 'WorkflowEditor',
            query: queryParams
        });

    } catch (error) {
        console.error('创建流程失败:', error);
    } finally {
        createLoading.value = false;
    }
};

// 编辑流程
const handleEdit = (row) => {
    router.push({ 
        name: 'WorkflowEditor', 
        query: { editId: row.id }
    });
};

// 删除流程
const handleDelete = async (row) => {
    try {
        await ElMessageBox.confirm(
            `确定删除流程记录 "${row.project_name}" 吗？`,
            '确认删除',
            {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }
        );
        
        const response = await http.delete(`/api/pipelines/records/${row.id}`);
        if (response.data.success) {
            ElMessage.success('删除成功');
            fetchData(); // 重新加载数据
        } else {
            ElMessage.error('删除失败: ' + (response.data.message || '未知错误'));
        }
    } catch (error) {
        if (error !== 'cancel') {
            console.error('删除流程记录失败:', error);
            ElMessage.error('删除失败: ' + (error.message || '网络错误'));
        }
    }
};

// 筛选相关方法
const onFilterStatusChange = () => {
    showFilterContainer.value = !showFilterContainer.value;
    updateFilterCount();
};

const onFilter = () => {
    form.page = 1;
    fetchData();
    updateFilterCount();
};

const updateFilterCount = () => {
    let count = 0;
    if (form.project_name_re) count++;
    if (form.description_re) count++;
    filterCount.value = count;
};

const handleReset = () => {
    form.project_name_re = '';
    form.description_re = '';
    form.page = 1;
    filterCount.value = 0;
    fetchData();
};

const handleRefresh = () => {
    fetchData();
};

// 分页
const onPageChange = () => {
    fetchData();
};

// 列设置
const selectAllColumn = (checked) => {
    tableColumns.value.forEach(column => {
        if (!column.disabled) {
            column.show = checked;
        }
    });
};

const resetColumns = () => {
    tableColumns.value.forEach(column => {
        column.show = true;
    });
};

// 初始化
onMounted(() => {
    fetchData();
});
</script>

<style scoped>
.tool-bar-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 0 4px;
}

.filter-container {
    display: flex;
    gap: 16px;
    margin-bottom: 16px;
    padding: 16px;
    background-color: #f5f7fa;
    border-radius: 4px;
}

.table-container {
    flex: 1;
}

.pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 16px;
}

.column-popper-title {
    padding: 8px 0;
    border-bottom: 1px solid #e4e7ed;
    margin-bottom: 8px;
}

.column-content {
    padding: 4px 0;
}

.column-item {
    padding: 4px 0;
}
</style>
