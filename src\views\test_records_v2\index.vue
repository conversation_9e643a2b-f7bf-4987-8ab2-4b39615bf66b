<template>
    <el-tabs v-model="activeName" @tab-click="handleTabClick" style="margin-left: 20px;;">
        <el-tab-pane label="计划执行记录" name="1">
            <div v-if="activeName === '1'" style="padding-top: 20px">
                <ExecRecords />
            </div>
        </el-tab-pane>
        <el-tab-pane label="用例执行记录" name="2">
            <div v-if="activeName === '2'" style="padding-top: 20px;height: 100%;">
                <RecordItems />
            </div>
        </el-tab-pane>
        <el-tab-pane label="我收藏的" name="3">
            <div v-if="activeName === '3'" style="padding-top: 20px;height: 100%;">
                <RecordItemsCollected />
            </div>
        </el-tab-pane>

    </el-tabs>

</template>


<script setup>
import { ref, onActivated, provide } from 'vue'
import ExecRecords from './exec_records.vue'
import RecordItems from './record_items.vue'
import RecordItemsCollected from './record_items_collected.vue'
import { useTestRecordStore } from '@/stores/testRecord.js';
import { useAccessStat } from '@/utils/accessStat';

useAccessStat('test_records_v2/list', '测试记录列表');

let testRecordStore = useTestRecordStore();

const activeName = ref(testRecordStore.tab || '1');

const testPlanId = ref(null);

provide('testPlanId', testPlanId);

const handleTabClick = (tab) => {
    testRecordStore.setTab(tab.paneName);
};

onActivated(() => {
    if (testRecordStore.testPlanId) {
        testPlanId.value = testRecordStore.testPlanId;
        testRecordStore.clearTestPlanId();
    }
    activeName.value = testRecordStore.tab || '1';
});

</script>


<style lang="scss" scoped></style>