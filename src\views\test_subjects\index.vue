<template>
  <div class="test-activity-tabs">
    <el-tabs v-model="activeTabName" class="custom-tabs" @tab-click="handleTabClick">
      <el-tab-pane label="测试产品" name="products"></el-tab-pane>
      <el-tab-pane label="测试样件" name="prototypes"></el-tab-pane>
    </el-tabs>
    <div v-if="activeTabName==='products'">
        <TestProductList></TestProductList>
    </div>
    <div v-if="activeTabName==='prototypes'">
        <TestPrototypesIndex></TestPrototypesIndex>
    </div>
   
  </div>
</template>

<script setup>
import { ref } from 'vue';
import TestProductList from '@/views/test_products/index.vue';
import TestPrototypesIndex from '@/views/test_prototypes/index.vue';
const activeTabName = ref('products');

const handleTabClick = (tab) => {
  console.log('点击的标签：', tab.props.name);
  // 可在此处添加切换标签时的逻辑，如请求对应数据等
};
</script>

<style scoped>
 
.test-activity-tabs {
  width: 100%;
  margin: 0px 20px;
}

.custom-tabs {
    margin-bottom: 25px;
}

.el-tabs__nav {
  border-bottom: none;
}

.el-tabs__item {
  padding: 0 16px;
  font-size: 14px;
  color: #606266;
  margin-right: 24px;
}

.el-tabs__item.is-active {
  color: #409eff;
  border-bottom: 2px solid #409eff;
}
</style>