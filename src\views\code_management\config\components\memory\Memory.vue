<template>
  <div class="config-detail">
    <!-- 内存信息配置内容 -->
    <div class="memory-config">
      <info 
        v-if="memory_show"
        :key="`Configuration-${node_level}`" 
        :config="{variables: memory}"
        :workspacePath="workspacePath"
        :branchStatus="branchStatus"
        :project_code="project_code"
        :project_name="project_name"
        :project_gitlab="project_gitlab"
        :project_branch="project_branch"
        :node_level="node_level"
        :nodeName="nodeName"
        :hasEditPermission="hasEditPermission"/>
      
      <!-- 表格标题和操作按钮 -->
      <div class="table-header">
        <div class="header-content">
          <h3 class="table-title">内存信息配置</h3>
          <div class="table-actions">
            <el-button 
              type="primary" 
              @click="handleAddRow"
              :disabled="!hasEditPermission"
            >
              新增行
            </el-button>
            <el-button 
              type="danger" 
              @click="handleDeleteLastRow"
              :disabled="!hasEditPermission || table_info.tableData?.length === 0"
            >
              删除行
            </el-button>
          </div>
        </div>
      </div>
      
      <!-- 表格容器 -->
      <div class="table-container">
        <el-table
          :data="table_info['tableData']"
          border
          style="width: 100%"
          :header-cell-style="{ background: '#f0f5ff' }"
          :row-class-name="rowClassName"
        >
          <el-table-column
              v-for="(col, index) in table_info['tableColumns']"
              :key="index"
              :prop="col.prop"
              :label="col.label"
              align="center"
              :min-width="col.minWidth || 120"
          >
              <!-- 为每个列添加编辑模板 -->
              <template #default="scope">
              <!-- 根据列的不同类型选择不同的输入组件 -->
              <template v-if="col.type === 'text'">
                  <el-input
                  v-model="scope.row[col.prop]"
                  :disabled="!hasEditPermission"
                  size="small"
                  class="cell-input"
                  @focus="cacheOldValue(scope.$index, col.prop)"
                  @change="handleCellChange(scope.row, scope.$index, index, col.prop)"
                  ></el-input>
              </template>
              <!-- 新增 text_disable 分支，强制禁用 -->
              <template v-else-if="col.type === 'text_disable'">
                  <el-input
                  v-model="scope.row[col.prop]"
                  disabled
                  size="small"
                  class="cell-input"
                  ></el-input>
              </template>
              <template v-else-if="col.type === 'select' && col.options">
                  <el-select
                  v-model="scope.row[col.prop]"
                  :disabled="!hasEditPermission"
                  size="small"
                  style="width: 100%"
                  @focus="cacheOldValue(scope.$index, col.prop)"
                  @change="handleCellChange(scope.row, scope.$index, index, col.prop)"
                  >
                  <el-option
                      v-for="opt in col.options"
                      :key="opt.value"
                      :label="opt.label"
                      :value="opt.value"
                  ></el-option>
                  </el-select>
              </template>
              <template v-else>
                  <el-input
                  v-model="scope.row[col.prop]"
                  :disabled="!hasEditPermission"
                  size="small"
                  class="cell-input"
                  @focus="cacheOldValue(scope.$index, col.prop)"
                  @change="handleCellChange(scope.row, scope.$index, index, col.prop)"
                  ></el-input>
              </template>
              </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, reactive, nextTick } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import http from '@/utils/http/http';
import info from './info.vue';


const props = defineProps({
  config: {
    type: Object,
    required: true
  },
  workspacePath: {
    type: String,
    required: true
  },
  branchStatus: {
    type: String,
    required: true
  },
  project_code:{
    type: String,
    required: true
  },
  project_name: {
    type: String,
    required: true
  },
  project_gitlab:{
    type: String,
    required: true
  },
  project_branch: {
    type: String,
    required: true
  },
  node_level: {
    type: String,
    required: true
  },
  nodeName: {
      type: String,
      required: true
  },
  previousConfig: {
    type: Object,
    default: () => ({})
  },
  hasEditPermission: {
    type: Boolean,
    default: true
  }
});

let currentMessage = null;
const memory_show = ref(false);

// 行样式分类
const rowClassName = ({ row, rowIndex }) => {
  return rowIndex % 2 === 0 ? 'table-row-even' : 'table-row-odd';
};

// 数据存储
const memory = ref([])
const table_info = ref({
  tableData: [],
  tableColumns: []
})
const branch_create = ref(true);


// 构造新行数据
const newRow = {
  index_row: '',
  address_index: 'demo',
  address_info: '',
  data_length: '1',  // 初始数据长度为0
  default_value: ['0x0'], // 默认值为空数组
  read_from_flash: '1',
  desc: 'demo'
};


// 从API获取数据
const fetchinfo = async () => {
  try {
    const response = await http.post('/code_management/memory_info', {
      params: { 
        node_level: props.node_level,
        nodeName: props.nodeName,
        project_code: props.project_code,
        project_name: props.project_name,
        project_gitlab: props.project_gitlab,
        project_branch: props.project_branch,
        workspacePath: props.workspacePath,
        branchStatus: props.branchStatus
      }
    });

    if (response.data.status === 1) {
      memory.value = response.data.memory_info;
      table_info.value = response.data.table_info;
      
      // 初始化时计算数据长度
      table_info.value.tableData.forEach(row => {
        calculateDataLength(row);
      });
      
      memory_show.value = true;
    }

  } catch (error) {
    console.error('列配置加载失败');
  }
};

// 计算数据长度（按逗号分割默认值的数量）
const calculateDataLength = (row) => {
  try {
    // 确保 row 和 default_value 存在
    if (!row || row.default_value === undefined || row.default_value === null) {
      row.data_length = '0';
      return;
    }

    // 1. 处理数组类型（直接取长度）
    if (Array.isArray(row.default_value)) {
      row.data_length = row.default_value.length.toString();
      return;
    }

    // 2. 处理字符串类型（按逗号分割）
    if (typeof row.default_value === 'string') {
      // 清除所有不可见字符（避免换行/制表符干扰）
      const cleanedStr = row.default_value.replace(/\s+/g, '');
      // 按逗号分割（空字符串返回空数组）
      const values = cleanedStr === '' ? [] : cleanedStr.split(',');
      row.data_length = values.length.toString();
      return;
    }

    // 3. 其他类型（如数字、对象等）
    row.data_length = '0';
  } catch (error) {
    console.error('计算数据长度出错:', error, '行数据:', row);
    row.data_length = '0'; // 出错时默认长度为0
  }
};

// 检查地址范围是否足够
const checkAddressRange = (row, rowIndex, newValue) => {
  // 获取当前行和下一行的地址
  const currentAddr = parseInt(row.address_info, 16);
  const rowCount = table_info.value.tableData.length;
  
  // 如果是最后一行，没有下一行，不做检查
  if (rowIndex >= rowCount - 1) {
    return true;
  }
  
  // 获取下一行地址
  const nextRow = table_info.value.tableData[rowIndex + 1];
  const nextAddr = parseInt(nextRow.address_info, 16);
  
  // 计算地址差值（十进制）
  const addressDiff = nextAddr - currentAddr;
  
  // 计算新的默认值长度
  let newValueLength;
  if (Array.isArray(newValue)) {
    newValueLength = newValue.length;
  } else if (typeof newValue === 'string') {
    newValueLength = newValue.split(',').filter(val => val.trim() !== '').length;
  } else {
    newValueLength = 0;
  }
  
  // 检查长度是否超过地址范围
  return newValueLength <= addressDiff;
};

// 新增行处理函数
const handleAddRow = async () => {

  // 获取最后一行数据
  const lastRow = table_info.value.tableData.length 
    ? table_info.value.tableData[table_info.value.tableData.length - 1] 
    : null;

  // 获取新增行的索引
  newRow.index_row = lastRow ? lastRow.index_row + 1 : 1

  try {
    const requestParams = {
      project_code: props.project_code,
      project_name: props.project_name,
      project_gitlab: props.project_gitlab,
      project_branch: props.project_branch,
      node_level: props.node_level,
      nodeName: props.nodeName,
      workspacePath: props.workspacePath,
      branchStatus: props.branchStatus,
      row_index: newRow.index_row,
      row_config_info: newRow

    };

    const response = await http.post('/code_management/new_memory', {
      params: requestParams
    });

    if (response.data.status === 1) {
      console.log("获取的参数：", response.data)
      const { min_size } = response.data;
      
      if (min_size === undefined || min_size === null) {
        ElMessage.warning('新增行失败：未获取到有效的最小单元地址');
        return;
      }

   
      // 计算新的地址信息
      let newAddressInfo = '0x00000000'; 
      let newAddrNum = 0; // 初始化newAddrNum
      if (lastRow) {
        const lastAddrNum = parseInt(lastRow.address_info, 16);
        const minSizeNum = parseInt(min_size, 16);
        // 获取数据长度
        const dataLength = parseInt(lastRow.data_length, 10);
        const divisor = minSizeNum;
        let result;

        if (dataLength > minSizeNum) {
            const quotient = dataLength / divisor;
            const integerPart = Math.floor(quotient); // 抹除小数部分
            // 判断是否有余数（避免浮点数精度问题，用乘法反推）
            if (integerPart * divisor < dataLength) {
              result = integerPart + 1;
            } else {
              result = integerPart;
            }
            // 计算新的地址 
            newAddrNum = lastAddrNum + minSizeNum * result;
          } else {
            newAddrNum = lastAddrNum + minSizeNum;
          }
        newAddressInfo = `0x${newAddrNum.toString(16).padStart(8, '0').toUpperCase()}`;
      }
      
      newRow.address_info = newAddressInfo;

      table_info.value.tableData.push(newRow);
      ElMessage.success('新增行成功');
    } else {
      ElMessage.warning('新增行失败：' + (response.data.msg || '未知错误'));
    }
  } catch (error) {
    console.error('新增行请求失败:', error);
    ElMessage.error('新增行请求失败，请重试');
  }
};
// 删除最后一行处理函数
const handleDeleteLastRow = async () => {
  if (!table_info.value.tableData || table_info.value.tableData.length === 0) {
    ElMessage.warning('没有可删除的行');
    return;
  }

  try {
    const lastRow = table_info.value.tableData[table_info.value.tableData.length - 1];
    
    const confirmResult = await ElMessageBox.confirm(
      `确定要删除最后一行数据吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    if (confirmResult === 'confirm') {
      const response = await http.post('/code_management/delete_memory', {
        params: {
          row_ids: [lastRow.indexNo],
          project_code: props.project_code,
          project_name: props.project_name,
          project_gitlab: props.project_gitlab,
          project_branch: props.project_branch,
          node_level: props.node_level,
          nodeName: props.nodeName,
          workspacePath: props.workspacePath,
          branchStatus: props.branchStatus
        }
      });

      if (response.data.status === 1) {
        table_info.value.tableData.pop();
        ElMessage.success('删除成功');
      } else {
        ElMessage.warning('删除失败：' + (response.data.msg || '未知错误'));
      }
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除行请求失败:', error);
      ElMessage.error('删除行请求失败，请重试');
    }
  }
};

// 组件挂载时执行
onMounted(() => {
  fetchinfo();
});

// 缓存单元格旧值
const cellOldValues = ref({});

const cacheOldValue = (rowIndex, prop) => {
  if (table_info.value?.tableData && table_info.value.tableData[rowIndex]) {
    if (!cellOldValues.value[rowIndex]) {
      cellOldValues.value[rowIndex] = {};
    }
    cellOldValues.value[rowIndex][prop] = JSON.parse(JSON.stringify(table_info.value.tableData[rowIndex][prop]));
    console.log(`已缓存旧值：行${rowIndex}，列${prop}=${cellOldValues.value[rowIndex][prop]}`);
  }
};

// 单元格变化处理函数
const handleCellChange = async (row, rowIndex, colIndex, prop) => {
  const oldValue = cellOldValues.value[rowIndex]?.[prop];
  const newValue = row[prop];
  
  // 校验：如果没有旧值缓存或值未变化，直接返回
  if (oldValue === undefined) {
    console.warn(`未找到行${rowIndex}列${prop}的旧值缓存`);
    return;
  }
  
  // 深比较判断值是否真的变化
  const isEqual = JSON.stringify(oldValue) === JSON.stringify(newValue);
  if (isEqual) {
    console.log(`值未变化，跳过请求：${oldValue}`);
    return;
  }

  // 处理默认值变化 - 自动计算数据长度并检查地址范围
  if (prop === 'default_value') {
    // 检查地址范围是否足够
    const isValid = checkAddressRange(row, rowIndex, newValue);
    if (!isValid) {
      // 地址范围不足，回滚数据并提示
      if (currentMessage) currentMessage.close();
      currentMessage = ElMessage.error('数据长度超过地址范围，已恢复原值');
      rollbackValue(row, rowIndex, prop, oldValue);
      return;
    }
    
    // 自动计算数据长度
    calculateDataLength(row);
  }

  // 检查编辑权限
  if (!props.hasEditPermission) {
    if (currentMessage) currentMessage.close();
    currentMessage = ElMessage.warning('当前无编辑权限，数据已自动回退');
    rollbackValue(row, rowIndex, prop, oldValue);
    return;
  }

  // 发送修改请求
  try {
    const response = await http.post('/code_management/memory_change', {
      params: {
        config_value: newValue,
        workspace_path: props.workspacePath,
        branch_status: props.branchStatus,
        config_label: prop,
        project_code: props.project_code,
        project_name: props.project_name,
        project_gitlab: props.project_gitlab,
        project_branch: props.project_branch,
        node_level: props.node_level,
        nodeName: props.nodeName,
        row: rowIndex + 1,
        col: colIndex + 1
      }
    });

    if (currentMessage) currentMessage.close();

    if (response.data.config_status === 1) {
      currentMessage = ElMessage.success('配置更新成功');
      // 清除已生效的旧值缓存
      if (cellOldValues.value[rowIndex]) {
        delete cellOldValues.value[rowIndex][prop];
      }
    } else {
      currentMessage = ElMessage.warning('配置更新失败，已恢复原值');
      rollbackValue(row, rowIndex, prop, oldValue);
    }
  } catch (error) {
    if (currentMessage) currentMessage.close();
    currentMessage = ElMessage.warning('请求失败，已恢复原值');
    console.info('请求错误:', error);
    rollbackValue(row, rowIndex, prop, oldValue);
  }
};

// 回滚值的工具函数
// 回滚值的工具函数
const rollbackValue = (row, rowIndex, prop, oldValue) => {
  // 1. 先更新数据源
  if (table_info.value?.tableData && table_info.value.tableData[rowIndex]) {
    // 深拷贝旧值避免引用问题
    const restoredValue = JSON.parse(JSON.stringify(oldValue));
    // 更新数据源
    table_info.value.tableData[rowIndex][prop] = restoredValue;
    
    // 如果是回滚默认值，同时回滚数据长度
    if (prop === 'default_value') {
      calculateDataLength(table_info.value.tableData[rowIndex]);
    }
    
    // 2. 再更新当前行对象（解决表格渲染引用问题）
    // 先删除原有属性触发响应式更新
    delete row[prop];
    // 使用nextTick确保DOM更新后再设置新值
    nextTick(() => {
      row[prop] = restoredValue;
    });
  }
  console.log(`已回滚：行${rowIndex}列${prop}=${oldValue}`);
};
</script>

<style scoped>
/* 保持原有样式不变 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

.config-detail {
  padding: 24px;
  background-color: #fff;
  border-radius: 12px;
  width: 100%;
  overflow: hidden;
}

.memory-config {
  width: 100%;
  overflow: hidden;
}

.table-header {
  margin-top: 30px;
  margin-bottom: 16px;
  width: 100%;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-actions {
  display: flex;
  gap: 10px;
}

.el-button {
  padding: 10px;
}

.table-title {
  font-size: 18px;
  font-weight: 600;
  color: #1d2129;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.table-title::before {
  content: '';
  width: 4px;
  height: 18px;
  background-color: #409eff;
  border-radius: 2px;
}

.table-container {
  width: 100%;
  overflow-x: auto;
  overflow-y: hidden;
  margin-top: 16px;
}

.el-table {
  border: 1px solid #e5e6eb;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.2s ease;
}

.el-table th {
  font-size: 14px;
  font-weight: 500;
  color: #4e5969;
  padding: 12px 0;
  border-bottom: 1px solid #e5e6eb !important;
  background-color: #f0f5ff !important;
}

.el-table td {
  padding: 12px 8px;
  border-bottom: 1px solid #f2f3f5 !important;
  font-size: 13px;
  color: #1d2129;
}

.table-row-even {
  background-color: #fff;
}

.table-row-odd {
  background-color: #f9fafb;
}

.el-table__row:hover > td {
  background-color: #e6f7ff !important;
  transition: background-color 0.2s ease;
}

.cell-input {
  height: 40px;
  border-radius: 4px;
  border-color: #dcdfe6;
  transition: all 0.2s;
  width: 100%;
}

.cell-input:focus-within {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

:deep(.el-input.is-disabled .el-input__inner) {
  background-color: #f5f7fa;
  color: #86909c;
  cursor: not-allowed;
}

:deep(.el-table--border) {
  border-left: none;
  border-right: none;
}

:deep(.el-table--border th.gutter:last-of-type) {
  border-right: none !important;
}

:deep(.el-table::before) {
  height: 0;
}

:deep(.el-table__empty-text) {
  color: #86909c;
  padding: 40px 0;
  font-size: 14px;
}

@media (max-width: 768px) {
  .config-detail {
    padding: 16px;
  }
  
  .table-title {
    font-size: 16px;
  }
}
</style>