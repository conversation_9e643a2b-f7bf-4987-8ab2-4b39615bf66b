<template>
  <div class="test-activity-tabs">
    <el-tabs v-model="activeTabName" class="custom-tabs" @tab-click="handleTabClick">
      <el-tab-pane label="测试机台" name="machines"></el-tab-pane>
      <el-tab-pane label="测试设备" name="devices"></el-tab-pane>
    </el-tabs>
    <div v-if="activeTabName==='machines'">
        <machines></machines>
    </div>
    <div v-if="activeTabName==='devices'">   
      <devices></devices>
  </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import machines from '@/views/machine/index.vue'
import devices from '@/views/device/index.vue'
import toDo from '@/views/toDo.vue'
import { useRouter } from 'vue-router';

const router = useRouter();



const activeTabName = ref('machines');

const handleTabClick = (tab) => {
  console.log('点击的标签：', tab.props.name);
  // 可在此处添加切换标签时的逻辑，如请求对应数据等
};
</script>

<style scoped>
 
.test-activity-tabs {
  width: 100%;
  margin: 0px 20px;
}

.custom-tabs {
    margin-bottom: 25px;
}

.el-tabs__nav {
  border-bottom: none;
}

.el-tabs__item {
  padding: 0 16px;
  font-size: 14px;
  color: #606266;
  margin-right: 24px;
}

.el-tabs__item.is-active {
  color: #409eff;
  border-bottom: 2px solid #409eff;
}
</style>