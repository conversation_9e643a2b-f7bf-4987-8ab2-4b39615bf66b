<template>
    <el-tree-select ref="selectRef" v-model="model" :load="loadNode" :props="{ isLeaf: 'isLeaf' }"
        :cache-data="cacheData" :multiple="multiple" lazy :data="data" filterable :filter-method="filterMethod"
        clearable @change="onChange">

        <template #default="{ data }">
            <div style="display: flex">
                <el-avatar v-if="data.isLeaf" :size="20" :src="data.avatar" style="margin-right: 5px;" />
                <span v-if="data.isLeaf" class="custom-tree-node">
                    <span>{{ data.label }}</span>
                </span>
                <template v-else>
                    <!-- <Icon icon="mingcute:department-fill" size="1.2em" style="margin-right: 5px;" /> -->
                    <span class="custom-tree-node">
                        <span>{{ data.label }}</span>
                    </span>
                </template>
            </div>
        </template>

    </el-tree-select>
</template>

<script setup>
import { ref, computed } from 'vue';
import http from '@/utils/http/http.js';
import { debounce } from 'lodash';

const props = defineProps({
    multiple: {
        type: Boolean,
        default: false
    },
    cacheData: {
        type: Array,
        default: () => []
    },
    labelField: {
        type: String,
        default: 'realName'
    },
    valueField: {
        type: String,
        default: 'username'
    },
});

const model = defineModel();

const multiple = computed(() => props.multiple);

const cacheData = computed(() => props.cacheData);

const data = ref([]);

let selectRef = ref(null);

const get_orga = (resolve, department_id) => {
    http.get("/users/fs_group_list", { params: { department_id: department_id } }).then(res => {
        let data1 = res.data.data.departmentList;
        data1 = data1.map(item => {
            item.label = item.name;
            item.value = item.departmentId;
            item.children = [];
            item.isLeaf = false;
            return item;
        });
        let data2 = res.data.data.staffList;
        data2 = data2.map(item => {
            item.label = item[props.labelField];
            item.value = item[props.valueField];
            item.isLeaf = true;
            return item;
        });

        let data = data1.concat(data2);

        if (data.length === 0) {
            data = [{
                label: '暂无数据',
                value: '暂无数据',
                isLeaf: true,
                disabled: true
            }];
        }

        resolve(data);
    });
};

const loadNode = (node, resolve) => {
    if (node.level === 0) {
        get_orga(resolve);
    } else {
        get_orga(resolve, node.data.value);
    }
};

let state = 0;

const filterMethod = debounce(
    (query) => {

        if (query) {
            state = 1;
            http.get("/users/search_user", { params: { name: query } }).then(res => {
                data.value = res.data.data.map(item => {
                    item.label = item[props.labelField];
                    item.value = item[props.valueField];
                    item.isLeaf = true;
                    return item;
                });
            });
        } else {
            if (state == 1) {
                state = 0;
                data.value = [];
                get_orga((d) => { data.value = d; });
            }
        }
    }, 300);

const getNode = (value) => {
    let node = selectRef.value.getNode(value);
    if (!node) {
        for (let i = 0; i < cacheData.value.length; i++) {
            if (cacheData.value[i].value === value) {
                node = cacheData.value[i];
                break;
            }
        }
    };
    return node;
}

defineExpose({
    getNode
});

const emit = defineEmits(['change']);

const onChange = (val) => {
    let node = selectRef.value.getNode(val);
    // console.log(node?.data);
    if (node) {
        emit('change', node.data);
    }
};

</script>
