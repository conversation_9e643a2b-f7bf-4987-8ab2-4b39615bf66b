<template>
  <div class="test-activity-tabs">
    <el-tabs v-model="activeTabName" class="custom-tabs" @tab-click="handleTabClick">
      <el-tab-pane label="测试指令" name="Cmdlist"></el-tab-pane>
      <el-tab-pane label="测试图片" name="image"></el-tab-pane>
      <el-tab-pane label="点检清单" name="Itemlist"></el-tab-pane>
      <el-tab-pane label="Lesson" name="lesson"></el-tab-pane>
    </el-tabs>
    <div v-if="activeTabName==='Cmdlist'">
        <customCmds></customCmds>
    </div>
    <div v-if="activeTabName==='image'">   
      <imageResources></imageResources>
  </div>
  <div v-if="activeTabName==='Itemlist'">
    <inspectionItems></inspectionItems>   
  </div>
  <div v-if="activeTabName==='lesson'">
    <toDo></toDo>
  </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import customCmds from '@/views/custom_cmds/index.vue'
import imageResources from '@/views/image_resources/index.vue'
import inspectionItems  from '@/views/inspection-items/index.vue'
import toDo from '@/views/toDo.vue'
import { useRouter } from 'vue-router';

const router = useRouter();



const activeTabName = ref('Cmdlist');

const handleTabClick = (tab) => {
  console.log('点击的标签：', tab.props.name);
  // 可在此处添加切换标签时的逻辑，如请求对应数据等
};
</script>

<style scoped>
 
.test-activity-tabs {
  width: 100%;
  margin: 0px 20px;
}

.custom-tabs {
    margin-bottom: 25px;
}

.el-tabs__nav {
  border-bottom: none;
}

.el-tabs__item {
  padding: 0 16px;
  font-size: 14px;
  color: #606266;
  margin-right: 24px;
}

.el-tabs__item.is-active {
  color: #409eff;
  border-bottom: 2px solid #409eff;
}
</style>