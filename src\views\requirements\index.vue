<template>
    <div style="display: flex; flex-direction: column; height: calc(109.4vh - 250px);">
        <el-tabs v-model="activeTabName" class="tabs" @tab-click="handleTabClick">
            <el-tab-pane label="相关方需求" name="stakeholder_need"></el-tab-pane>
            <el-tab-pane label="系统功能性需求" name="system_requirement"></el-tab-pane>
            <el-tab-pane label="系统技术安全要求" name="system_safe_need"></el-tab-pane>
            <el-tab-pane label="软件需求" name="software_requirement"></el-tab-pane>
            <el-tab-pane label="硬件需求" name="hardware_requirement"></el-tab-pane>
        </el-tabs>


        <div class="tool-bar-container">

            <div style="margin-left: auto; display: flex; gap: 10px;">
                <el-tooltip class="box-item" effect="dark" content="重置已添加的筛选条件" placement="top-start">
                    <el-button text bg @click="handleReset">重置</el-button>
                </el-tooltip>
                <filterButton @click="onFilterStatusChange" :count="filterCount" />
                <el-button icon="Refresh" text bg @click="handleRefresh">刷新</el-button>
            </div>
        </div>

        <div v-if="showFilterContainer">
            <el-row :gutter="10" style="width: 100%;max-width: 1800px;margin-bottom: 10px;">
                <el-col :span="4">
                    <el-input v-model="form.name" placeholder="请输入需求名称" @keyup.enter="onFilter" clearable
                        style="width: 100%;">
                        <template #append>
                            <el-button icon="Search" @click="onFilter"></el-button>
                        </template>
                    </el-input>
                </el-col>
                <el-col :span="4">
                    <el-select v-model="form.propertyList" placeholder="请选择需求属性" @change="onFilter" style="width: 100%;"
                        multiple clearable>
                        <el-option label="产品需求" value="PRODUCT"></el-option>
                        <el-option label="需求标题" value="TITLE"></el-option>
                        <el-option label="功能安全需求" value="FUNCTION_SAFE"></el-option>
                        <el-option label="非功能安全需求" value="NON_FUNCTION_SAFE"></el-option>
                    </el-select>
                </el-col>
                <el-col :span="4">
                    <el-select v-model="form.reviewStatusList" placeholder="请选择单个评审状态" @change="onFilter"
                        style="width: 100%;" multiple clearable>
                        <el-option label="已撤销" value="CANCEL"></el-option>
                        <el-option label="初始状态" value="INIT"></el-option>
                        <el-option label="审批中" value="APPROVE"></el-option>
                        <el-option label="已评审" value="ACCEPT"></el-option>
                        <el-option label="已驳回" value="REJECT"></el-option>
                    </el-select>
                </el-col>
                <el-col :span="4">
                    <el-select v-model="form.multiReviewStatusList" placeholder="请选择整体评审状态" @change="onFilter"
                        style="width: 100%;" multiple clearable>
                        <el-option label="已撤销" value="CANCEL"></el-option>
                        <el-option label="未评审" value="INIT"></el-option>
                        <el-option label="审批中" value="APPROVE"></el-option>
                        <el-option label="已评审" value="ACCEPT"></el-option>
                        <el-option label="已驳回" value="REJECT"></el-option>
                    </el-select>
                </el-col>
                <el-col :span="4">
                    <el-select v-model="form.stateList" placeholder="请选择需求状态" @change="onFilter" style="width: 100%;"
                        multiple clearable>
                        <el-option label="未开始" value="NOT_START"></el-option>
                        <el-option label="进行中" value="DOING"></el-option>
                        <el-option label="已实现" value="REALIZE"></el-option>
                    </el-select>
                </el-col>
            </el-row>
            <el-row :gutter="10" style="width: 100%;max-width: 1800px;margin-bottom: 10px;">
                <el-col :span="6">
                    <el-date-picker type="datetimerange" v-model="form.create_time" range-separator="-" start-placeholder="创建时间"
                     value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%;"
                     @change="onFilter" />
                </el-col>
                <el-col :span="6">
                    <el-date-picker type="datetimerange" v-model="form.update_time" range-separator="-" start-placeholder="更新时间"
                     value-format="YYYY-MM-DD HH:mm:ss"  style="width: 100%;"
                     @change="onFilter" />
                </el-col>
            </el-row>
        </div>

        <el-table :data="tableData" stripe border style="width: 100%;flex: 1;" class="table-container">

            <el-table-column prop="num" label="需求ID" min-width="150" align="center" fixed="left"></el-table-column>

            <el-table-column prop="name" label="需求名称" min-width="200" align="center">
                <template #default="{ row }">
                    <el-link type="primary" :underline="false" @click="handleDetail(row)">{{ row.name }}</el-link>
                </template>
            </el-table-column>

            <!-- <el-table-column prop="projectName" label="所属项目" min-width="300" align="center"></el-table-column> -->

            <!-- <el-table-column prop="type" label="需求类型" min-width="200" align="center">
                <template #default="{ row }">
                    <el-tag v-if="row.type == 'stakeholder_need'">相关方需求</el-tag>
                    <el-tag v-else-if="row.type == 'system_requirement'">系统功能性需求</el-tag>
                    <el-tag v-else-if="row.type == 'system_safe_need'">系统技术安全要求</el-tag>
                    <el-tag v-else-if="row.type == 'software_requirement'">软件需求</el-tag>
                    <el-tag v-else-if="row.type == 'hardware_requirement'">硬件需求</el-tag>
                    <el-tag v-else>{{ row.type }}</el-tag>
                </template>
            </el-table-column> -->

            <el-table-column prop="version" label="版本" min-width="200" align="center">
                <template #default="{ row }">
                    <span>{{ row.version }}({{ versionTypeMap[row.versionType] || row.versionType }})</span>
                </template>
            </el-table-column>

            <el-table-column prop="reviewStatus" label="单个评审状态" min-width="150" align="center">
                <template #default="{ row }">
                    <el-tag v-if="row.reviewStatus == 'CANCEL'" type="primary">已撤销</el-tag>
                    <el-tag v-else-if="row.reviewStatus == 'INIT'" type="primary">初始状态</el-tag>
                    <el-tag v-else-if="row.reviewStatus == 'APPROVE'" type="warning">审批中</el-tag>
                    <el-tag v-else-if="row.reviewStatus == 'ACCEPT'" type="success">已评审</el-tag>
                    <el-tag v-else-if="row.reviewStatus == 'REJECT'" type="danger">已驳回</el-tag>
                    <el-tag v-else>{{ row.reviewStatus }}</el-tag>
                </template>
            </el-table-column>

            <el-table-column prop="multiReviewStatus" label="整体评审状态" min-width="150" align="center">
                <template #default="{ row }">
                    <el-tag v-if="row.multiReviewStatus == 'CANCEL'" type="primary">已撤销</el-tag>
                    <el-tag v-else-if="row.multiReviewStatus == 'INIT'" type="primary">未评审</el-tag>
                    <el-tag v-else-if="row.multiReviewStatus == 'APPROVE'" type="warning">审批中</el-tag>
                    <el-tag v-else-if="row.multiReviewStatus == 'ACCEPT'" type="success">已评审</el-tag>
                    <el-tag v-else-if="row.multiReviewStatus == 'REJECT'" type="danger">已驳回</el-tag>
                    <el-tag v-else>{{ row.multiReviewStatus }}</el-tag>
                </template>
            </el-table-column>

            <!-- <el-table-column prop="describe" label="需求描述" min-width="200" align="center"></el-table-column> -->

            <el-table-column prop="state" label="需求状态" min-width="150" align="center">
                <template #default="{ row }">
                    <el-tag v-if="row.state == 'NOT_START'" type="primary">未开始</el-tag>
                    <el-tag v-else-if="row.state == 'DOING'" type="warning">进行中</el-tag>
                    <el-tag v-else-if="row.state == 'REALIZE'" type="success">已实现</el-tag>
                    <el-tag v-else>{{ row.state }}</el-tag>
                </template>
            </el-table-column>

            <!-- <el-table-column prop="creatorUser.realName" label="创建人" min-width="100" align="center"></el-table-column> -->

            <el-table-column prop="updateUser.realName" label="责任人" min-width="100" align="center"></el-table-column>

            <!-- <el-table-column prop="createTime" label="创建时间" min-width="200" align="center"></el-table-column> -->

            <el-table-column prop="updateTime" label="更新时间" min-width="200" align="center"></el-table-column>

            <el-table-column prop="wbsTask" label="WBS任务" min-width="200" align="center">
                <template #default="{ row }">
                    <span v-if="row.wbsTask == null">--</span>
                </template>
            </el-table-column>

            <el-table-column label="操作" min-width="200" fixed="right" align="center">
                <template #default="{ row }">
                    <div style="display: flex; justify-content: center; gap: 10px;">
                        <el-button type="primary" size="small" @click="handleAddTestCase(row)">添加用例</el-button>
                        <el-button type="primary" size="small" @click="handleRelateTestCase(row)">关联用例</el-button>
                    </div>
                </template>
            </el-table-column>

        </el-table>

        <div class="pagination-container">
            <el-pagination :page-sizes="[1, 10, 15, 20, 25, 50, 100]" layout="prev, pager, next, jumper, total, sizes"
                v-model:current-page="form.page" v-model:page-size="form.pagesize" :total="total" background
                @change="onPageChange" />
        </div>

        <el-dialog v-if="dialogRelateVisible" v-model="dialogRelateVisible" title="关联测试用例" width="1000"
            :close-on-click-modal="false">
            <RelateTestCases :requirement="requirement" :relatedTestCaseIds="relatedTestCaseIds"
            @confirm="dialogRelateVisible = false" @cancel="dialogRelateVisible = false" />
        </el-dialog>

    </div>
</template>


<script setup>
import { ref, reactive, watch, onMounted, onActivated } from 'vue';
import http from '@/utils/http/http.js';
import { useRouter, useRoute } from 'vue-router';
import { useProjectStore } from '@/stores/project.js';
import RelateTestCases from './relate-test-cases.vue'
import { useAccessStat } from '@/utils/accessStat';

useAccessStat('/requirements/list', '需求列表');

const router = useRouter();
const route = useRoute();
let projectStore = useProjectStore();
let filterCount = ref(0);
const tableData = ref([]);
const dialogRelateVisible = ref(false);
const requirement = ref({});
const relatedTestCaseIds = ref([]);

const versionTypeMap = {
    "REVIEW": '评审版本',
};

let form = reactive({
    page: 1,
    pagesize: 10,
    type_list: ["stakeholder_need"],
    project_id: '',
    create_time: [],
    update_time: [],
});

let total = ref(0);

let showFilterContainer = ref(false);

const activeTabName = ref('stakeholder_need');

function handleTabClick(tab) {
    form.type_list = [tab.paneName];
    update_table();
};

function onFilterStatusChange() {
    showFilterContainer.value = !showFilterContainer.value;
};

function update_table() {
    if (!form.project_id) {
        tableData.value = [];
        total.value = 0;
        return;
    }

    if (form.create_time === null) {
        form.create_time = [];
    }

    if (form.update_time === null) {
        form.update_time = [];
    }

    http.get('/requirements', { params: form }).then(res => {
        tableData.value = res.data.data.results;
        total.value = res.data.data.count;
    });

    filterCount.value = Object.keys(form).filter(key => !['page', 'pagesize', 'project_id', 'type_list'].includes(key)).reduce((count, key) => {
        if (form[key] == '' || form[key] == undefined || form[key] == null || form[key].length == 0) {
            return count;
        } else {
            return count + 1;
        }
    }, 0)
};

function handleReset() {
    Object.keys(form).forEach(key => {
        delete form[key];
    });
    form.page = 1;
    form.pagesize = 10;
    form.project_id = projectStore.project_info.id;
    form.type_list = ["stakeholder_need"];

    update_table();
};

function onPageChange() {
    update_table();
};

function onFilter() {
    form.page = 1;
    form.pagesize = 10;
    update_table();
};

function handleDetail(row) {
    router.push(`/requirements/${row.id}`);
};

function handleAddTestCase(row) {
    router.push({ path: '/test_cases2/add', query: { project_number: row.projectCode, requirement_id: row.id } });
};

function handleRelateTestCase(row) {
    requirement.value = row;

    let projectRelList = row.projectRelList;
    let test_cases = [];
    projectRelList.forEach(item => {
        test_cases = test_cases.concat(item.datas);
    });
    relatedTestCaseIds.value = test_cases.map(item => item.testManageNum);

    dialogRelateVisible.value = true;
};

function handleRefresh() {
    update_table();
};

watch(() => projectStore.project_info, () => {
    form.project_id = projectStore.project_info.id;
    update_table();
});

onMounted(() => {
    form.project_id = projectStore.project_info.id;
    if (route.query.version_name) {
        form.m_version = route.query.version_name;
    }
    update_table();
});

onActivated(() => {
    form.project_id = projectStore.project_info.id;
    if (route.query.version_name) {
        form.m_version = route.query.version_name;
    }
    update_table();
});


</script>


<style lang="scss" scoped>
.search-container {
    width: 100%;
    display: flex;
    justify-content: center;
    justify-items: center;
    height: 40px;
}

.search-input {
    width: 50%;
    max-width: 500px;
}

.search-button {
    border: 1px solid #dcdfe6;
}

.filter-container {
    width: 100%;
    display: flex;
    justify-content: flex-start;

    margin-bottom: 10px;

    .el-input,
    .el-select {
        width: 50%;
        max-width: 300px;
        margin-right: 10px;
    }
}

.tool-bar-container {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    justify-items: center;

    .el-select {
        width: 500px;
        margin-left: 10px;
    }

}

.el-aside {
    width: 200px;
    height: 100% !important;

    margin-top: 20px;
}

.el-main {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);

    margin-top: 0px;

    .table-container {
        flex: 1;
        overflow: auto;
    }
}
</style>