<template>

    
      <!-- 初始化弹窗 -->
      <el-dialog
        v-model="dialogInitVisible"
        title="初始化配置"
        width="45%"
        center
        @close="$emit('update:modelValue', false)"
      >
      

        <!-- 芯片选择 -->
        <div class="form-group ini_form_group">
          <span class="label">芯片：</span>
          <el-select
            v-model="props.formData.selectedChip"
            class="input-field"
            placeholder="请选择芯片"
            style="border: none; padding: 0px;"
          >
            <el-option
              v-for="(chip, index) in chipList"
              :key="index"
              :label="chip"
              :value="chip"
            >{{ chip }}</el-option>
          </el-select>
        </div>
        <!-- 功能模块 -->
        <div class="form-group ini_form_group" >
          <span class="label">功能模块：</span>
          <div class="checkbox-container">
            <div
              v-for="(module, index) in functionalModules"
              :key="index"
              class="checkbox-item"
              style="border: none;"
            >
              <input
                type="checkbox"
                v-model="props.formData.selectedModules"
                :value="module"
                class="checkbox"
              />
              <span class="module-label">{{ module }}</span>
            </div>
          </div>
        </div>

        
        <template #footer>
          <el-button @click="cancelCreation">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </template>
      </el-dialog>

</template>


<script setup>
import { onMounted,reactive, ref, watch } from 'vue';
import http from '@/utils/http/http';
import { ElMessage } from 'element-plus';
import { useRouter, useRoute } from 'vue-router';
import { useProjectStore } from '@/stores/project';

const router = useRouter();

// 接收父组件传递的参数
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  // 接收表单数据
  formData: {
    type: Object,
    default: () => ({})
  },
  // 接收功能模块数据
  functionalModules: {
    type: Array,
    default: () => []
  },
  // 接收选中的芯片数据
  pickChips: {
    type: Array,
    default: () => []
  },
  // 接收芯片列表数据
  chipList: {
    type: Array,
    default: () => []
  },
  project_code: { 
      type: String,
      required: true 
  },
  project_name:{ 
      type: String,
      required: true 
  },
  project_description:{
      type: String,
      required: true 
  }
});

// 定义事件
const emit = defineEmits(['update:modelValue','success', 'close','update:submitLoading']);



// 使用 reactive 定义表单数据对象
let formData = reactive({
  sdkGitLab: "http://*********/mcu-team/sdk_code/hiwaysdk_2.0",
  sdkBranch: "",
  selectedChip: "",
  selectedModules: [],
  group: '',
  space: '',
  branchDisabled: false, // 控制SDK Branch是否禁用
  tagDisabled: false,   // 控制SDK Tag是否禁用
});

const visible = ref(false)

let branches = ref([]); // 示例分支列表
let tags = ref([]); // 示例tag列表

// 功能模块初始化状态
let isInitialized = ref(false);
// 提交功能状态
let submit_status = ref(false);

let init_status = ref();
let message = ref();

let pick_chips = ref([]);

let config_info = ref({});
let config = ref({})
let pick_info = ref({});


let pick_tree_info = ref([]);
let workspace_path = "";
let branch_status = "";
// 项目号
let project_code = "";
// 项目名称
let project_name = "";

// 初始项目号
let init_project_code = "";
let init_project_name = "";


const loading=ref(false);

let teamdata = ref([]);
let projectOptions = ref([]);
let branchOptions = ref([]);

onMounted(() => {
    // 从props初始化数据
  functionalModules.value = props.functionalModules;
  pick_chips.value = props.pickChips;
  chipList.value = props.chipList;
  
  // 初始化选中的模块
  formData.selectedModules = [...props.pickChips];
  console.log("跳转到sdk_init.vue");

  });

  
// 监听props变化，实时更新数据
watch(() => props.functionalModules, (newVal) => {
  functionalModules.value = newVal;
});

watch(() => props.pickChips, (newVal) => {
  pick_chips.value = newVal;
  formData.selectedModules = [...newVal];
});

watch(() => props.chipList, (newVal) => {
  chipList.value = newVal;
});


// 监听父组件v-model传入的值
watch(
  () => props.modelValue,
  (newVal) => {
    visible.value = newVal;
  },
  { immediate: true }  // 初始化时立即同步
);







// 错误处理函数
function handleError(error) {
  if (error.response) {
    ElMessage.error(`请求失败: 状态码 ${error.response.status} - ${error.response.data.message || '未知错误'}`);
  } else if (error.request) {
    ElMessage.error('请求失败: 没有收到服务器响应');
  } else {
    ElMessage.error('请求失败: ' + error.message);
  }
}




const dialogInitVisible = ref(true);
// 功能模块
let functionalModules = ref([]);
// 芯片
const chipList = ref([])


let currentMessage = null; // 用于存储当前消息实例





// 自动重试封装函数
async function retryRequest(fn, retries = 3, delay = 1000) {
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      if (attempt < retries) {
        console.warn(`请求失败，正在重试（${attempt}/${retries})...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      } else {
        throw error;
      }
    }
  }
}


//取消按钮的功能
const cancelCreation = () => {
  // emit('close');
  // router.push('/projects_repository/project_index')
  emit('update:modelValue', false);
  emit('close');
  
}

// 提交按钮的功能
const handleSubmit = () => {
console.log("表单数据:", props.formData);


// 非空校验
if (!props.formData.selectedModules.length) {
  ElMessage.warning('请先单击初始化，选择功能模块');
  return;
} else if (!props.formData.sdkGitLab.trim() || !props.formData.engineeringGroup || !props.formData.projectName || !props.formData.selectedChip || !props.formData.selectedModules.length) {
  if (!props.formData.defaultBranch && !props.formData.sdkVersion) {
          ElMessage.warning('信息不能有空值');
          return;
  }

} else {
          console.log("初始化：", init_project_code, init_project_name);
          console.log("提交：",project_code, project_name);
          try {
              if (init_project_code === project_code) {

                // 提交sdk配置信息
                submitForm();
            } else {
              ElMessage.warning('项目信息有变化，请选择正确的项目：'+ init_project_code);
            }

          } catch (error) {
            console.error("判断错误", error);
          }
   
           
        }
        
  };



  
// 配置提交按钮功能
  const submitForm = () => {
    console.log("项目信息:", project_code, project_name);
    // 请求芯片和功能模块的 grpc配置 
    console.log("表单数据:", formData);
    // 获取提交配置信息
    emit('update:submitLoading', true);
    loading.value = true;
    // 使用 Axios 实例发送 Get请求
    retryRequest(() => http.post('/code_management/config_submit',{
      params:{
        // 项目信息
        project_code: props.project_code,
        project_name: props.project_name,
        // 功能信息
        sdk_gitlab: props.formData.sdkGitLab,
        sdk_branch: props.formData.defaultBranch,
        sdk_tag:  props.formData.sdkVersion,
        selected_chip:  props.formData.selectedChip,
        selected_modules:  props.formData.selectedModules,
        // 提交配置信息
        workspace:  props.formData.projectName,
        workgroup:  props.formData.engineeringGroup,
        project_description: props.project_description
        // config_info: JSON.stringify(config_info.value)
      },timeout: 30000
    }), 1, 2000).then(response => {
      emit('update:submitLoading', false);
      loading.value = false;
      console.log("提交信息状态:", response.data);

      if (response.data.config_status === 1) {
          ElMessage.success('提交成功');
          router.push('/projects_repository/project_index')
          emit('success');
      } else {
        ElMessage.error(`提交失败：${response.data.message}`);
      }
    }).catch(error => { 
      emit('update:submitLoading', false);
      loading.value = false;
      ElMessage.error('提交失败')
      console.error("提交失败:", error);
    });
  }


</script>

<style scoped>
/* 容器样式 */
.codemangement-container {
  width: 100%;
  padding: 20px;
  border: 1px solid #ccc;
  box-shadow: 0 0 10px #ccc;
  font-size: 14px;
  font-weight: normal;
}

.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #3080dd;
  padding: 15px 0px;
  margin: -20px -20px;
  color: white;
}

.main-title {
  font-size: 24px;
  font-weight: bold;
  padding-left: 20px;
}

.logo-icon {
  font-size: 24px;
  color: white;
  margin-right: 20px;
}

/* 表单样式 */
form {
  margin-top: 50px;
}

/* 表单项样式 */
.form-group {
  margin-bottom: 50px; /* 表单项之间的间距 */
  display: flex;
  align-items: flex-start; /* 确保元素垂直对齐起点 */
  gap: 10px; /* 元素之间的间距 */
}

.ini_form_group{
  margin-bottom: 0px;
}

/* 标签样式 */
.label {
  display: inline-block;
  width: 120px; /* 标签固定宽度 */
  margin-right: -10px; /* 标签与输入框的间距 */
  margin-left: 30px;
  margin-top: 10px;
  color: #757575;
  font-size: 14px;
}

/* 输入框样式 */
.input-field {
  flex: 1; /* 输入框占据剩余空间 */
  height: 30px; /* 设置输入框高度与 el-select 一致 */
  padding: 0 10px; /* 输入框内边距 */
  border: 1px solid #ccc;
  border-radius: 4px;
  outline: none;
  margin-right: 100px;
  color: #757575;
}

.input-field[disabled] {
  background-color: #f5f5f5;
  border: 1px solid #ddd;
}

input:focus {
  border-color: #3080dd;
}

.submit-btn {
  margin-top: -5px;
  margin-left: -70px;
  margin-right: 100px;
  background-color: #007bff;
  color: #fff;
  border: none;
  border-radius: 2px;
  cursor: pointer;
  font-size: 14px;
  border-radius: 4px;
}

.submit-btn:hover {
  background-color: #0056b3;
}

/* el-select 样式 */
.el-select {
  flex: 1; /* el-select 占据剩余空间 */
  height: 40px; /* 设置 el-select 高度 */
  padding: 0 10px; /* el-select 内边距 */
  border: 1px solid #ccc;
  border-radius: 4px;
  outline: none;
}

.el-select[disabled] {
  background-color: #f5f5f5;
  border: 1px solid #ddd;
}

.el-select .el-input__inner {
  height: 40px; /* 设置 el-select 内部输入框高度 */
}

.el-select .el-input__inner:focus {
  border-color: #3080dd;
}

.el-select .el-input__inner:hover {
  background-color: #ccc;
}

.el-select .el-input__inner:active {
  background-color: #e3162b;
  color: white;
}

/* 复选框样式 */
.checkbox-container {
  margin-top: 10px;
  margin-left: 5px; /* 复选框对齐标签的位置 */
  zoom: 100%;
  color: #757575;
}

/* 勾选后的复选框样式 */
input[type="checkbox"]:checked {
  accent-color: #3080dd;/* 设置复选框勾选后的颜色 */
}


.checkbox-item {
  margin-bottom: 20px; /* 复选框之间的间距 */
}

.checkbox {
  margin-right: 10px; /* 复选框与标签的间距 */
  vertical-align: middle;
}

.module-label {
  font-size: 14px; /* 复选框标签字体大小 */
  vertical-align: middle;
}

/* 按钮样式 */
.button-container {
  text-align: right; /* 按钮靠右对齐 */
  margin-top: 20px; /* 按钮与表单的间距 */
  margin-bottom: 50px;
  margin-right: 120px;
  border-radius: 4px;
}

button {
  padding: 10px 20px;
  background-color: #007bff;
  color: #fff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  margin-right: 20px;
}

button:hover {
  background-color: #0056b3;
}

/* 弹窗标题居中 */
.custom-dialog-header {
  text-align: center;
  font-size: 18px;
  font-weight: normal;
  padding-bottom: 30px;
}

/* 自定义表单整体内边距 */
.custom-form {
  padding: 0 20px;
}


/* 自定义 label 的样式 */
.form-label {
  width: 80px;
}
/* 每个表单项的样式 */
.submit-form-group {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

/* 输入框和下拉框的宽度统一 */
.submit-input-field {
  width:350px;
  flex: 1;
  font-size: 14px;
  height: 30px;
  border: none;
  padding-left: 10px;
  padding-right: 30px;
}

</style>
