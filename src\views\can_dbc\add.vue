<template>
    <div class="add-container">
        <el-form :model="form" label-width="auto" :rules="rules" status-icon ref="formRef">

            <el-form-item label="所属项目" prop="project_number">
                <Projects v-model="form.project_number" :includePrefix="false" :includeAll="false" ref="projectRef" />
            </el-form-item>

            <el-form-item label="DBC文件" prop="file">
                <upload-item v-model="form.file" />
            </el-form-item>

            <el-form-item label="描述">
                <el-input type="textarea" :rows="3" v-model="form.description" placeholder="请输入描述"></el-input>
            </el-form-item>

            <div class="submit-button-container">
                <el-button type="default" @click="onCancel">取消</el-button>
                <el-button type="primary" @click="onConfirm">提交</el-button>
            </div>

        </el-form>
    </div>
</template>

<script setup>
import { ref } from 'vue';
import http from '@/utils/http/http.js';
import Projects from '@/components/projects.vue';
import { useProjectStore } from '@/stores/project.js';
import uploadItem from './upload-item.vue';
import { ElMessage } from 'element-plus';

const formRef = ref(null);
const projectRef = ref(null);
const projectStore = useProjectStore();

const form = ref({
    project_number: projectStore.project_info.projectCode || '',
    file: null,
    description: '',
});

const rules = ref({
    project_number: [
        { required: true, message: '请选择所属项目', trigger: 'change' },
    ],
    file: [
        { required: true, message: '请上传DBC文件', trigger: 'change' },
    ],
});

const emit = defineEmits(['confirm', 'cancel'])

const onConfirm = () => {
    formRef.value.validate(async (valid) => {
        if (valid) {
            let project_info = projectRef.value.getProjectInfo(form.value.project_number);

            const formData = new FormData();

            formData.append('project_id', project_info?.id);
            formData.append('project_name', project_info?.name);
            formData.append('project_number', form.value.project_number);
            formData.append('file', form.value.file);
            formData.append('description', form.value.description);

            http.post('/can_dbc', formData).then(res => {
                emit('confirm');
                ElMessage({
                    message: '提交成功',
                    type: 'success',
                });
            }).catch(err => {
                ElMessageBox.alert(err.response.data.msg, '提交失败', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'error',
                })
            });
        };
    });
};

const onCancel = () => {
    emit('cancel');
};

</script>


<style lang="scss" scoped>
.add-container {
    padding: 0 20px;
}

.submit-button-container {
    display: flex;
    justify-content: flex-end;
}
</style>