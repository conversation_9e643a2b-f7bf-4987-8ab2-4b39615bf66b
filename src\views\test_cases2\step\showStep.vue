<template>

    <ShowCanParams v-if="step_type == 'CAN'" :params="props.params" />
    <ShowI2cParams v-else-if="step_type == 'I2C'" :params="props.params" />
    <ShowLinParams v-else-if="step_type == 'LIN'" :params="props.params" />
    <ShowCustomCmdParams v-else-if="step_type == 'CUSTOM_CMD'" :params="props.params" />
    <ShowCanDbcParams v-else-if="step_type == 'CAN_DBC'" :params="props.params" />

</template>


<script setup>

import { computed } from 'vue';

import ShowCanParams from './can/f_show.vue';
import ShowI2cParams from './i2c/f_show.vue';
import ShowLinParams from './lin/f_show.vue';
import ShowCustomCmdParams from './customCmd/f_show.vue';
import ShowCanDbcParams from './can_dbc/f_show.vue';

const props = defineProps({
    type: {
        type: String,
        required: true,
    },
    params: {
        type: Object,
        required: true,
    },
});

const step_type = computed(() => {
    return props.type;
});

</script>


<style lang="scss" scoped>
.full-width {
    width: 100%;
}

.half-width {
    width: 50%;
}
</style>