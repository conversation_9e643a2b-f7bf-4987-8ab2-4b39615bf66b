<template>
  <!-- 唯一的 template 根元素，所有页面内容包裹在此处 -->
  <div class="sdk-mindmap-container" v-custom-loading="loading">
    <!-- 思维导图容器：确保设置明确的宽高，避免渲染后不可见 -->
    <div 
      id="mindMapContainer" 
      class="mindmap-wrapper"
    >
    
  </div>
      <div class="button">
          <router-link to="/code_management/sdk" >
              <el-button type="danger">返回</el-button>
          </router-link>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import MindMap from "simple-mind-map";
import http from '@/utils/http/http.js';

// 1. 响应式数据定义（初始值设为null，标记未加载状态）
const mindmapData = ref(null); // 存储接口返回的完整思维导图数据
const tableData = ref([]);     // 存储过滤后的子节点数据
let mindMap = null;            // 思维导图实例（非响应式）
// 加载状态
const loading = ref(true)
// 2. 节点过滤函数（递归处理子节点，兼容空数据）
function filterItems(items) {
  if (!items || !Array.isArray(items)) return [];
  
  return items
    .filter(item => true) // 可根据实际需求添加过滤条件（如过滤弃用节点）
    .map(item => ({
      ...item,
      children: filterItems(item.children) // 递归处理子节点
    }));
}

// 3. 数据处理核心函数（转换为思维导图格式）
function processData() {
  if (!mindmapData.value) return; // 数据未加载时直接退出
  
  // 提取一级子节点并过滤
  const filteredItems = filterItems(mindmapData.value.children || []);
  tableData.value = filteredItems;
  
  // 转换格式并更新思维导图
  updateMindMapData();
}

// 4. 思维导图数据更新函数（适配 simple-mind-map 格式）
function updateMindMapData() {
  if (!mindMap) return; // 实例未初始化时退出
  
  // 构建思维导图所需的结构（simple-mind-map 要求 data.text 为节点文本）
  const mindMapRoot = {
    data: { text: mindmapData.value.label || 'SDK(2.0.0)' }, // 根节点文本
    children: buildNodeTree(tableData.value)                 // 递归构建子节点
  };
  
  // 调用插件API更新数据（触发重新渲染）
  mindMap.updateData(mindMapRoot);
}

// 5. 递归构建节点树（适配插件的层级结构）
function buildNodeTree(nodes) {
  if (!nodes || !Array.isArray(nodes)) return [];
  
  return nodes.map(node => ({
    data: { text: node.label || '未命名参数' }, // 节点文本（取自label字段）
    children: buildNodeTree(node.children)      // 递归处理子节点
  }));
}

// 6. 思维导图实例初始化（确保容器存在且样式正常）
function initMindMap() {
  // 查找容器元素（确保模板中容器ID匹配）
  const container = document.getElementById('mindMapContainer');
  if (!container) {
    console.error('思维导图容器未找到，请检查模板中的ID是否为 "mindMapContainer"');
    return;
  }
  
  // 初始化插件实例（基础配置）
  mindMap = new MindMap({
    el: container, // 挂载容器
    // 初始占位数据（加载完成前显示）
    data: {
      data: { text: 'SDK(2.0.0)'},
      children: []
    },
    initRootNodePosition: ['5%', '50%'], // 根节点初始位置（左侧居中）
    // 样式配置（修复原代码中 borderColor 拼写错误）
    theme: 'classic4'
  });
  // 拦截节点新增事件
  // 保存原方法
    // 保存原始方法
    const originalExecCommand = mindMap.execCommand.bind(mindMap);

    // 重写插入子节点的方法
    mindMap.execCommand = function (name, ...args) {
      console.log('execCommand:', name, ...args);
      // 禁止新增子节点
      if (name === 'INSERT_CHILD_NODE') {
        console.info('禁止新增子节点');
        return null;
      }
      return originalExecCommand(name, ...args);
    };

  // 修改主题样式
  mindMap.setThemeConfig({
      lineColor: '#79bbff',
      lineWidth: 2,
      lineStyle: 'straight',
      paddingX: 10,
      paddingY: 10,
      root: {
          fillColor: '#79bbff',
          color: '#ffffff',
          borderColor: '#79bbff',
          borderWidth: 2,
          borderRadius: 8,
          fontSize: 16,
          marginX: 30,
          marginY: 20
        },
        second: {
          fillColor: '#ffffff',
          color: '#777777',
          borderColor: '#79bbff',
          borderWidth: 1,
          borderRadius: 6,
          fontSize: 14,
          marginX: 30,
          marginY: 20
        },
        node: {
          fillColor: '#ffffff',
          color: '#777777',
          borderColor: '#79bbff',
          borderWidth: 1,
          borderRadius: 6,
          fontSize: 13,
          marginX: 30,
          marginY: 20
        }
  })

}

// 7. 接口请求函数（获取思维导图数据）
function getMindmapData() {
  http.post("/code_management/mindmap_data")
    .then(res => {
      loading.value = false;
      console.log('接口响应数据:', res.data);
      // 验证数据格式（确保 status=1 且 mindmapData 存在）
      if (res.data?.status === 1 && res.data?.mindmapData) {
        mindmapData.value = res.data.mindmapData; // 更新响应式数据
      } else {
        console.error('接口返回数据格式错误:', res.data);
        mindmapData.value = { label: 'SDK(2.0.0)', children: [{ label: '数据加载失败' }] };
      }
    })
    .catch(err => {
      loading.value = false;
      console.error('思维导图数据请求失败:', err);
      // 错误状态下的降级数据
      mindmapData.value = { label: 'SDK(2.0.0)', children: [{ label: '接口请求失败，请重试' }] };
    });
}


// 8. 生命周期钩子：页面挂载时初始化
onMounted(() => {
  initMindMap();    // 第一步：先初始化思维导图实例（确保容器就绪）
  getMindmapData(); // 第二步：再请求接口数据（避免实例未就绪时数据已返回）
});

// 9. 监听数据变化：数据加载完成后自动更新思维导图
watch(
  mindmapData,       // 监听响应式数据 mindmapData
  (newData) => {     // newData 是更新后的值
    if (newData) {   // 只有当数据不为空时才处理
      console.log('检测到思维导图数据更新，开始渲染');
      processData(); // 触发数据处理和思维导图更新
    }
  },
  { deep: true }     // 深度监听：确保对象内部属性变化也能触发
);
</script>

<style scoped>
/* 为思维导图容器添加明确的样式，避免渲染后不可见 */
.sdk-mindmap-container {
  position: relative;
  width: 100%;
  height: 100vh; /* 占满视口高度，可根据实际需求调整 */
}

.mindmap-wrapper {
  width: 100%;
  height: 100%;
}

.button {
  position: absolute; /* 脱离文档流，固定在定位容器右上角 */
  top: 50px; /* 距离定位容器顶部的距离，可调整 */
  right: 50px; /* 距离定位容器右侧的距离，可调整 */
  z-index: 999; /* 提高层级，确保不被思维导图遮挡 */
}
</style>
