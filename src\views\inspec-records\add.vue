<template>
    <el-divider />
    <div class="add-container">
        <el-form :model="form" label-width="auto" :rules="rules" status-icon ref="formRef">

            <el-form-item label="点检人员" prop="person_id">
                <el-select v-model="form.person_id" placeholder="请选择点检人员">
                    <el-option v-for="person in personList" :key="person.id" :label="person.user_name" :value="person.id" />
                </el-select>
            </el-form-item>
 
            <div class="submit-button-container">
                <el-button type="default" @click="onCancel">取消</el-button>
                <el-button type="primary" @click="onSubmit">提交</el-button>
            </div>

        </el-form>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';

import http from '@/utils/http/http.js';

const formRef = ref(null);
const personList = ref([]);

const form = ref({
    person_id: '',
});

const rules = ref({
    person_id: [
        { required: true, message: '请选择点检人员', trigger: 'blur' },
    ],
});

const emit = defineEmits(['confirm', 'cancel'])

const onSubmit = () => {
    formRef.value.validate(async (valid) => {
        if (valid) {
            let data = {
                ...form.value,
            };

            http.post('/inspecs/tasks', data).then(res => {
                emit('confirm');
            }).catch(err => {
                ElMessageBox.alert(err.response.data.msg, '提交失败', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'error',
                })
            });
        };
    });
};

const onCancel = () => {
    emit('cancel');
};

onMounted(() => {
    http.get('/inspecs/persons', { params: { pagesize: 10000 } }).then(res => {
        let items = res.data.data.results;
        personList.value = items;
    });
   
});

</script>


<style lang="scss" scoped>
.add-container {
    padding: 0 20px;
}

.submit-button-container {
    display: flex;
    justify-content: flex-end;
}
</style>