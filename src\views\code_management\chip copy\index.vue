<template>
  <!-- 提示弹框 -->
  <TipDialog v-if="showTipDialog" />
  <div v-else v-custom-loading="loading">
    <!-- 顶部工具栏组件 -->
    <Toolbar
      :sdk-version="SdkVersion"
      :form="form"
      :space-options="spaceOptions"
      :branch-options="branchOptions"
      :loading="loading"
      :has-edit-permission="hasEditPermission"
      @update:form="(newForm) => Object.assign(form, newForm)"
      @gitlab-click="handleGitlabClick"
      @download="handleDownload"
      @save="handleSave"
      @publish="handlePublish"
      @merge-test="handlemerge"
      @confirm-action="confirmAction"
    />
      <!-- 主要内容区域 -->
      <div class="main-content">
        <!-- 左侧容器 -->
        <div class="left-container">
          <!-- 左侧第一行：分为两列 -->
          <div class="left-row-1">
            <!-- 芯片配置组件 - 占30% -->
            <div class="chip-config-section">
              <ChipConfig
                v-model:chip-form="chipForm"
                :chip-options="chipOptions"
                :chip-options-loading="chipOptionsLoading"
                @chip-model-change="handleChipModelChange"
                @vcc-voltage-change="handleVccVoltageChange"
                @clock-freq-change="handleClockFreqChange"
              />
            </div>
            <!-- 右侧80%区域 - 芯片预览 -->
            <div class="other-config-section">
              <ChipPreview
                :pin-count="chipPreviewData.pinCount"
                :chip-model="chipPreviewData.chipModel"
                :pins="chipPreviewData.pins"
                :type-style-config="chipPreviewData.typeStyleConfig"
                :highlighted-pin="highlightedPin"
                @pin-click="handlePinClick"
                @pin-edit="handlePinEdit"
              />
            </div>
          </div>

          <!-- 左侧第二行 -->
          <div class="left-row-2">
            <PinConfigTable
              :pin-table-data="pinTableData"
              :search-key="searchKey"
              :io-config="ioConfig"
              :current-page="currentPage"
              :page-size="pageSize"
              :available-io-types="availableIoTypes"
              :type-style-config="chipPreviewData.typeStyleConfig"
              @update:search-key="searchKey = $event"
              @update:io-config="ioConfig = $event"
              @update:current-page="currentPage = $event"
              @update:page-size="pageSize = $event"
              @row-click="handlePinTableRowClick"
              @edit-pin="handlePinEdit"
            />
          </div>
        </div>

        <!-- 右侧容器 -->
        <div class="right-container">
          <!-- 请求状态指示器 -->
          <!-- <div v-if="isChipChangeRequesting" class="request-status-indicator">
            <el-alert
              title="配置修改中..."
              type="info"
              :description="`正在修改引脚${currentRequestInfo?.pinId}的${currentRequestInfo?.fieldLabel}，请等待完成后再进行其他修改`"
              show-icon
              :closable="false"
            />
          </div> -->


          <PinEditorContainer
            ref="pinEditorRef"
            :pinData="pinEditorData"
            :pinInfo="pinEditorData.pinInfoData"
            :allPinsData="pinTableData"
            :typeInfo="typeInfo"
            :projectCode="project_code"
            :projectName="project_name"
            :gitlab="form.gitlab"
            :projectBranch="form.project_branch"
            :branchStatus="branch_status"
            :chipName="chipForm.chipModel"
            :chip="chip"
            :workspacePath="workspace"
            :highlightedPin="highlightedPin"
            :hasEditPermission="hasEditPermission"
            @field-change="handleFieldChange"
            @module-change="handleModuleChange"
            @module-config-change="handleModuleConfigChange"
            @pin-selection-change="handlePinSelectionChange"
            @pin-data-loaded="handlePinDataLoaded"
            @chip-preview-update="handleChipPreviewUpdate"
            @pin-table-update="handlePinTableUpdate"
            @update-pin-type="handleUpdatePinType"
            @update-related-pin-type="handleUpdateRelatedPinType"
            @refresh-chip-data="handleRefreshChipData"
            />

        </div>
      </div>
  </div>
          

</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted, watch } from 'vue';
import http from '@/utils/http/http';
import { ElMessageBox } from 'element-plus';
import { Loading } from '@element-plus/icons-vue';
import messageManager from '@/utils/messageManager';
import { useProjectStore } from '@/stores/project.js';
import Toolbar from '@/views/code_management/top/Toolbar.vue';
import ChipConfig from './components/ChipConfig.vue';
import ChipPreview from './components/ChipPreview.vue';
import PinConfigTable from './components/PinConfigTable.vue';
import PinEditorContainer from './components/PinEditorContainer.vue';   
import TipDialog from '../None.vue';
const projectStore = useProjectStore();

// 项目相关变量
let project_code = '';
let project_name = '';

const form = reactive({
  gitlab: '',
  project_branch: ''
});
const spaceOptions = ref([]);
const branchOptions = ref([]);
const branch_create = ref(true);

// 工作空间和分支状态
const workspace = ref('');
const branch_status = ref('');
let workspace_path = '';

// 引脚功能类型缓存
const pinModuleTypeCache = ref({});


// 初始化时从全局状态恢复数据
const initializeFromStore = () => {
  const codeManagement = projectStore.codeManagement;
  form.gitlab = codeManagement.gitlab || '';
  form.project_branch = codeManagement.project_branch || '';
  spaceOptions.value = codeManagement.spaceOptions || [];
  branchOptions.value = codeManagement.branchOptions || [];
  SdkVersion.value = codeManagement.sdkVersion || '';
};

const SdkVersion = ref('');
const loading = ref(false);
const showTipDialog = ref(true);

// 芯片相关数据
const chip = ref(null);
const color = ref([]);
const io = ref([]);
const pinCount = ref(0);
const table = ref([]) // 🎯 存储原始table数据，用于model字段绑定
const typeInfo = ref({}) // 🎯 存储引脚类型信息数据
const chipOptions = ref([]);
const chipOptionsLoading = ref(false);
const chipForm = reactive({
  chipModel: '',
  vccVoltage: 3.3,
  clockFreq: 160,
  ioConfig: []
});

// 芯片预览相关数据
const highlightedPin = ref(null);
const chipPreviewData = reactive({
  pinCount: 0,
  chipModel: '',
  pins: [],
  typeStyleConfig: {}
});

// 权限控制 - 基于branch_create状态
const hasEditPermission = computed(() => branch_create.value);

// PinConfigTable 组件所需数据
const pinTableData = ref([]);
const searchKey = ref('');
const ioConfig = ref([]);
const currentPage = ref(1);
const pageSize = ref(10);
const availableIoTypes = ref([]);

// 引脚功能类型缓存 - 存储每个引脚的功能类型选择
const pinTypeCache = ref({});

// 引脚配置缓存 - 以引脚号为主键，缓存每个引脚的完整配置信息
const pinConfigCache = ref({});

// 模块配置缓存 - 以"引脚ID_功能类型"为键，缓存对应的模块配置信息
const moduleConfigCache = ref({});

// 引脚完整状态缓存 - 保存每个引脚的完整配置状态（功能类型 + 模块配置 + 表单数据）
const pinCompleteStateCache = ref({});

// 引脚编辑器数据
const pinEditorData = ref({});
const pinEditorLoading = ref(false);

// 引脚编辑器组件引用
const pinEditorRef = ref(null);

// 仓库信息
const repositoryInfo = computed(() => ({
  project_code: project_code,
  project_name: project_name,
  project_gitlab: form.gitlab,
  project_branch: form.project_branch
}));







// 单击仓库
const handleGitlabClick = () => {
  // 判断有无存在的ElMessage， 存在则关闭
  if (!project_code) {
    messageManager.warning('请选择项目！')
  }
}

// 芯片配置事件处理函数
const handleChipModelChange = (value) => {
  console.log('芯片型号变更:', value);
  chipForm.chipModel = value;
};

const handleVccVoltageChange = (value) => {
  console.log('VCC电压变更:', value);
  chipForm.vccVoltage = value;
};

const handleClockFreqChange = (value) => {
  console.log('时钟频率变更:', value);
  chipForm.clockFreq = value;
};

// 监听 chipForm 变化（调试用）
watch(chipForm, (newForm, oldForm) => {
  console.log('父组件: chipForm 变化:', newForm);

  // 如果芯片型号发生变化，清除所有缓存
  if (oldForm && newForm.chipModel !== oldForm.chipModel) {
    console.log('🧹 芯片型号变化，清除所有功能类型缓存');
    pinModuleTypeCache.value = {};
    pinTypeCache.value = {};
  }

  // 同步更新芯片预览的芯片型号
  if (newForm.chipModel) {
    chipPreviewData.chipModel = newForm.chipModel;
  }
}, { deep: true });

// 引脚相关事件处理函数
const handlePinClick = async (pinId) => {
  console.log('引脚点击:', pinId);

  highlightedPin.value = pinId;

  // 调用 PinEditorContainer 的方法获取引脚数据
  if (pinEditorRef.value) {
    await pinEditorRef.value.getPinEditorData(pinId);
  }
};

const handlePinEdit = async (pinData) => {
  console.log('引脚编辑事件触发:', pinData);

  const pinId = pinData.pinId || pinData.pinNumber;

  // 清除历史高亮效果，设置当前引脚高亮
  highlightedPin.value = pinId;

  console.log('设置引脚高亮 - 引脚ID:', pinId, '类型:', typeof pinId);
  console.log('当前highlightedPin值:', highlightedPin.value);

  // 调用 PinEditorContainer 的方法获取引脚数据
  if (pinEditorRef.value) {
    await pinEditorRef.value.getPinEditorData(pinId);
  }
};

const handlePinTableRowClick = async (rowData) => {
  console.log('表格行点击:', rowData);

  const pinId = rowData.pinId || rowData.pinNumber;

  highlightedPin.value = pinId;

  console.log('表格行点击 - 引脚ID:', pinId, '类型:', typeof pinId);
  console.log('当前highlightedPin值:', highlightedPin.value);

  // 调用 PinEditorContainer 的方法获取引脚数据
  if (pinEditorRef.value) {
    await pinEditorRef.value.getPinEditorData(pinId);
  }
};

// 清除引脚高亮
const clearPinHighlight = () => {
  highlightedPin.value = null;
  console.log('清除引脚高亮');
};

// 处理引脚数据加载完成事件
const handlePinDataLoaded = (data) => {
  console.log('🎯 引脚数据加载完成:', data);

  const { pinId, pinData, pinInfo, moduleOptions } = data;

  // 🎯 完全清空引脚编辑器数据，确保不保留上一个引脚的字段
  Object.keys(pinEditorData.value).forEach(key => {
    delete pinEditorData.value[key];
  });

  // 🎯 明确清空type字段，确保不被其他引脚数据污染
  pinEditorData.value.type = '';

  // 更新引脚编辑器数据
  Object.assign(pinEditorData.value, pinData);

  // 🎯 再次确保type字段为空，将由模块配置请求设置
  pinEditorData.value.type = '';

  // 同步更新芯片预览和引脚配置表的数据
  if (pinData.pinType && pinData.pinType !== '') {
    console.log(`同步更新引脚 ${pinId} 的类型为: ${pinData.pinType}`);

    // 更新芯片预览中的引脚颜色和类型
    updateChipPreviewPinColor(pinId, pinData.pinType);

    // 更新引脚配置表中的数据
    updatePinConfigTableData(pinId, pinData.pinType);
  }
};

// 处理芯片预览更新事件
const handleChipPreviewUpdate = (data) => {
  console.log('🎯 芯片预览更新事件:', data);

  // 如果有多个引脚更新信息
  if (data.pinUpdates && Array.isArray(data.pinUpdates)) {
    data.pinUpdates.forEach(pinUpdate => {
      console.log(`🔄 更新芯片预览中的引脚 ${pinUpdate.pinId}:`, pinUpdate);

      // 更新芯片预览中的引脚显示
      updateChipPreviewPinColor(pinUpdate.pinId, pinUpdate.type);
    });
  } else {
    // 兼容旧的单引脚更新格式
    updateChipPreviewPinColor(data.pinId, data.moduleType);
  }
};

// 处理引脚表格更新事件
const handlePinTableUpdate = (data) => {
  console.log('🎯 引脚表格更新事件:', data);

  // 如果有多个引脚更新信息
  if (data.pinUpdates && Array.isArray(data.pinUpdates)) {
    data.pinUpdates.forEach(pinUpdate => {
      console.log(`🔄 更新引脚表格中的引脚 ${pinUpdate.pinId}:`, pinUpdate);

      // 更新引脚表格中的引脚信息
      updatePinConfigTableData(pinUpdate.pinId, pinUpdate.type, pinUpdate.channelName);
    });
  } else {
    // 兼容旧的单引脚更新格式
    updatePinConfigTableData(data.pinId, data.moduleType, data.channelName || '');
  }
};

// 处理单个引脚类型更新事件
const handleUpdatePinType = (data) => {
  console.log('🎯 单个引脚类型更新事件:', data);

  const { pinId, newType, functionType, channelName } = data;

  // 更新芯片预览
  updateChipPreviewPinColor(pinId, newType);

  // 更新引脚表格
  updatePinConfigTableData(pinId, newType, channelName || '');

  // 缓存引脚类型
  cachePinModuleType(pinId, functionType);
};

// 🎯 处理刷新芯片数据事件
const handleRefreshChipData = async (data) => {
  console.log('🔄 收到刷新芯片数据请求:', data);

  const { reason, affectedPins, moduleType } = data;

  try {
    console.log(`🔄 开始刷新芯片数据 - 原因: ${reason}`);
    console.log(`🔄 影响的引脚: [${affectedPins.join(', ')}]`);
    console.log(`🔄 模块类型: ${moduleType}`);

    // 🎯 重新请求芯片信息（包含芯片预览和引脚配置表数据）
    console.log('🔄 重新请求芯片信息数据...');
    await get_chipinfo();

    console.log('✅ 芯片数据刷新完成');
    // messageManager.success('芯片数据已刷新');

  } catch (error) {
    console.error('❌ 刷新芯片数据失败:', error);
    messageManager.error('刷新芯片数据失败: ' + error.message);
  }
};

// 缓存引脚的功能类型
const cachePinModuleType = (pinId, moduleType) => {
  if (pinId && moduleType && moduleType !== '') {
    pinModuleTypeCache.value[pinId] = moduleType;
    console.log(`💾 缓存引脚 ${pinId} 的功能类型: ${moduleType}`);
    console.log('📋 当前功能类型缓存状态:', pinModuleTypeCache.value);
  }
};

// 获取引脚的缓存功能类型
const getCachedPinModuleType = (pinId) => {
  if (pinId && pinModuleTypeCache.value[pinId]) {
    const cachedType = pinModuleTypeCache.value[pinId];
    console.log(`📖 从缓存获取引脚 ${pinId} 的功能类型: ${cachedType}`);
    return cachedType;
  }
  console.log(`ℹ️ 引脚 ${pinId} 没有缓存的功能类型`);
  return null;
};

// 清除引脚的缓存功能类型
const clearCachedPinModuleType = (pinId) => {
  if (pinId && pinModuleTypeCache.value[pinId]) {
    delete pinModuleTypeCache.value[pinId];
    console.log(`🗑️ 清除引脚 ${pinId} 的功能类型缓存`);
  }
};

// 缓存管理函数
// 保存引脚配置到缓存
const savePinConfigToCache = (pinId, configData) => {
  console.log(`💾 保存引脚 ${pinId} 配置到缓存:`, configData);
  pinConfigCache.value[pinId] = {
    ...configData,
    lastUpdated: Date.now()
  };
  console.log(`📦 当前引脚配置缓存状态:`, Object.keys(pinConfigCache.value));
};

// 从缓存获取引脚配置
const getPinConfigFromCache = (pinId) => {
  const cached = pinConfigCache.value[pinId];
  if (cached) {
    console.log(`📖 从缓存获取引脚 ${pinId} 配置:`, cached);
    return cached;
  }
  console.log(`📖 引脚 ${pinId} 无缓存配置`);
  return null;
};

// 保存模块配置到缓存（按引脚+功能类型组合）
const saveModuleConfigToCache = (pinId, moduleType, configData) => {
  const cacheKey = `${pinId}_${moduleType}`;
  console.log(`💾 保存引脚 ${pinId} 的模块类型 ${moduleType} 配置到缓存:`, configData);
  moduleConfigCache.value[cacheKey] = {
    config: configData,
    pinId: pinId,
    moduleType: moduleType,
    lastUpdated: Date.now()
  };
  console.log(`📦 当前模块配置缓存键:`, Object.keys(moduleConfigCache.value));
};

// 从缓存获取模块配置（按引脚+功能类型组合）
const getModuleConfigFromCache = (pinId, moduleType) => {
  const cacheKey = `${pinId}_${moduleType}`;
  const cached = moduleConfigCache.value[cacheKey];
  if (cached && cached.config && Array.isArray(cached.config) && cached.config.length > 0) {
    console.log(`📖 从缓存获取引脚 ${pinId} 的模块类型 ${moduleType} 配置:`, cached.config);
    return cached.config;
  }
  console.log(`📖 引脚 ${pinId} 的模块类型 ${moduleType} 无有效缓存配置`);
  return null;
};

// 清除特定引脚+功能类型的模块配置缓存
const clearModuleConfigCache = (pinId, moduleType) => {
  const cacheKey = `${pinId}_${moduleType}`;
  if (moduleConfigCache.value[cacheKey]) {
    delete moduleConfigCache.value[cacheKey];
    console.log(`🗑️ 已清除引脚 ${pinId} 的模块类型 ${moduleType} 缓存`);
  }
};

// 清除所有缓存（用于测试）
const clearAllCache = () => {
  pinConfigCache.value = {};
  moduleConfigCache.value = {};
  pinTypeCache.value = {};
  pinCompleteStateCache.value = {};
  console.log('🗑️ 已清除所有缓存');
};

// 保存引脚完整状态到缓存
const savePinCompleteState = (pinId) => {
  if (!pinEditorData.value || !pinId) {
    console.log('⚠️ 无法保存引脚状态：缺少数据或引脚ID');
    return;
  }

  const currentState = {
    pinId: pinId,
    pinName: pinEditorData.value.pinName || `PIN${pinId}`,
    pinType: pinEditorData.value.pinType || '',
    moduleConfig: pinEditorData.value.moduleConfig || [],
    formData: { ...pinEditorData.value }, // 保存完整的表单数据
    lastUpdated: Date.now(),
    isConfigured: !!(pinEditorData.value.pinType && pinEditorData.value.pinType !== '')
  };

  pinCompleteStateCache.value[pinId] = currentState;
  console.log(`💾 保存引脚 ${pinId} 完整状态:`, currentState);
  console.log(`📦 当前已缓存的引脚:`, Object.keys(pinCompleteStateCache.value));
};

// 从缓存恢复引脚完整状态
const restorePinCompleteState = (pinId) => {
  const cachedState = pinCompleteStateCache.value[pinId];
  if (cachedState && cachedState.isConfigured) {
    console.log(`📖 恢复引脚 ${pinId} 完整状态:`, cachedState);
    return cachedState;
  }
  console.log(`📖 引脚 ${pinId} 无已配置的缓存状态`);
  return null;
};

// 显示缓存状态（用于调试）
const showCacheStatus = () => {
  console.log('📊 缓存状态报告:');
  console.log('  - 引脚配置缓存:', Object.keys(pinConfigCache.value));
  console.log('  - 模块配置缓存 (引脚ID_功能类型):', Object.keys(moduleConfigCache.value));
  console.log('  - 引脚类型缓存:', Object.keys(pinTypeCache.value));
  console.log('  - 引脚完整状态缓存:', Object.keys(pinCompleteStateCache.value));

  // 详细显示模块配置缓存
  if (Object.keys(moduleConfigCache.value).length > 0) {
    console.log('📋 模块配置缓存详情:');
    Object.entries(moduleConfigCache.value).forEach(([key, value]) => {
      console.log(`    ${key}: ${value.config?.length || 0} 个配置字段`);
    });
  }

  // 详细显示引脚完整状态缓存
  if (Object.keys(pinCompleteStateCache.value).length > 0) {
    console.log('📋 引脚完整状态缓存详情:');
    Object.entries(pinCompleteStateCache.value).forEach(([pinId, state]) => {
      console.log(`    引脚${pinId}: ${state.pinType || '未配置'} (${state.isConfigured ? '已配置' : '未配置'})`);
    });
  }
};

// 处理功能类型变化
const handleModuleChange = async (moduleData) => {
  console.log('主组件 - 功能类型变化:', moduleData);
  console.log('当前芯片预览数据:', chipPreviewData);
  // console.log('当前引脚配置表数据:', pinTableData.value);

  const { pinId, newModule, formData } = moduleData;

  if (!pinId) {
    console.error('pinId为空，无法更新引脚信息');
    return;
  }

  // 允许newModule为空，这表示清空功能类型
  if (newModule === undefined || newModule === null) {
    console.error('newModule为undefined或null，无法更新引脚信息');
    return;
  }

  if (newModule && newModule !== '') {
    console.log(`✅ 开始更新引脚 ${pinId} 的类型为: ${newModule}`);
  } else {
    console.log(`🧹 开始清空引脚 ${pinId} 的功能类型`);
  }

  // 0. 缓存引脚的功能类型选择
  pinTypeCache.value[pinId] = newModule;
  console.log(`🔄 缓存引脚 ${pinId} 的功能类型: ${newModule}`);
  console.log('📦 当前完整缓存状态:', JSON.stringify(pinTypeCache.value, null, 2));

  // 0.1 同时缓存到新的功能类型缓存中
  cachePinModuleType(pinId, newModule);

  // 0.1 缓存引脚的基本配置信息
  if (formData) {
    savePinConfigToCache(pinId, {
      pinId: pinId,
      pinName: formData.pin_name || `PIN${pinId}`,
      pinType: newModule,
      formData: { ...formData },
      moduleType: newModule
    });
  }

  // 1. 更新芯片预览中对应引脚的颜色
  updateChipPreviewPinColor(pinId, newModule);

  // 2. 更新引脚配置表中的数据
  updatePinConfigTableData(pinId, newModule);

  // 3. 更新当前引脚信息
  updateCurrentPinInfo(pinId, newModule);

  // 🎯 4. 更新table中的model值，实现与功能类型的绑定
  updateTableModelValue(pinId, newModule);

  // 注意：模块配置请求现在由DynamicPinEditor组件在功能类型有值时自动触发

  // 保存当前引脚的完整状态
  savePinCompleteState(pinId);

  console.log('所有更新完成');
};

// 处理字段变化（简化版本）
const handleFieldChange = (fieldData) => {
  console.log('🔄 index.vue - 收到字段变化事件:', fieldData);

  // 兼容旧的调用方式
  if (typeof fieldData === 'string') {
    const fieldKey = fieldData;
    console.log('🎯 index.vue - 字段变化:', fieldKey);
    return;
  }

  // 新的调用方式
  const { pinId, fieldKey, fieldValue } = fieldData;
  console.log(`🎯 引脚 ${pinId} 字段 ${fieldKey} 变化为: ${fieldValue}`);

  // 功能类型字段变化由子组件直接处理
  if (fieldKey === 'module') {
    console.log('🎯 功能类型字段变化，由子组件直接处理');
  }
};


const handleModuleConfigChange = async (configChangeData) => {
  console.log('🔄 收到模块配置变化事件:', configChangeData);

  const {
    pinId,
    pinName,
    moduleType,
    fieldKey,
    fieldValue,
    fieldLabel,
    formData
  } = configChangeData;

  // ✅ 检查是否有正在进行的请求
  if (isChipChangeRequesting.value) {
    console.warn('⚠️ 有正在进行的配置修改请求，拒绝新请求');
    const currentInfo = currentRequestInfo.value;
    const currentDesc = currentInfo ?
      `引脚${currentInfo.pinId}的${currentInfo.fieldLabel}` :
      '配置修改';

    // messageManager.warning(`上一条修改（${currentDesc}）没有变更完成，请等待上条修改完成后，再进行更改`);

    // 回退当前修改
    revertField(pinId, fieldKey);
    return;
  }

  // ✅ 参数校验
  if (!pinId || !moduleType || !fieldKey || fieldValue === undefined) {
    console.warn('⚠️ 模块配置变化参数不完整，跳过处理');
    return;
  }

  // ✅ 权限校验
  if (branch_create.value === "false" || branch_create.value === false) {
    console.warn('⚠️ 没有权限修改配置，回退修改');
    messageManager.error('无编辑权限，自动回退数据');
    revertField(pinId, fieldKey);
    return;
  }

  // ✅ 工作区、分支、芯片等必要上下文检查
  if (!workspace.value || branch_status.value === null || !chipForm.chipModel) {
    console.warn('⚠️ 缺少必要上下文，跳过配置变化处理');
    return;
  }

  // ✅ 提取通道名称
  const channelName = (formData?.name?.trim()) || "";
  if (!channelName) {
    console.log('⚠️ 通道名称为空，使用字段标签作为默认值:', fieldLabel);
  }
  console.log('🎯 最终通道名称:', channelName);

  // ✅ 构造请求参数
  const requestParams = {
    project_code: project_code,
    project_name: project_name,
    project_gitlab: form.gitlab,
    project_branch: form.project_branch,
    workspace_path: workspace.value,
    branch_status: branch_status.value,
    chip_name: chipForm.chipModel,
    pin_number: pinId,
    function_type: moduleType,
    name: channelName,
    change_label: fieldLabel,
    change_value: fieldValue
  };

  // ✅ 设置请求状态
  isChipChangeRequesting.value = true;
  currentRequestInfo.value = {
    pinId,
    fieldKey,
    fieldLabel,
    moduleType,
    timestamp: Date.now()
  };

  // ✅ 设置超时保护，防止请求状态永远不被清除（30秒超时）
  const timeoutId = setTimeout(() => {
    if (isChipChangeRequesting.value) {
      console.warn('⚠️ 请求超时保护触发，强制清除请求状态');
      isChipChangeRequesting.value = false;
      currentRequestInfo.value = null;
      messageManager.warning('请求超时，已重置状态');
    }
  }, 30000);

  try {
    console.log('🚀 发送配置变化请求:', requestParams);
    console.log('🔒 设置请求状态为进行中');

    const response = await http.get('/code_management/chip_change', {
      params: requestParams,
      timeout: 15000
    });

    if (response.data?.status === 1) {
      console.log('✅ 配置修改成功');
      messageManager.success('配置修改成功');

      // ✅ 配置修改成功后，缓存引脚的功能类型
      if (moduleType && moduleType !== '' && moduleType !== 'OTHER') {
        cachePinModuleType(pinId, moduleType);
      }
    } else {
      const msg = response.data?.message || '未知错误';
      console.warn('⚠️ 配置修改失败:', msg);
      messageManager.error(`配置修改失败: ${msg}，已自动回退数据`);
      revertField(pinId, fieldKey);
    }

  } catch (error) {
    let errorMessage = '配置修改失败';

    if (error.code === 'ECONNABORTED') {
      errorMessage = '请求超时，配置修改失败';
    } else if (error.response) {
      errorMessage = `配置修改失败: ${error.response.data?.message || error.response.statusText}`;
    } else if (error.message) {
      errorMessage = `配置修改失败: ${error.message}`;
    }

    console.error('❌ 请求失败:', errorMessage);
    messageManager.error(errorMessage + '，已自动回退数据');
    revertField(pinId, fieldKey);
  } finally {
    // ✅ 清除超时定时器
    clearTimeout(timeoutId);

    // ✅ 无论成功还是失败，都要清除请求状态
    console.log('🔓 清除请求状态');
    isChipChangeRequesting.value = false;
    currentRequestInfo.value = null;
  }
};



// 处理更新相关引脚功能类型的事件
const handleUpdateRelatedPinType = (data) => {
  const { pinId, newType } = data;

  // 0. 缓存引脚的功能类型选择
  pinTypeCache.value[pinId] = newType;
  console.log(`🔄 缓存引脚 ${pinId} 的功能类型: ${newType}`);
  console.log('📦 当前完整缓存状态:', JSON.stringify(pinTypeCache.value, null, 2));

  // 0.1 同时缓存到新的功能类型缓存中
  cachePinModuleType(pinId, newType);

  // 0.1 缓存引脚的基本配置信息
  const cachedConfig = getPinConfigFromCache(pinId);
  if (cachedConfig) {
    savePinConfigToCache(pinId, {
      ...cachedConfig,
      pinType: newType,
      moduleType: newType
    });
  }

  // 1. 更新芯片预览中对应引脚的颜色
  updateChipPreviewPinColor(pinId, newType);

  // 2. 更新引脚配置表中的数据
  updatePinConfigTableData(pinId, newType);

  // 3. 更新当前引脚信息
  updateCurrentPinInfo(pinId, newType);

  // 🎯 4. 更新table中的model值，实现与功能类型的绑定
  updateTableModelValue(pinId, newType);
};



// ✅ 提取公共回退逻辑
const revertField = (pinId, fieldKey) => {
  if (pinEditorData.value?.pinId?.toString() === pinId?.toString()) {
    pinEditorData.value.shouldRevert = true;
    pinEditorData.value.revertFieldKey = fieldKey;
    pinEditorData.value = { ...pinEditorData.value }; // 强制更新
    console.log(`✅ 已设置回退标记字段: ${fieldKey}`);
  } else {
    console.warn('⚠️ 无法设置回退标记，pinEditorData 或 pinId 不匹配');
  }
};

// 🎯 处理引脚选择变化（简化版本）
const handlePinSelectionChange = (selectionData) => {
  console.log('🎯 主组件 - 收到引脚选择变化事件:', selectionData);

  const { pinId, moduleType, selectedPins } = selectionData;

  console.log(`🎯 当前引脚: ${pinId}, 功能类型: ${moduleType}`);
  console.log(`🎯 选中的引脚: [${selectedPins?.join(', ') || '无'}]`);

  // 这里可以添加具体的处理逻辑，如批量设置引脚功能类型等
};


// 更新芯片预览中引脚颜色
const updateChipPreviewPinColor = (pinId, newModule) => {
  if (chipPreviewData.pins && chipPreviewData.pins.length > 0) {
    const pinIndex = chipPreviewData.pins.findIndex(pin =>
      pin.pinId?.toString() === pinId?.toString()
    );

    if (pinIndex !== -1) {
      // 确定最终的类型值
      const finalType = (newModule && newModule !== '') ? newModule : '未配置';

      // 创建新的引脚对象来确保响应式更新
      const updatedPin = {
        ...chipPreviewData.pins[pinIndex],
        type: finalType,
        pinType: finalType,
        displayType: finalType
      };

      // 使用数组替换来触发响应式更新
      chipPreviewData.pins.splice(pinIndex, 1, updatedPin);

      if (newModule && newModule !== '') {
        console.log(`✅ 更新芯片预览引脚 ${pinId} 类型为: ${newModule}`);
      } else {
        console.log(`🧹 清空芯片预览引脚 ${pinId} 类型，设置为: 未配置`);
      }
      console.log('更新后的引脚数据:', updatedPin);
    } else {
      console.warn(`⚠️ 未找到引脚 ${pinId} 在芯片预览数据中`);
    }
  } else {
    console.warn('⚠️ 芯片预览数据为空或未初始化');
  }
};

// 更新引脚配置表数据
const updatePinConfigTableData = (pinId, newModule, channelName = '') => {
  if (pinTableData.value && pinTableData.value.length > 0) {
    const pinIndex = pinTableData.value.findIndex(pin =>
      pin.pinId?.toString() === pinId?.toString()
    );

    if (pinIndex !== -1) {
      // 更新引脚类型和状态
      if (newModule && newModule !== '') {
        pinTableData.value[pinIndex].pinType = newModule;
        pinTableData.value[pinIndex].status = '已配置';

        // 如果有通道名称，也更新通道名称
        if (channelName && channelName.trim() !== '') {
          pinTableData.value[pinIndex].channelName = channelName;
          console.log(`✅ 更新引脚配置表引脚 ${pinId} 类型为: ${newModule}, 状态为: 已配置, 通道名称: ${channelName}`);
        } else {
          console.log(`✅ 更新引脚配置表引脚 ${pinId} 类型为: ${newModule}, 状态为: 已配置`);
        }
      } else {
        pinTableData.value[pinIndex].pinType = '未配置';
        pinTableData.value[pinIndex].status = '未配置';
        console.log(`🧹 更新引脚配置表引脚 ${pinId} 类型为: 未配置, 状态为: 未配置`);
      }
    } else {
      console.warn(`⚠️ 未找到引脚 ${pinId} 在引脚配置表中`);
    }
  } else {
    console.warn('⚠️ 引脚配置表数据为空或未初始化');
  }
};

// 更新当前引脚信息
const updateCurrentPinInfo = (pinId, newModule) => {
  if (pinEditorData.value && pinEditorData.value.pinId?.toString() === pinId?.toString()) {
    pinEditorData.value.pinType = newModule;
    console.log(`更新当前引脚信息 ${pinId} 类型为: ${newModule}`);
  }
};

// 🎯 更新table中的model值
const updateTableModelValue = (pinId, newModule) => {
  if (table.value && Array.isArray(table.value)) {
    const pinIndex = table.value.findIndex(pin =>
      pin.pin_id?.toString() === pinId?.toString()
    );

    if (pinIndex !== -1) {
      // 更新table中对应引脚的model值
      table.value[pinIndex].model = newModule;
      console.log(`🎯 更新table中引脚 ${pinId} 的model值为: ${newModule}`);
      console.log(`🎯 更新后的引脚数据:`, table.value[pinIndex]);
    } else {
      console.warn(`⚠️ 未在table中找到引脚 ${pinId}`);
    }
  }
};

// 🎯 从table中获取引脚的model值
const getTableModelValue = (pinId) => {
  if (table.value && Array.isArray(table.value)) {
    const pinData = table.value.find(pin =>
      pin.pin_id?.toString() === pinId?.toString()
    );

    if (pinData) {
      const modelValue = pinData.model;
      console.log(`🎯 从table获取引脚 ${pinId} 的model值: ${modelValue}`);
      return modelValue;
    }
  }
  console.log(`🎯 未在table中找到引脚 ${pinId} 的model值`);
  return null;
};



// 注意：getPinEditorData 函数已移动到 PinEditorContainer 组件中

// 监听窗口大小变化（保留用于未来扩展）
const handleResize = () => {
  // 可以在这里添加响应式布局逻辑
};

// 组件挂载时初始化数据
onMounted(async () => {
  try {
    loading.value = true;

    // 从全局状态初始化数据
    initializeFromStore();

    // 监控项目信息变化
    const info = projectStore.project_info || {};
    project_code = info.projectCode || '';
    project_name = info.name || '';

    console.log('project_code:', project_code);
    console.log('project_name:', project_name);
    console.log('从全局状态恢复的仓库信息:', form.gitlab);
    console.log('从全局状态恢复的分支信息:', form.project_branch);

    if (project_code=="" && project_name=="") {
      console.info('项目信息为空，请先选择项目');
      showTipDialog.value = true;
    } else {
      console.info('项目信息已选择，开始初始化');
      showTipDialog.value = false;
      // 如果没有仓库信息，则获取仓库列表
      if (!form.gitlab || spaceOptions.value.length === 0) {
        await get_space();
      } else {
        // 如果有仓库信息但没有分支信息，则获取分支列表
        if (!form.project_branch || branchOptions.value.length === 0) {
          await get_branch();
        } else {
          // 如果都有，直接提交分支信息
          await submit_branch_info();
        }
      }
    }



    // 添加窗口大小变化监听
    window.addEventListener('resize', handleResize);

    // 在开发环境中暴露调试函数到全局
    if (process.env.NODE_ENV === 'development') {
      window.debugCache = {
        showStatus: showCacheStatus,
        clearAll: clearAllCache,
        getPinConfig: getPinConfigFromCache,
        getModuleConfig: (pinId, moduleType) => getModuleConfigFromCache(pinId, moduleType),
        saveModuleConfig: (pinId, moduleType, config) => saveModuleConfigToCache(pinId, moduleType, config),
        clearModuleConfig: (pinId, moduleType) => clearModuleConfigCache(pinId, moduleType),
        savePinState: (pinId) => savePinCompleteState(pinId),
        restorePinState: (pinId) => restorePinCompleteState(pinId),
        pinConfigCache: pinConfigCache,
        moduleConfigCache: moduleConfigCache,
        pinTypeCache: pinTypeCache,
        pinCompleteStateCache: pinCompleteStateCache
      };
      console.log('🔧 调试工具已暴露到 window.debugCache');
    }

  } catch (error) {
    console.error('组件初始化错误:', error);
    messageManager.error('组件初始化失败，请刷新页面重试');
  } finally {
    loading.value = false;
  }
});

// 监控项目信息变化
watch(() => projectStore.project_info, (newval) => {
  if (newval) {
    const newProjectCode = newval.projectCode || '';
    const newProjectName = newval.name || '';

    // 只有当项目代码真正发生变化时才重新获取仓库信息
    if (newProjectCode !== project_code) {
      console.log('项目切换:', project_code, '->', newProjectCode);

      project_code = newProjectCode;
      project_name = newProjectName;

      // 检查项目是否选择
      if (project_code=="" && project_name=="") {
        showTipDialog.value = true;
      } else {
        showTipDialog.value = false;
        // 清空全局状态和本地状态
        projectStore.clearCodeManagement();
        form.gitlab = '';
        form.project_branch = '';
        spaceOptions.value = [];
        branchOptions.value = [];
        get_space();
      }
    }
  }
});

// 监控仓库信息变化
watch(() => form.gitlab, (newval, oldval) => {
  if (newval !== oldval) {
    // 同步更新全局状态
    projectStore.updateCodeManagement({ gitlab: newval });
    form.project_branch = '';
    get_branch();
  }
});

// 监控分支信息变化
watch(() => form.project_branch, (newval, oldval) => {
  if (newval !== oldval) {
    // 同步更新全局状态
    projectStore.updateCodeManagement({ project_branch: newval });
    submit_branch_info();
  }
});


// 清理监听器
onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
});


// 获取仓库列表
const get_space = async () => {
  try {
    loading.value = true;
    console.log('🔄 调用get_space - 开始获取仓库信息，项目代码:', project_code);
    console.trace('get_space调用堆栈:'); // 添加调用堆栈跟踪

    const response = await http.get('/code_management/space_options', {
      params: {
        project_code: project_code
      }
    });

    console.log("获取已创建的仓库信息:", response.data);

    if (response.data.status === 1) {
      spaceOptions.value = response.data.space_options || [];
      console.log('仓库列表:', spaceOptions.value);

      // 同步更新全局状态
      projectStore.setSpaceOptions(spaceOptions.value);

      // 设置默认仓库为第一个
      if (spaceOptions.value.length > 0 && !form.gitlab) {
        form.gitlab = spaceOptions.value[0];
        console.log('设置默认仓库:', form.gitlab);
        // 注意：这里不需要手动调用get_branch()，因为watch监听器会自动触发
      } else if (spaceOptions.value.length === 0) {
        messageManager.warning('该项目暂无可用仓库');
        showTipDialog.value = true;
      }
    } else {
      console.error('获取仓库信息失败:', response.data.message);
      messageManager.error(response.data.message || '获取仓库信息失败');
    }
  } catch (error) {
    console.error('获取仓库信息失败:', error.message);
    messageManager.error('获取仓库信息失败: ' + error.message);
  } finally {
    loading.value = false;
  }
};

// 获取分支列表
const get_branch = async () => {
  if (!project_code || !project_name) {
    console.info('项目信息为空，不请求分支数据');
    return;
  }

  if (!form.gitlab) {
    console.warn('仓库地址为空，无法获取分支');
    return;
  }

  try {
    console.log('🔄 调用get_branch - 开始获取分支信息，仓库:', form.gitlab);
    console.trace('get_branch调用堆栈:'); // 添加调用堆栈跟踪

    const response = await http.get('/code_management/branch_options', {
      params: {
        project_code: project_code,
        project_gitlab: form.gitlab
      }
    });

    if (response.data.status === 1) {
      branchOptions.value = response.data.branch_options || [];
      console.log('分支列表:', branchOptions.value);

      // 同步更新全局状态
      projectStore.setBranchOptions(branchOptions.value);

      // 设置默认分支为第一个
      if (branchOptions.value.length > 0 && !form.project_branch) {
        form.project_branch = branchOptions.value[0];
        console.log('设置默认分支:', form.project_branch);
        // 注意：这里不需要手动调用submit_branch_info()，因为watch监听器会自动触发
      } else if (branchOptions.value.length === 0) {
        messageManager.warning('该仓库暂无可用分支');
        showTipDialog.value = true;
      }
    } else {
      console.error('获取分支信息失败:', response.data.message);
      messageManager.error(response.data.message || '获取分支信息失败');
    }
  } catch (error) {
    console.error('获取分支信息失败:', error.message);
    messageManager.error('获取分支信息失败: ' + error.message);
  }
};

// 提交分支信息并获取SDK版本
const submit_branch_info = async () => {
  if (!project_code || !project_name) {
      console.info('项目信息为空，不提交数据');
      return;
  }
  
  if (!form.gitlab || !form.project_branch) {
    console.info('仓库或分支信息不完整，无法提交');
    return;
  }

  try {
    loading.value = true;
    console.log('🔄 调用submit_branch_info - 提交分支信息:', {
      project_code,
      project_name,
      gitlab: form.gitlab,
      branch: form.project_branch
    });
    // console.trace('submit_branch_info调用堆栈:'); // 添加调用堆栈跟踪

    const response = await http.get('/code_management/branch_submit', {
      params: {
        project_code: project_code,
        project_name: project_name,
        project_gitlab: form.gitlab,
        project_branch: form.project_branch
      },
      timeout: 60000
    });

    console.log("分支提交响应:", response.data);
    console.log(response.data.table)
    console.log(response.data.color)
    if (response.data.config_status === 1) {
      SdkVersion.value = response.data.sdk_version;
      console.log('SDK版本:', SdkVersion.value);

      // 保存工作空间和分支状态信息
      workspace_path = response.data.work_space;
      workspace.value = response.data.work_space;
      branch_status.value = response.data.branch_status;
      console.log('工作空间:', workspace.value);
      console.log('分支状态:', branch_status.value);

      // 同步更新全局状态
      projectStore.setSdkVersion(SdkVersion.value);

      // 更新分支创建权限状态
      branch_create.value = response.data.branch_create === true || response.data.branch_create === 'true';
      console.log('分支创建权限:', branch_create.value);
      

      // 调用获取芯片信息接口
      get_chipinfo()

      // 可以在这里添加其他成功后的操作
      messageManager.success('项目配置加载成功');
    } else {
      const errorMsg = response.data.message || '分支提交失败';
      console.error('分支提交失败:', errorMsg);
      messageManager.error(errorMsg);
    }
  } catch (error) {
    console.error('分支提交失败:', error.message);
    messageManager.error('分支提交失败: ' + error.message);
  } finally {
    loading.value = false;
  }
};

// 获取芯片信息
const get_chipinfo = async () => {
  try {
    chipOptionsLoading.value = true;
    console.log('🔄 开始获取芯片信息');
    console.log('请求参数:', {
      project_code: project_code,
      project_name: project_name,
      project_gitlab: form.gitlab,
      project_branch: form.project_branch
    });

    const response = await http.get('/code_management/chip_info', {
      params: {
        project_code: project_code,
        project_name: project_name,
        project_gitlab: form.gitlab,
        project_branch: form.project_branch
      }
    });

    console.log("获取芯片信息完整响应:", response);
    console.log("获取芯片信息数据:", response.data);

    console.log('芯片型号:', response.data.data.chip);
    console.log('芯片引脚颜色:', response.data.data.color);
    console.log('芯片引脚模块:', response.data.data.io);
    console.log('芯片引脚配置:', response.data.data.table);
    console.log('芯片引脚类型:', response.data.data.type_info);


    if (response.data && response.data.status === 1) {
      chip.value = response.data.data?.chip || response.data.chip;
      typeInfo.value = response.data.data?.type_info || {}; // 🎯 保存引脚类型信息
      console.log("提取的芯片数据:", chip.value);
      console.log("提取的类型信息:", typeInfo.value);

    } else {
      console.error('API返回状态不正确:', response.data);
      messageManager.error('获取芯片信息失败: ' + (response.data?.message || '未知错误'));
      return;
    }

    // 处理芯片选项数据
    if (chip.value) {
      console.log('芯片数据类型:', typeof chip.value);
      console.log('芯片数据是否为数组:', Array.isArray(chip.value));

      if (Array.isArray(chip.value)) {
        // 如果是数组，转换为选项格式
        chipOptions.value = chip.value.map(item => ({
          label: item.name || item.model || item.label || item,
          value: item.model || item.value || item.name || item
        }));
        console.log('处理后的芯片选项(数组):', chipOptions.value);

        // 设置默认选中第一个芯片
        if (chipOptions.value.length > 0) {
          const firstChip = chip.value[0];
          const firstOption = chipOptions.value[0];

          // 使用选项中的 value 作为 chipModel，确保匹配
          const newChipModel = firstOption.value;
          const newVccVoltage = firstChip.vccVoltage || 3.3;
          const newClockFreq = firstChip.clockFreq || 160;

          console.log('准备设置芯片数据(数组):', {
            chipModel: newChipModel,
            vccVoltage: newVccVoltage,
            clockFreq: newClockFreq,
            firstOption: firstOption
          });

          chipForm.chipModel = newChipModel;
          chipForm.vccVoltage = newVccVoltage;
          chipForm.clockFreq = newClockFreq;
          console.log('设置后的 chipForm(数组):', chipForm);
        }
      } else if (typeof chip.value === 'object') {
        // 如果是单个对象，转换为数组格式
        const optionValue = chip.value.model || chip.value.value || chip.value.name || 'default';
        chipOptions.value = [{
          label: chip.value.name || chip.value.model || chip.value.label || 'Default Chip',
          value: optionValue
        }];
        console.log('处理后的芯片选项(对象):', chipOptions.value);

        const newVccVoltage = chip.value.vccVoltage || 3.3;
        const newClockFreq = chip.value.clockFreq || 160;

        console.log('准备设置芯片数据(对象):', {
          chipModel: optionValue,
          vccVoltage: newVccVoltage,
          clockFreq: newClockFreq
        });

        chipForm.chipModel = optionValue;
        chipForm.vccVoltage = newVccVoltage;
        chipForm.clockFreq = newClockFreq;
        console.log('设置后的 chipForm(对象):', chipForm);
      } else if (typeof chip.value === 'string') {
        // 如果是字符串，直接使用
        chipOptions.value = [{
          label: chip.value,
          value: chip.value
        }];
        console.log('处理后的芯片选项(字符串):', chipOptions.value);
        chipForm.chipModel = chip.value;
        console.log('设置芯片型号(字符串):', chipForm.chipModel);
      }
    } else {
      console.warn('芯片数据为空或未定义');
      chipOptions.value = [];
    }


    // 处理芯片预览数据
    // 1. 引脚数量使用 response.data.data.table.length
    if (response.data.data && response.data.data.table && Array.isArray(response.data.data.table)) {
      // 🎯 保存原始table数据，用于model字段绑定
      table.value = response.data.data.table;
      console.log("🎯 保存原始table数据:", table.value);

      chipPreviewData.pinCount = response.data.data.table.length;
      console.log("芯片引脚的个数：", chipPreviewData.pinCount);

      // 处理引脚数据
      chipPreviewData.pins = response.data.data.table.map((pinData, index) => {
        // 引脚编号直接使用 pin_id 字段
        const pinNumber = pinData.pin_id || (index + 1);
        // 引脚名称直接使用 pin_name 字段
        const pinName = pinData.pin_name || `PIN${pinNumber}`;

        // 使用model字段作为引脚类型，如果model为空，则设置为"其他"
        const pinType = pinData.model ? pinData.model : '未配置';

        // console.log(`ChipPinDiagram - 引脚 PIN${pinNumber} 类型: ${pinType}, 原始model值: ${pinData.model}`);

        return {
          pinId: pinNumber.toString(),
          pinNumber: pinNumber,
          name: pinName,
          type: pinType,
          pinType: pinType, // 同时设置pinType字段，确保兼容性
          displayType: pinType,
          originalData: pinData
        };
      });

      console.log('处理后的芯片引脚数据:', chipPreviewData.pins);

      // 同时处理PinConfigTable所需的数据
      pinTableData.value = response.data.data.table.map((pinData, index) => {
        // 引脚编号直接使用 pin_id 字段
        const pinNumber = pinData.pin_id || (index + 1);

        // 引脚名称直接使用 pin_name 字段
        const pinName = pinData.pin_name || `PIN${pinNumber}`;

        // 当引脚类型为null时，显示"未配置"
        const pinType = !pinData.model || pinData.model === 'Null' ? '未配置' : pinData.model;

        // 可选类型直接使用 module 字段，如果是数组则展示所有选项，如果是字符串则作为单个选项
        let availableTypes = [];
        if (pinData.module) {
          if (Array.isArray(pinData.module)) {
            availableTypes = pinData.module;
          } else {
            availableTypes = [pinData.module];
          }
        }

        // // 调试信息
        // console.log(`引脚 ${pinNumber} 数据处理:`, {
        //   pin_name: pinData.pin_name,
        //   module: pinData.module,
        //   processedName: pinName,
        //   processedAvailableTypes: availableTypes
        // });

        // 状态通过status字段确认是否配置
        const status = pinData.status ? '已配置' : '未配置';

        return {
          pinId: pinNumber.toString(),
          pinName: pinName,
          pinType: pinType,
          pinNumber: pinNumber,
          model: pinData.model || '',
          module: pinData.module || [],
          availableTypes: availableTypes,
          status: status,
          description: pinData.description || '',
          originalData: pinData
        };
      });

      // 对引脚配置表数据按引脚编号进行升序排列
      pinTableData.value.sort((a, b) => {
        const pinIdA = parseInt(a.pinId) || 0;
        const pinIdB = parseInt(b.pinId) || 0;
        return pinIdA - pinIdB;
      });

      console.log('处理并排序后的PinConfigTable数据:', pinTableData.value);
    } else {
      chipPreviewData.pinCount = 0;
      chipPreviewData.pins = [];
      pinTableData.value = [];
      console.warn('未找到 response.data.data.table 数据');
    }

    // 处理io数据生成availableIoTypes
    if (response.data.data.io && Array.isArray(response.data.data.io)) {
      console.log('原始IO数据:', response.data.data.io);

      // 转换IO数据为PinConfigTable组件期望的格式
      availableIoTypes.value = response.data.data.io.map(ioItem => {
        // 处理不同的数据格式
        if (typeof ioItem === 'string') {
          return {
            value: ioItem,
            label: ioItem,
            description: ''
          };
        } else if (typeof ioItem === 'object') {
          return {
            value: ioItem.value || ioItem.name || ioItem.type || ioItem.label,
            label: ioItem.label || ioItem.name || ioItem.type || ioItem.value,
            description: ioItem.description || ioItem.desc || ''
          };
        }
        return {
          value: String(ioItem),
          label: String(ioItem),
          description: ''
        };
      });

      // 初始化ioConfig为空数组，复选框全部不勾选
      ioConfig.value = [];

      console.log('处理后的IO类型数据:', availableIoTypes.value);
      console.log('初始化的IO配置:', ioConfig.value);
    } else {
      availableIoTypes.value = [];
      ioConfig.value = [];
      console.warn('未找到 response.data.data.io 数据');
    }

    // 设置芯片型号
    if (chipForm.chipModel) {
      chipPreviewData.chipModel = chipForm.chipModel;
    } else if (response.data.data && response.data.data.chip) {
      chipPreviewData.chipModel = response.data.data.chip;
    }

    // 2. 图例颜色使用 response.data.data.color 中的数据
    console.log('原始color数据:', response.data.data.color);

    if (response.data.data && response.data.data.color) {
      console.log('检测到颜色数据，类型:', typeof response.data.data.color);

      // 如果是数组格式，直接传递给 ChipPinDiagram 组件处理
      if (Array.isArray(response.data.data.color)) {
        console.log('使用数组格式的颜色配置');
        chipPreviewData.typeStyleConfig = response.data.data.color;
      } else if (typeof response.data.data.color === 'object') {
        // 如果是对象格式，也直接传递
        console.log('使用对象格式的颜色配置');
        chipPreviewData.typeStyleConfig = response.data.data.color;
      }

      console.log('最终传递的图例颜色配置:', chipPreviewData.typeStyleConfig);
    } else {
      // 使用默认颜色配置
      chipPreviewData.typeStyleConfig = {
        'VCC': { color: '#ff4757', backgroundColor: '#ffe8e8' },
        'GND': { color: '#2f3542', backgroundColor: '#e8e8e8' },
        'IO': { color: '#3742fa', backgroundColor: '#e8f0ff' },
        'CLK': { color: '#2ed573', backgroundColor: '#e8ffe8' },
        'RST': { color: '#ffa502', backgroundColor: '#fff5e8' },
        'ADC': { color: '#ff6b81', backgroundColor: '#ffe8ed' },
        'PWM': { color: '#a55eea', backgroundColor: '#f3e8ff' },
        'UART': { color: '#26de81', backgroundColor: '#e8fff3' },
        'SPI': { color: '#fd79a8', backgroundColor: '#ffe8f1' },
        'I2C': { color: '#00b894', backgroundColor: '#e8fff8' },
        'OTHER': { color: '#B0BEC5', backgroundColor: '#f5f5f5' },
        '其他': { color: '#B0BEC5', backgroundColor: '#f5f5f5' },
        '未配置': { color: '#ECEFF1', backgroundColor: '#ECEFF1' }
      };
      console.log('未找到 response.data.data.color，使用默认图例颜色配置');
    }

    // messageManager.success('芯片信息获取成功');
  } catch (error) {
    console.error('获取芯片信息失败:', error);
    messageManager.error('获取芯片信息失败: ' + error.message);
  } finally {
    chipOptionsLoading.value = false;
  }
};

// 功能按钮处理函数
// download 操作
const handleDownload = () => {
  console.log('开始下载配置');
  messageManager.success('配置下载成功');
};



// commit 操作 - 接收工具栏传递的 commit 信息
const handleSave = async (commitMessage) => {
  try {
    loading.value = true;
    console.log('开始保存配置，Commit信息:', commitMessage);

    const response = await http.post('/code_management/config_commit', {
      params: {
        commit_message: commitMessage,
        workspace_path: workspace.value,
        branch_status: branch_status.value
      }
    }, {
      timeout: 60000
    });

    loading.value = false;
    console.log('保存配置响应:', response.data);

    if (response.data.commit_status === 1) {
      messageManager.success('配置已保存:commit成功');
    } else {
      messageManager.error('配置保存失败:commit失败');
    }
  } catch (error) {
    loading.value = false;
    console.error('保存配置失败:', error);
    messageManager.error('保存失败: ' + (error.message || '网络错误'));
  }
};

// push 操作 - 接收工具栏传递的发布确认
const handlePublish = async () => {
  try {
    loading.value = true;
    console.log('开始发布配置');

    const response = await http.post('/code_management/config_push', {
      params: {
        workspace_path: workspace.value,
        branch_status: branch_status.value
      },
    }, {
      timeout: 60000
    });

    loading.value = false;
    console.log('发布配置响应:', response.data);

    if (response.data.push_status === 1) {
      messageManager.success('配置已发布');
    } else {
      messageManager.error('配置发布失败');
    }
  } catch (error) {
    loading.value = false;
    console.error('发布配置失败:', error);
    messageManager.error('发布失败: ' + (error.message || '网络错误'));
  }
};


const handlemerge = () => {
  dialogVisible.value = true;
}

// merge 确认操作 - 接收工具栏传递的 merge 分支信息
const confirmAction = async (mergeBranch) => {
  try {
    loading.value = true;
    console.log('开始合并分支到:', mergeBranch);

    const response = await http.post('/code_management/merge_project', {
      params: {
        workspace_path: workspace.value,
        merge_branch: mergeBranch
      },
     }, {
      timeout: 60000
    });

    loading.value = false;
    console.log('合并分支响应:', response.data);
    messageManager.success('已发送merge信息,请等待管理员审批');
  } catch (error) {
    loading.value = false;
    console.error('合并分支失败:', error);
    messageManager.error('merge失败: ' + (error.message || '网络错误'));
  }
};


// 测试merge功能（由工具栏组件处理弹窗）
const mergetest = () => {
  // 弹窗逻辑现在在Toolbar组件中处理
  console.log('准备合并操作');
};

</script>

<style scoped>
/* 顶部工具栏 */


/* 顶部导航样式 */
.top-navigation {
  background: #fff;
  border-bottom: 2px solid #e4e7ed;
  padding: 0 20px;
  margin-bottom: 0;
}

.nav-tabs {
  display: flex;
  gap: 0;
}

.nav-tab {
  padding: 16px 24px;
  cursor: pointer;
  border-bottom: 3px solid transparent;
  color: #606266;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
}

.nav-tab:hover {
  color: #409eff;
  background-color: #f5f7fa;
}

.nav-tab.active {
  color: #409eff;
  border-bottom-color: #409eff;
  background-color: #ecf5ff;
}

.nav-tab i {
  font-size: 16px;
}

/* Merge弹窗样式 */
.merge-dialog {
  border-radius: 8px;
}

/* 修复弹窗定位问题 - 覆盖全局样式 */
.merge-dialog.el-dialog {
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  margin: 0 !important;
  z-index: 2000 !important;
}

/* 确保弹窗遮罩层正常显示 */
.el-overlay {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  z-index: 1999 !important;
}

.merge-dialog .el-dialog__header {
  padding: 20px 20px 10px;
  border-bottom: 1px solid #ebeef5;
}

.merge-dialog .el-dialog__body {
  padding: 20px;
}

.dialog-content {
  text-align: center;
  padding: 10px 0;
}

.dialog-icon {
  font-size: 48px;
  color: #409eff;
  margin-bottom: 16px;
}

.dialog-description {
  font-size: 16px;
  color: #606266;
  margin-bottom: 20px;
  line-height: 1.5;
}

.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 12px;
  padding: 10px 0;
}

.dialog-footer .el-button {
  min-width: 80px;
}

/* 主要内容区域布局 */
.main-content {
  display: flex;
  align-items: flex-start; /* 从顶部开始对齐，不强制拉伸 */
  gap: 20px;
  /* margin-top: 20px; */
  /* padding: 0 30px; 🎯 添加与 Toolbar 一致的左右 padding */
  margin: 20px 5px;
  height: auto; /* 根据内容自适应高度 */
  min-height: calc(100vh - 200px); /* 最小高度保持视窗高度 */
  /* 调试边框 */
  /* border: 2px solid red; */
}

/* 左侧容器 */
.left-container {
  flex: 1;
  height: 100%; /* 确保高度填满父容器 */
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 左侧第一行 */
.left-row-1 {
  display: flex;
  gap: 20px;
  flex: 1;
}

/* 芯片配置区域 - 占30% */
.chip-config-section {
  flex: 0 0 30%;
}

/* 芯片预览区域 - 占80% */
.other-config-section {
  flex: 1;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  height: 100%;
  overflow: hidden;
}

/* 左侧第二行 */
.left-row-2 {
  flex: 1;
  background: #f8f9fa;
  /* border: 1px solid #e9ecef; */
  border-radius: 8px;
  /* padding: 20px; */
  display: flex;
  flex-direction: column;
  height: 100%;
  min-width: 0;
  width: 100%;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

/* 右侧容器 */
.right-container {
  flex: 0 0 30%; /* 右侧容器占30%宽度 */
  width: 100%;
  height: auto; /* 根据内容自适应高度 */
  min-height: fit-content; /* 最小高度适应内容 */
  display: flex;
  flex-direction: column;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  background: #fff; /* 添加背景色 */
  align-self: flex-start; /* 从顶部开始对齐，不强制拉伸 */
}

/* 占位内容样式 */
.placeholder-content {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #6c757d;
  font-size: 16px;
}

.placeholder-content p {
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-content {
    gap: 15px;
  }

  .left-row-1 {
    flex-direction: column;
    gap: 15px;
  }

  .chip-config-section {
    width: 100% !important;
  }

  .other-config-section {
    width: 100% !important;
  }
}

@media (max-width: 768px) {
  .main-content {
    flex-direction: column;
    align-items: stretch; /* 保持拉伸对齐 */
    gap: 10px;
    height: auto;
    min-height: calc(100vh - 200px);
  }

  .left-container {
    width: 100%;
    height: auto; /* 移动端允许自动高度 */
  }

  .right-container {
    flex: none;
    width: 100%;
    height: auto; /* 移动端允许自动高度 */
  }

  .left-row-1 {
    flex-direction: column;
    gap: 10px;
  }
}


</style>