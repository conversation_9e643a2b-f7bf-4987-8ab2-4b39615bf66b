<template>

    <el-form :model="model" label-width="auto" :rules="rules" status-icon ref="formRef">
        <el-form-item label="版本类型" prop="soft_type">
            <el-select v-model="model.soft_type" placeholder="请选择版本类型" filterable clearable>
                <el-option v-for="item in softTypeList" :key="item.id" :label="item.value"
                    :value="item.id"></el-option>
            </el-select>
        </el-form-item>

        <el-form-item label="版本号" prop="soft_version">
            <el-input v-model="model.soft_version" placeholder="请输入版本号"></el-input>
        </el-form-item>

        <el-form-item label="版本信息" prop="soft_version_info">
            <el-input type="textarea" :rows="3" v-model="model.soft_version_info" placeholder="请输入版本信息"></el-input>
        </el-form-item>

        <el-form-item label="是否更新" prop="is_update">
            <el-select v-model="model.is_update" placeholder="请选择是否更新" clearable>
                <el-option label="是" value="l9qptk5e-cc6ftoliw28-0"></el-option>
                <el-option label="否" value="l9qptk65-va45ggfw72f-1"></el-option>
            </el-select>
        </el-form-item>

        <el-form-item label="版本路径" prop="soft_path">
            <el-input v-model="model.soft_path" placeholder="请输入版本路径"></el-input>
        </el-form-item>

        <el-form-item label="变更履历" prop="soft_revision">
            <el-input type="textarea" :rows="3" v-model="model.soft_revision" placeholder="请输入变更履历"></el-input>
        </el-form-item>

        <el-form-item label="版本存在的风险" prop="soft_risk">
            <el-input type="textarea" :rows="3" v-model="model.soft_risk" placeholder="请输入版本存在的风险"></el-input>
        </el-form-item>
    </el-form>

</template>

<script setup>
import { ref, inject } from 'vue';

const model = defineModel();
const formRef = ref(null);
const rules = ref({
    soft_type: [{ required: true, message: '请选择版本类型', trigger: 'change' }],
    soft_version: [{ required: true, message: '请输入版本号', trigger: 'blur' }],
    soft_version_info: [{ required: true, message: '请输入版本信息', trigger: 'blur' }],
    is_update: [{ required: true, message: '请选择是否更新', trigger: 'change' }],
    soft_path: [{ required: true, message: '请输入版本路径', trigger: 'blur' }],
    soft_revision: [{ required: true, message: '请输入变更履历', trigger: 'blur' }],
    soft_risk: [{ required: true, message: '请输入版本存在的风险', trigger: 'blur' }]
})

const softTypeList = inject('softTypeList', []);

</script>

<style lang="scss" scoped></style>