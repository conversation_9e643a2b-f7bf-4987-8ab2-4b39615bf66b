<template>
    <el-divider />
    <div class="add-container">
        <el-form :model="form" label-width="auto" :rules="rules" status-icon ref="formRef">

            <el-form-item label="点检项名称">
                <el-input v-model="form.item_name" readonly></el-input>
            </el-form-item>

            <el-form-item label="点检项编号">
                <el-input v-model="form.item_code" readonly></el-input>
            </el-form-item>

            <el-form-item label="点检项描述">
                <el-input type="textarea" :rows="3" v-model="form.item_desc" readonly></el-input>
            </el-form-item>

            <el-form-item label="点检时间">
               <el-input v-model="form.time_date" readonly></el-input>
            </el-form-item>

            <el-form-item label="点检人员">
                <el-input v-model="form.person_name" readonly></el-input>
            </el-form-item>

            <el-form-item label="点检状态" prop="is_pass">
                <el-select v-model="form.is_pass" placeholder="请选择点检状态">
                    <el-option label="通过" :value="true"></el-option>
                    <el-option label="不通过" :value="false"></el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="点检描述" prop="desc">
                <el-input type="textarea" :rows="3" v-model="form.desc"></el-input>
            </el-form-item>

            <div class="submit-button-container">
                <el-button type="default" @click="onCancel">取消</el-button>
                <el-button type="primary" @click="onSubmit">提交</el-button>
            </div>

        </el-form>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import http from '@/utils/http/http.js';


const props = defineProps({
    r_id: {
        type: Number,
        required: true,
    },
});

const formRef = ref(null);

const form = ref({
    item_name: '',
    item_code: '',
    item_desc: '',
    time_date: '',
    person_name: '',
    is_pass: null,
    desc: '',
});

const rules = ref({
    is_pass: [
        { required: true, message: '请选择点检状态', trigger: 'change' },
    ],
    desc: [
        { required: true, message: '请输入点检描述', trigger: 'blur' },
    ],
});

const emit = defineEmits(['confirm', 'cancel'])

const onSubmit = () => {
    formRef.value.validate(async (valid) => {
        if (valid) {
            let data = {
                id: props.r_id,
                is_pass: form.value.is_pass,
                desc: form.value.desc,
            };

            http.put(`/inspecs/records/${props.r_id}`, data).then(res => {
                emit('confirm');
            }).catch(err => {
                ElMessageBox.alert(err.response.data.msg, '编辑失败', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'error',
                })
            });
        };
    });
};

const onCancel = () => {
    emit('cancel');
};

onMounted(() => {
    if (props.r_id) {
        http.get(`/inspecs/records/${props.r_id}`).then(res => {
            let data = res.data.data;

            form.value.item_name = data.item_name;
            form.value.item_code = data.item_code;
            form.value.item_desc = data.item_desc;
            form.value.time_date = data.time_date;
            form.value.person_name = data.person_name;
            form.value.is_pass = data.is_pass;
            form.value.desc = data.desc;
        });
    };
});

</script>


<style lang="scss" scoped>
.add-container {
    padding: 0 20px;
}

.submit-button-container {
    display: flex;
    justify-content: flex-end;
}
</style>