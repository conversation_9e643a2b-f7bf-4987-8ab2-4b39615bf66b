<template>
  <div class="git-tag-form">
    <el-form :model="form" label-width="140px">

      <el-form-item label="启用 Git Tag" required>
        <el-switch v-model="form.enable_git_tag" />
        <span style="margin-left: 12px; color: #606266; font-size: 14px;">
          启用后，构建完成时会自动创建 Git Tag
        </span>
      </el-form-item>

      <el-form-item v-if="form.enable_git_tag">
        <div style="color: #909399; font-size: 14px; line-height: 1.5;">
          <p><strong>说明：</strong></p>
          <p>• Tag 名称将自动使用环境变量 <code>MCU_Folder_Name</code></p>
          <p>• Tag 描述将自动生成为 "Release ${MCU_Folder_Name}"</p>
          <p>• 后端会自动处理 Tag 的创建和推送</p>
        </div>
      </el-form-item>

      <el-divider />

      <el-form-item style="margin-bottom: 8px;">
        <el-button type="primary" @click="handleSave">保存配置</el-button>
        <el-button @click="$emit('cancel')">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  initialConfig: { type: Object, default: () => ({}) }
})
const emit = defineEmits(['save', 'cancel'])

// 默认表单数据
const defaultFormData = {
  enable_git_tag: true
}

// 表单数据
const form = reactive({
  ...defaultFormData,
  ...props.initialConfig
})

// 监听 props 变化，更新表单数据
watch(() => props.initialConfig, (newConfig) => {
  Object.assign(form, defaultFormData, newConfig)
}, { deep: true, immediate: false })

// 保存配置
const handleSave = () => {
  // 简化验证，只需要确认配置即可
  emit('save', form)
}
</script>

<style scoped>
.git-tag-form {
  padding: 20px;
}

/* 让表单项靠左对齐 */
.git-tag-form :deep(.el-form) {
  text-align: left;
}

/* 表单项整体靠左 */
.git-tag-form :deep(.el-form-item) {
  margin-bottom: 20px;
  text-align: left;
  display: flex;
  align-items: flex-start;
}

/* 标签靠左对齐，固定宽度 */
.git-tag-form :deep(.el-form-item__label) {
  text-align: left !important;
  justify-content: flex-start !important;
  width: 140px !important;
  min-width: 140px;
  padding-right: 12px;
  font-weight: 500;
  color: #606266;
}

/* 表单内容区域靠左 */
.git-tag-form :deep(.el-form-item__content) {
  text-align: left;
  flex: 1;
  margin-left: 0 !important;
}

/* 输入框、选择器等组件宽度调整 */
.git-tag-form :deep(.el-input),
.git-tag-form :deep(.el-select) {
  width: 100%;
  max-width: 300px;
}

/* Switch组件特殊处理 */
.git-tag-form :deep(.el-switch) {
  margin-right: 12px;
}

/* 按钮组靠左 */
.git-tag-form :deep(.el-form-item:last-child) {
  margin-top: 30px;
}

.git-tag-form :deep(.el-button) {
  margin-right: 12px;
}
</style>
