<template>
  <div class="git-tag-form">
    <el-form :model="form" label-width="140px">
      
      <el-form-item label="启用 Git Tag" required>
        <el-switch v-model="form.enable_git_tag" />
        <span style="margin-left: 12px; color: #606266; font-size: 14px;">
          启用后，构建完成时会自动创建 Git Tag
        </span>
      </el-form-item>

      <el-form-item label="Tag 名称来源" v-if="form.enable_git_tag">
        <el-radio-group v-model="form.tag_source">
          <el-radio value="env_variable">环境变量 (MCU_Folder_Name)</el-radio>
          <el-radio value="custom">自定义</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="自定义 Tag 名称" v-if="form.enable_git_tag && form.tag_source === 'custom'">
        <el-input v-model="form.custom_tag_name" placeholder="例如: v1.0.0" />
        <div style="margin-top: 8px; color: #909399; font-size: 12px;">
          支持环境变量，例如: release-${BUILD_NUMBER}
        </div>
      </el-form-item>

      <el-form-item label="Tag 描述" v-if="form.enable_git_tag">
        <el-input 
          v-model="form.tag_message" 
          type="textarea" 
          :rows="3"
          placeholder="Tag 描述信息，例如: Release version ${MCU_Folder_Name}"
        />
      </el-form-item>

      <el-divider />
      
      <el-form-item style="margin-bottom: 8px;">
        <el-button type="primary" @click="handleSave">保存配置</el-button>
        <el-button @click="$emit('cancel')">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  initialConfig: { type: Object, default: () => ({}) }
})
const emit = defineEmits(['save', 'cancel'])

// 默认表单数据
const defaultFormData = {
  enable_git_tag: true,
  tag_source: 'env_variable', // 'env_variable' 或 'custom'
  custom_tag_name: '',
  tag_message: 'Release ${MCU_Folder_Name}'
}

// 表单数据
const form = reactive({
  ...defaultFormData,
  ...props.initialConfig
})

// 监听 props 变化，更新表单数据
watch(() => props.initialConfig, (newConfig) => {
  Object.assign(form, defaultFormData, newConfig)
}, { deep: true, immediate: false })

// 保存配置
const handleSave = () => {
  // 验证必填项
  if (form.enable_git_tag) {
    if (form.tag_source === 'custom' && !form.custom_tag_name.trim()) {
      ElMessage.error('请填写自定义 Tag 名称')
      return
    }
    
    if (!form.tag_message.trim()) {
      ElMessage.error('请填写 Tag 描述')
      return
    }
  }
  
  emit('save', form)
}
</script>

<style scoped>
.git-tag-form {
  padding: 20px;
}

/* 让表单项靠左对齐 */
.git-tag-form :deep(.el-form) {
  text-align: left;
}

/* 表单项整体靠左 */
.git-tag-form :deep(.el-form-item) {
  margin-bottom: 20px;
  text-align: left;
  display: flex;
  align-items: flex-start;
}

/* 标签靠左对齐，固定宽度 */
.git-tag-form :deep(.el-form-item__label) {
  text-align: left !important;
  justify-content: flex-start !important;
  width: 140px !important;
  min-width: 140px;
  padding-right: 12px;
  font-weight: 500;
  color: #606266;
}

/* 表单内容区域靠左 */
.git-tag-form :deep(.el-form-item__content) {
  text-align: left;
  flex: 1;
  margin-left: 0 !important;
}

/* 输入框、选择器等组件宽度调整 */
.git-tag-form :deep(.el-input),
.git-tag-form :deep(.el-select) {
  width: 100%;
  max-width: 300px;
}

/* Switch组件特殊处理 */
.git-tag-form :deep(.el-switch) {
  margin-right: 12px;
}

/* 按钮组靠左 */
.git-tag-form :deep(.el-form-item:last-child) {
  margin-top: 30px;
}

.git-tag-form :deep(.el-button) {
  margin-right: 12px;
}
</style>
