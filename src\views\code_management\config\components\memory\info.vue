<template>
  <div class="dynamic-form">


    <div class="variables-container" style="margin-top:-5px; margin-left: 0px;">
      <template v-for="(group, groupIdx) in (config?.variables || [])" :key="groupIdx">
        <!-- 分组标题（优化箭头样式，更明显） -->
        <div
            class="group-title"
            @click="toggleGroup(groupIdx)"
          >
           <!-- 优化后的箭头图标，使用Element Plus图标并确保类名正确 -->
            <el-icon
              :class="[
                'group-arrow',
                { 'is-open': activeGroup.includes(groupIdx) }
              ]"
            >
              <ArrowRight />
            </el-icon>
            <span class="group-title-text">{{ group.group }}</span>
          </div>

        <!-- 分组内容（展开时显示，模拟子项缩进） -->
        <div
          class="group-content"
          v-show="activeGroup.includes(groupIdx)"
          :style="{ marginLeft: '24px' }"
        >
          <!-- 遍历分组中的字段 -->
          <template v-for="(field, fieldKey) in group.list" :key="fieldKey">
            <!-- 主字段 -->
            <div class="form-item">
              <label class="form-label">
                {{ field.name }}{{ field.unit ? `(${field.unit})` : '' }}
                <el-tooltip
                  :content="Array.isArray(field.desc) ? field.desc.join('\n') : (field.desc || '暂无描述')"
                  placement="top"
                  effect="dark"
                >
                  <span class="help-icon">?</span>
                </el-tooltip>
              </label>

              <div class="input-container">
                
                <!-- uint 类型字段 -->
                <template v-if="field.type.includes('uint')">
                    <!-- 当类型为 uint_hex 时显示十六进制悬浮框 -->
                    <template v-if="field.type === 'uint8_hex'">
                      <el-tooltip
                        :content="`当前值: 0x${typeof formData[fieldKey] === 'string' ? formData[fieldKey].toUpperCase() : (formData[fieldKey] || '00').toString().toUpperCase()}`"
                        placement="top"
                        effect="dark"
                      >
                        <el-input
                          v-model="formData[fieldKey]"
                          :key="`hex-input-${fieldKey}`"
                          class="form-input input-control"
                          @input="handleHexInput(fieldKey, $event)"
                          @blur="handleHexBlur(fieldKey)"
                          @change="(val) => handleChange(fieldKey, val, field)"
                          placeholder="请输入十六进制值（如 1A、FF）"
                          clearable
                        >
                          <template #prefix>0x</template>
                          <template #suffix>
                            <span class="text-gray-400 ml-1"
                             v-if="field.min !== undefined && field.min !== null && field.max !== undefined && field.max !== null"
                            >
                              (范围: {{ field.min }}-{{ field.max }})
                            </span>
                          </template>
                        </el-input>
                      </el-tooltip>
                    </template>
                    <!-- 其他 uint 类型保持原有样式 -->
                    <template v-else>
                      <el-input-number
                        v-model="formData[fieldKey]"
                        :min="parseInt(field.min)"
                        :max="parseInt(field.max)"
                        class="form-input input-control"
                        @change="(val) => handleChange(fieldKey, val,  field)"
                      />
                    </template>
                  </template>
                <template v-else-if="field.type === 'text'">
                  <el-input
                      v-model="formData[fieldKey]"
                      :key="`hex-input-${fieldKey}`"
                      class="form-input input-control"
                      @change="(val) => handleChange(fieldKey, val, field)"
                      placeholder="请输入文本内容"
                      clearable
                    ></el-input>
                </template>
               <template v-else-if="field.type === 'text_disable'">
                  <el-input
                      v-model="formData[fieldKey]"
                      :key="`hex-input-${fieldKey}`"
                      class="form-input input-control"
                      @change="(val) => handleChange(fieldKey, val, field)"
                      placeholder="请输入文本内容"
                      clearable
                      :disabled=true
                    ></el-input>
                </template>
              </div>
            </div>

         
          </template>
        </div>

 
       
      </template>
    </div>

   
  </div>
</template>

<script setup>
import { ref, reactive, watch, toRaw, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { Loading, ArrowRight } from '@element-plus/icons-vue';
import http from '@/utils/http/http';


// 定义props接收config、workspace_path和branch_status
const props = defineProps({
  config: {
    type: Object,
    required: true
  },
  workspacePath: {
    type: String,
    required: true
  },
  branchStatus: {
    type: String,
    required: true
  },
  project_code:{
    type: String,
    required: true
  },
  project_name: {
    type: String,
    required: true
  },
  project_gitlab:{
    type: String,
    required: true
  },
  project_branch: {
    type: String,
    required: true
  },
  node_level: {
    type: String,
    required: true
  },
  nodeName: {
      type: String,
      required: true
  },
  previousConfig: {
    type: Object,
    default: () => ({})
  },
  hasEditPermission: {
    type: Boolean,
    default: true
  }
});

// 用于接收子组件配置变更事件
const emit = defineEmits(['configChanged']);

// 表单数据初始化
const formData = reactive({});
// 保存上一次的表单数据
const previousFormData = reactive({});
// 标记配置是否已变更
const isConfigChanged = ref(false);


// 存储原始的default数据
const originalDefaults = reactive({});

console.log('workspacePath:', props.workspacePath);
console.log('branchStatus:', props.branchStatus);

console.log('config.variables:', props.config.variables);
let currentMessage = null; // 用于存储当前消息实例

// 🎯 配置修改请求状态管理
const isConfigChangeRequesting = ref(false);
const currentRequestInfo = ref(null);

// 深度克隆对象（处理数组和对象）
const deepClone = (obj) => {
  // 基础类型直接返回
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }
  
  // 处理日期、正则等特殊对象（如果有）
  if (obj instanceof Date) {
    return new Date(obj.getTime());
  }
  
  // 处理数组
  if (Array.isArray(obj)) {
    // 特殊处理 uint8_list_or 类型的数组
    if (obj.length > 0 && obj[0].value !== undefined) {
      return obj.map(item => {
        const newItem = {...item};
        // 确保 value 是十六进制字符串
        if (typeof newItem.value === 'number') {
          newItem.value = `0x${newItem.value.toString(16).toUpperCase().padStart(2, '0')}`;
        } else if (typeof newItem.value === 'string') {
          newItem.value = `0x${newItem.value.replace(/^0x/i, '').toUpperCase()}`;
        }
        return newItem;
      });
    }
    
    // 普通数组克隆
    return obj.map(item => deepClone(item));
  }
  // 处理普通对象
  const clone = {};
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      // 特殊处理 uint8_hex 类型的字段
      if (key === 'default' && obj.type === 'uint8_hex' && typeof obj[key] === 'string') {
        clone[key] = obj[key].replace(/^0x/i, ''); // 保持无0x前缀的格式
      } else {
        clone[key] = deepClone(obj[key]);
      }
    }
  }
  
  return clone;
};



if (props.config.variables) {
  props.config.variables.forEach(group => {
    Object.entries(group.list).forEach(([fieldKey, field]) => {
      console.log('fieldKey:', fieldKey, 'field:', field);
      let defaultValue = field.default || (field.enum && field.enum[0]) || '';
      
      // 对 uint8_hex 类型，保持字符串格式（去掉0x前缀）
      if (field.type === 'uint8_hex' && typeof defaultValue === 'string') {
        defaultValue = String(defaultValue).replace(/^0x/i, ''); // 去掉0x前缀，如"0xFF"→"FF"
      } else if (typeof defaultValue === 'string' && !isNaN(Number(defaultValue))&& 
          (field.type.includes('uint') || field.type === 'number')) {
        defaultValue = Number(defaultValue); // 其他类型仍可转为数字
      }


     
      
      // 存储原始的default数据
      originalDefaults[fieldKey] = deepClone(field.default);
      // 设置默认值

      formData[fieldKey] = defaultValue;
      previousFormData[fieldKey] = formData[fieldKey];

     

     
    

    });
  });





}


// 监听formData变化，更新previousFormData
watch(
  () => toRaw(formData),
  (newVal) => {
    if (isConfigChanged.value) return; // 正在处理变更时不更新
    // 深度克隆并正确处理数字数组
    Object.keys(newVal).forEach(key => {
      previousFormData[key] = deepClone(newVal[key]);
    });
  },
  { deep: true, immediate: true }
);

// 控制分组展开收起 - 默认展开所有分组
const activeGroup = ref(props.config.variables.map((_, index) => index)); 

function toggleGroup(groupIdx) {
  const index = activeGroup.value.indexOf(groupIdx);
  if (index > -1) {
    activeGroup.value.splice(index, 1); // 收起分组
  } else {
    activeGroup.value.push(groupIdx); // 展开分组
  }
}



// 获取当前选中值对应的子字段
function getSubFields(field, selectedValue) {
  if (!field.list || !field.list[selectedValue]) {
    return [];
  }

  const subFields = field.list[selectedValue];
  if (!Array.isArray(subFields)) {
    return [];
  }

  const result = [];
  subFields.forEach(subFieldObj => {
    Object.entries(subFieldObj).forEach(([key, subField]) => {
      result.push({
        key,
        field: subField
      });
    });
  });

  return result;
}






// 处理十六进制输入（实时过滤无效字符）
const handleHexInput = (fieldKey, value) => {
  console.log('Before hex input:', formData[fieldKey]);
  // 仅允许输入十六进制字符（0-9、A-F、a-f），自动转为大写
  const hexValue = value.replace(/[^0-9A-Fa-f]/g, '').toUpperCase();
  const inputValue = formData[fieldKey] || '0';
  if (!inputValue) return; // 提前返回避免后续处理
  console.log('After hex input:', hexValue);
  formData[fieldKey] = hexValue;
};

// 失焦时校验十六进制范围
const handleHexBlur = (fieldKey) => {
  console.log("fieldKey:", formData[fieldKey])
  const inputValue = formData[fieldKey] || '0';
  // 查找对应的 field 对象
  let targetField = null;

  if (props.config.variables) {
    for (const group of props.config.variables) {
      if (group.list[fieldKey]) {
        targetField = group.list[fieldKey];
        break;
      }
    }
  }
  
  if (!targetField) {
    console.error(`未找到字段 ${fieldKey} 的配置信息`);
    return;
  }
  
  // 处理带0x前缀的min和max值
  const minHex = targetField.min.replace(/^0x/i, ''); // 移除0x前缀
  const maxHex = targetField.max.replace(/^0x/i, ''); // 移除0x前缀
  
  // 转换为十进制用于比较范围
  const inputDec = parseInt(inputValue, 16);
  const minDec = parseInt(minHex, 16);
  const maxDec = parseInt(maxHex, 16);
  
  // 范围校验与修正
  if (isNaN(inputDec) || inputDec < minDec) {
    formData[fieldKey] = minHex; // 小于最小值时强制设为最小值
    ElMessage.warning(`输入值超出范围，最小值为 0x${minHex}`);
  } else if (inputDec > maxDec) {
    formData[fieldKey] = maxHex;// 大于最大值时强制设为最大值
    ElMessage.warning(`输入值超出范围，最大值为 0x${maxHex}`);
  }
};

const handleChange = (label_name, newVal, field) => {
  // 处理非数组类型的值
  let valueToSave = newVal;
  if (!Array.isArray(newVal)) {
    valueToSave = typeof newVal === 'string' && !isNaN(Number(newVal))
      ? Number(newVal)
      : newVal;
  }

  console.log('数据变更:', label_name, valueToSave, '权限状态:', props.hasEditPermission);

  // 检查编辑权限
  if (!props.hasEditPermission) {
    // 没有编辑权限时，自动回退到上一次的数据
    console.log('无编辑权限，自动回退数据:', label_name, previousFormData[label_name]);
    // 先关闭可能存在的旧消息
    if (currentMessage) {
      currentMessage.close();
    }

    // 显示权限提示消息
    currentMessage = ElMessage.warning('当前无编辑权限，数据已自动回退');

    // 延迟回退数据，确保用户能看到变化过程
    setTimeout(() => {
      formData[label_name] = previousFormData[label_name];
      isConfigChanged.value = false;
    }, 500);

    return; // 直接返回，不执行后续的API调用
  }

  // 有编辑权限时，正常更新数据并调用API
  formData[label_name] = valueToSave; // 更新表单数据
  isConfigChanged.value = true; // 标记配置已变更



  let config_value = valueToSave;

 
  if (Array.isArray(valueToSave)) {
    config_value = valueToSave.map(String).join(',');
  }
 
  console.log('field:', field)
 if (field && field.type === 'uint8_hex') {
    
    config_value = `0x${String(config_value).replace(/^0x/i, '')}`;
  }

  const config_path = props.nodeName + '/' + label_name;




  // 先关闭可能存在的旧消息
  if (currentMessage) {
    currentMessage.close();
  }

  //  显示加载对话框
  isConfigChangeRequesting.value = false;
  currentRequestInfo.value = {
    fieldLabel: label_name,
    fieldValue: Array.isArray(valueToSave) ? valueToSave.join(', ') : valueToSave
  };
  console.log("value:", config_value)
  console.log("label_name:", label_name)
  http.post('/code_management/memory_change', {
    params: {
      path: config_path,
      config_value: config_value,
      workspace_path: props.workspacePath,
      branch_status: props.branchStatus,
      config_label:label_name,
      project_code: props.project_code,
      project_name: props.project_name,
      project_gitlab: props.project_gitlab,
      project_branch: props.project_branch,
      node_level: props.node_level,
      nodeName: props.nodeName

    }
  }).then(response => {
    console.log("配置更改详情：", response.data);

    // 🎯 关闭加载对话框
    isConfigChangeRequesting.value = false;
    currentRequestInfo.value = null;

    if (response.data.config_status === 1) {
      // 关闭之前的消息并显示新消息
      if (currentMessage) {
        currentMessage.close();
      }
      currentMessage = ElMessage.success('配置更新成功');
      // 成功后更新上一次配置
      previousFormData[label_name] = valueToSave;
      isConfigChanged.value = false;
      // 通知父组件配置已变更
      emit('configChanged', formData);
    } else {
      // 关闭之前的消息并显示新消息
    if (currentMessage) {
      currentMessage.close();
    }
      currentMessage = ElMessage.error('配置更新失败，正在恢复上一次值');
      // 失败后恢复配置
      setTimeout(() => {
        formData[label_name] = previousFormData[label_name];

        if (field && field.type === 'uint8_list_or') {
          field.default = deepClone(originalDefaults[label_name]);
        }

        isConfigChanged.value = false;
        if (currentMessage) {
          currentMessage.close();
        }
        currentMessage = ElMessage.warning('已恢复到上一次配置');
      }, 1000);
    }
  }).catch(error => {
    // 🎯 关闭加载对话框
    isConfigChangeRequesting.value = false;
    currentRequestInfo.value = null;

    // 处理请求错误
    if (currentMessage) {
      currentMessage.close();
    }
    currentMessage = ElMessage.error('请求失败，请稍后再试');
    console.error('请求错误:', error);
    // 网络错误时恢复配置
    setTimeout(() => {
      formData[label_name] = previousFormData[label_name];
       if (field && field.type === 'uint8_list_or') {
        field.default = deepClone(originalDefaults[label_name]);
      }
      isConfigChanged.value = false;
      // 关闭之前的消息并显示新消息
    if (currentMessage) {
      currentMessage.close();
    }
      currentMessage = ElMessage.warning('已恢复到上一次配置');
    }, 1000);
  });
}


</script>

<style>
/* 整体容器 */
.dynamic-form {
  background-color: #fff;
  color: #666;
  width: 100%;
}

.form-item {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  transition: all 0.3s ease;
  position: relative;
}

.form-item:hover {
  border-color: #c6e2ff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.08);
}


.form-label {
  flex: 0 0 220px;
  text-align: left;
  margin-right: 20px;
  color: #606266;
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
  display: flex;
  align-items: center;
}

/* 用户自定义项标签特殊样式 */
.user-defined-label {
  color: #666;
  font-weight: 600;
  margin-left: 6px;
}

/* 帮助图标样式 */
.help-icon {
  display: inline-block;
  margin-left: 6px;
  width: 16px;
  height: 16px;
  line-height: 16px;
  text-align: center;
  background-color: #e1f3ff;
  color: #409eff;
  border: 1px solid #b3d8ff;
  border-radius: 50%;
  font-size: 12px;
  font-weight: bold;
  cursor: help;
  transition: all 0.3s ease;
}

.help-icon:hover {
  background-color: #409eff;
  color: #fff;
  border-color: #409eff;
  transform: scale(1.1);
  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.3);
}

/* 统一表单控件布局 */
.input-container {
  flex: 1; /* 占据剩余空间 */
  position: relative;
}

.input-control {
  width: 100%; /* 宽度占满容器 */
  font-size: 14px;
  box-sizing: border-box; /* 确保内边距和边框不影响宽度 */
}


.el-input-number {
  width:100%;
  font-size: 14px;
  margin-left: 0; /* 移除多余的左边距 */
}



.group-title {
  font-size: 15px;
  color: #303133;
  font-weight: 600;
  cursor: pointer;
  padding: 16px 12px;
  margin: 8px 0;
  display: flex;
  align-items: center;
  border-radius: 6px;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border: 1px solid #e4e7ed;
  transition: all 0.3s ease;
}

.group-title:hover {
  color: #409eff;
  background: linear-gradient(135deg, #ecf5ff 0%, #f0f9ff 100%);
  border-color: #b3d8ff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
  transform: translateY(-1px);
}

.group-title-text {
  font-size: 15px;
  font-weight: 600;
  margin-left: 8px;
}


.group-content {
  margin-left: 24px;
  padding: 16px 0 16px 16px;
  border-left: 2px solid #e4e7ed;
  background: linear-gradient(135deg, #fafbfc 0%, #ffffff 100%);
  border-radius: 0 8px 8px 0;
  position: relative;
}

.group-content::before {
  content: '';
  position: absolute;
  left: -2px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(180deg, #409eff, #67c23a);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.group-content:hover::before {
  opacity: 1;
}


/* 优化后的箭头图标样式 */
.group-arrow {
  transition: transform 0.3s ease;
  color: #909399;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: rgba(144, 147, 153, 0.1);
}

.group-arrow.is-open {
  transform: rotate(90deg);
  color: #409eff;
  background-color: rgba(64, 158, 255, 0.1);
}

.group-title:hover .group-arrow {
  color: #409eff;
  background-color: rgba(64, 158, 255, 0.15);
}

/* 交互态优化 */
.el-select:hover, .el-input-number:hover, .el-input:hover {
  border-color: #409eff;
}

.el-select:focus, .el-input-number:focus, .el-input:focus {
  border-color: #409eff;
  box-shadow: 0 0 0 2px #666;
}

/* 响应式调整 - 小屏幕适配 */
@media (max-width: 768px) {
  .form-label {
    width: 120px; /* 小屏幕下缩小标签宽度 */
  }
}

/* 配置修改弹框样式 */
.chip-request-dialog {
  --el-dialog-border-radius: 12px;
}

.chip-request-dialog .el-dialog {
  border-radius: 12px;
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

.chip-request-dialog .el-dialog__header {
  display: none;
}

.chip-request-dialog .el-dialog__body {
  padding: 0;
}

.request-dialog-content {
  padding: 32px 24px 24px;
  text-align: center;
  background: linear-gradient(135deg, #f8faff 0%, #ffffff 100%);
}

.request-dialog-header {
  margin-bottom: 24px;
}

.request-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 64px;
  height: 64px;
  background: linear-gradient(135deg, #409eff 0%, #fff 100%);
  border-radius: 50%;
  margin-bottom: 16px;
  box-shadow: 0 4px 16px rgba(64, 158, 255, 0.3);
}

.request-icon .el-icon {
  font-size: 28px;
  color: white;
}

.rotating-icon {
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.request-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  line-height: 1.4;
}

.request-dialog-body {
  margin-bottom: 24px;
}

.request-info {
  background: #f4f7ff;
  border: 1px solid #e1e8ff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
}

.info-item {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 8px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.info-value {
  font-size: 14px;
  color: #409eff;
  font-weight: 600;
  background: #ecf5ff;
  padding: 2px 8px;
  border-radius: 4px;
}

.request-message {
  font-size: 14px;
  color: #909399;
  line-height: 1.6;
  padding: 0 8px;
}

.request-progress {
  margin-top: 20px;
}

.request-progress .el-progress {
  margin-bottom: 0;
}

.request-progress .el-progress__text {
  display: none;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .chip-request-dialog .el-dialog {
    width: 90% !important;
    margin: 0 auto;
  }

  .request-dialog-content {
    padding: 24px 16px 16px;
  }

  .request-icon {
    width: 56px;
    height: 56px;
  }

  .request-icon .el-icon {
    font-size: 24px;
  }

  .request-title {
    font-size: 18px;
  }
}

/* 子字段样式 */
.sub-field {
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 8px;
  margin-top: 12px;
  margin-left: 0 !important;
  position: relative;
  overflow: hidden;
}

.sub-field::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
}

.sub-field .form-label {
  color: #606266;
  font-size: 13px;
  font-weight: 500;
}

.sub-field:hover {
  background: linear-gradient(135deg, #ecf5ff 0%, #f0f9ff 100%);
  border-left-color: #67c23a;
}
/* 在原有样式基础上添加 */
:deep(.el-input__prefix) {
  color: #ccc; /* 0x前缀颜色 */
}
.text-gray-400 {
  color: #999;
  font-size: 12px;
}



</style>