<template>
    <!-- 如果菜单项被隐藏，不渲染 -->
    <template v-if="!route.hidden">
        <template v-if="!route.children">
            <el-menu-item :index="index" @click="$router.push(route.path)">
                <svg-icon v-if="route?.icon?.name" :name="route.icon.name" :source="route.icon.source" />
                <template #title>
                    <app-item :title="route.title" />
                </template>
            </el-menu-item>

        </template>

        <template v-else>
            <el-sub-menu :index="index">
                <template #title>
                    <svg-icon v-if="route?.icon?.name" :name="route.icon.name" :source="route.icon.source" />
                    <app-item :title="route.title" />
                </template>
                <template v-for="(child, index2) in route.children">
                    <sider-item :route="child" :index="index + '-' + index2.toString()" />
                </template>
            </el-sub-menu>
        </template>
    </template>

</template>


<script setup>
import AppItem from './AppItem.vue';
import SvgIcon from '@/components/SvgIcon/index.vue';

let props = defineProps({
    route: {
        type: Object,
        required: true
    },
    index: {
        type: String,
        required: true
    },
});

</script>