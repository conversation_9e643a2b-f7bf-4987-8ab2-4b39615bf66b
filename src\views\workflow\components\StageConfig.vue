<template>
  <div class="stage-config-form">
    <el-form :model="form" label-width="120px">
      
      <el-form-item label="Stage Name" required>
        <el-input v-model="form.stage_name" placeholder="Build, Test, Deploy, etc." />
      </el-form-item>

      <el-form-item label="Stage Order" required>
        <el-input-number v-model="form.stage_order" :min="1" :max="100" disabled />
        <span style="margin-left: 8px; color: #909399; font-size: 12px;">
          (自动根据节点顺序设置)
        </span>
      </el-form-item>

      <!-- 新增：模式切换器 -->
      <el-form-item label="配置模式">
        <el-radio-group v-model="form.configMode" @change="handleModeChange">
          <el-radio-button label="commands">分步命令模式</el-radio-button>
          <el-radio-button label="script">脚本模式 (推荐)</el-radio-button>
        </el-radio-group>
        <div class="mode-tip">
          <span v-if="form.configMode === 'commands'" class="tip-text">
            适合简单命令，每个命令框一个操作
          </span>
          <span v-else class="tip-text">
            适合复杂脚本，直接粘贴完整 .bat/.sh 文件内容
          </span>
        </div>
      </el-form-item>

      
      <div v-if="form.configMode === 'commands'">
        <el-divider content-position="left">Commands</el-divider>
      
      <el-alert
        title="命令配置说明"
        type="info"
        show-icon
        :closable="false"
        style="margin-bottom: 16px;">
        <template #default>
          <ul style="margin: 0px; padding-left: 13px; font-size: 12px;">
            <li>每个命令框可以输入多行命令</li>
            <li><strong>BAT语法要求</strong>：<code style="background: #f5f7fa; padding: 0px 0px;">^</code> 必须是行的最后一个字符</li>
            <li><strong>推荐写法</strong>：使用 <code style="background: #f5f7fa; padding: 0px 0px;">&&</code> 连接命令更简单</li>
            <li><strong>示例</strong>：<code style="background: #f5f7fa; padding: 0px 0px;">git tag -a V1.2 -m "release V1.2" && git push origin V1.2</code></li>
            <li>使用 %变量名% 引用环境变量：<code style="background: #f5f7fa; padding: 0px 0px;">%PROJECT_NUMBER%</code></li>
          </ul>
        </template>
      </el-alert>

      <div v-for="(command, idx) in form.commands" :key="idx" class="command-block">
        <el-form-item :label="`Command ${idx + 1}`">
          <div style="width: 100%;">
            <el-input
              type="textarea"
              v-model="form.commands[idx]"
              :rows="6"
              placeholder="输入Bat命令...&#10;例如：&#10;git tag -a V1.2 -m &quot;release V1.2&quot; && git push origin V1.2&#10;或者：&#10;build.bat %PROJECT_NUMBER%"
              style="width: 100%;"
            />
            <div style="margin-top: 8px;">
              <el-button type="danger" plain size="small" @click="removeCommand(idx)" v-if="form.commands.length > 1">
                删除命令
              </el-button>
            </div>
          </div>
        </el-form-item>
      </div>
      
        <el-button type="primary" plain size="small" @click="addCommand" style="margin-top: 8px;">
          新增命令
        </el-button>
      </div>

      <!-- 新增的 Script 模式 -->
      <div v-if="form.configMode === 'script'">
        <el-divider content-position="left">Script Content</el-divider>
        
        <el-form-item label="脚本内容" required>
          <el-button type="primary" @click="openScriptEditor">
            <el-icon><Edit /></el-icon>
            打开脚本编辑器
          </el-button>
          <span v-if="form.scriptContent" class="script-status">
            已配置脚本 ({{ form.scriptContent?.length || 0 }} 字符)
          </span>
          <span v-else class="script-status-empty">
            未配置脚本内容
          </span>
        </el-form-item>
      </div>

      <el-divider />
      
      <el-form-item style="margin-bottom: 8px;">
        <el-button type="primary" @click="handleSave">保存配置</el-button>
        <el-button @click="$emit('cancel')">取消</el-button>
      </el-form-item>
    </el-form>

    <!-- 全屏脚本编辑器弹窗 -->
    <el-dialog
      v-model="scriptEditorVisible"
      title="脚本编辑器"
      :width="'75%'"
      :top="'5vh'"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      class="script-editor-dialog"
      @open="handleDialogOpen"
      @close="handleDialogClose"
    >
      <div class="script-editor-content">
        <div class="editor-toolbar">
          <div class="toolbar-left">
            <el-tag type="info" size="small">支持 Windows Bat 和 Linux Shell</el-tag>
            <el-tag type="success" size="small">无需转义特殊字符</el-tag>
          </div>
          <div class="toolbar-right">
            <span class="char-count-large">字符数: {{ tempScriptContent?.length || 0 }}</span>
          </div>
        </div>
        
        <el-input
          type="textarea"
          v-model="tempScriptContent"
          :rows="35"
          placeholder="在此输入完整的脚本内容..."
          class="fullscreen-textarea"
          @keydown.ctrl.s.prevent="saveScript"
        />
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelScriptEdit">取消</el-button>
          <el-button type="primary" @click="saveScript">
            <el-icon><Check /></el-icon>
            保存脚本
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { reactive, ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Edit, Check } from '@element-plus/icons-vue'

const props = defineProps({
  initialConfig: { type: Object, default: () => ({}) },
  stageOrder: { type: Number, default: 1 }
})
const emit = defineEmits(['save', 'cancel', 'disable-esc'])

// 默认表单数据
const defaultFormData = {
  stage_name: '',
  stage_order: props.stageOrder,
  configMode: 'commands', // 默认使用原有模式，保持向后兼容
  commands: [''], // 原有模式数据
  scriptContent: '' // 新增脚本模式数据
}

// 检测是否已经被转义（4个连续反斜杠表示已转义）
const isAlreadyEscaped = (str) => {
  return /\\{4,}/.test(str);
};

// 智能反转义函数
const smartUnescape = (str) => {
  if (!isAlreadyEscaped(str)) return str;
  // 将4个反斜杠还原为2个
  return str.replace(/\\{4}/g, '\\\\');
};

const mergeFormData = (initialConfig, defaultData) => {
  const result = JSON.parse(JSON.stringify(defaultData))
  result.stage_order = props.stageOrder
  
  if (initialConfig && typeof initialConfig === 'object') {
    if (initialConfig.stage_name) result.stage_name = initialConfig.stage_name
    
    // 检测配置模式
    if (initialConfig.config_mode === 'script' && initialConfig.script_content) {
      // 脚本模式：解码 Base64 内容
      result.configMode = 'script'
      try {
        result.scriptContent = decodeURIComponent(escape(atob(initialConfig.script_content)))
      } catch (e) {
        result.scriptContent = initialConfig.script_content
      }
    } else if (Array.isArray(initialConfig.commands) && initialConfig.commands.length > 0) {
      // 原有命令模式
      result.configMode = 'commands'
      result.commands = initialConfig.commands.map(cmd => {
        let content = '';
        // 如果是对象格式 {type: 'bat', content: '...'} 
        if (typeof cmd === 'object' && cmd.content) {
          content = cmd.content;
        } else {
          // 如果是字符串格式，直接返回
          content = cmd;
        }
        // 对加载的数据进行智能反转义
        return smartUnescape(content);
      })
    }
  }
  
  return result
}

const form = reactive(mergeFormData(props.initialConfig, defaultFormData))

// 脚本编辑器相关状态
const scriptEditorVisible = ref(false)
const tempScriptContent = ref('')

const addCommand = () => {
  form.commands.push('')
}

const removeCommand = (idx) => {
  if (form.commands.length > 1) {
    form.commands.splice(idx, 1)
  }
}

// 打开脚本编辑器
const openScriptEditor = () => {
  tempScriptContent.value = form.scriptContent
  scriptEditorVisible.value = true
}

// 处理弹窗打开事件
const handleDialogOpen = () => {
  emit('disable-esc', true)
}

// 处理弹窗关闭事件  
const handleDialogClose = () => {
  emit('disable-esc', false)
}

// 保存脚本内容
const saveScript = () => {
  form.scriptContent = tempScriptContent.value
  scriptEditorVisible.value = false
  ElMessage.success('脚本内容已保存')
}

// 取消脚本编辑
const cancelScriptEdit = () => {
  if (tempScriptContent.value !== form.scriptContent) {
    ElMessageBox.confirm('脚本内容已修改，确定要取消吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '继续编辑',
      type: 'warning'
    }).then(() => {
      scriptEditorVisible.value = false
    }).catch(() => {
      // 继续编辑
    })
  } else {
    scriptEditorVisible.value = false
  }
}

const convertPlaceholders = (str) => {
  // 只转换占位符，不进行转义
  let result = str.replace(/%([A-Za-z0-9_]+)%/g, (_, v) => '${env.' + v + '}');
  return result;
}


const formatBatScript = (str) => {
  str = smartUnescape(str);
  const lines = str.split(/\r?\n/).map(l => l.trim()).filter(l => l.length > 0);
  if (lines.length === 0) return '';
  if (lines.length === 1) {
    return isAlreadyEscaped(lines[0]) ? lines[0] : lines[0].replace(/\\/g, '\\\\');
  }
  
  // 检测独立命令的关键词
  const commandStarters = ['git', 'echo', 'cd', 'mkdir', 'copy', 'del', 'npm', 'yarn', 'mvn', 'docker', 'call', 'start', 'taskkill'];
  
  let result = '';
  let i = 0;
  
  while (i < lines.length) {
    let currentLine = lines[i];
    
    // 检查当前行是否以命令开头
    const isCommand = commandStarters.some(cmd => currentLine.toLowerCase().startsWith(cmd + ' '));
    
    // 如果当前行以命令开头，且不是第一行，则用 && 连接
    if (isCommand && result.length > 0) {
      result += ' && ' + currentLine;
    } else {
      // 如果是第一行或者不是命令开头，则直接添加
      if (result.length > 0) {
        // 检查上一行是否以 ^ 结尾
        if (result.endsWith(' ^')) {
          // 上一行已有 ^，直接换行连接
          result += '\\n' + currentLine;
        } else {
          // 上一行没有 ^，添加续行符
          result += ' ^\\n ' + currentLine;
        }
      } else {
        result = currentLine;
      }
    }
    
    i++;
  }
  
  // 智能转义：只有在未转义时才转义
  if (!isAlreadyEscaped(result)) {
    result = result.replace(/\\/g, '\\\\');
  }
  return result;
};

const handleSave = () => {
  // 验证必填字段
  if (!form.stage_name.trim()) {
    ElMessage.error('Stage Name 不能为空')
    return
  }

  // 根据配置模式进行不同的验证和处理
  if (form.configMode === 'commands') {
    // 原有命令模式验证
    const validCommands = form.commands.filter(cmd => cmd.trim().length > 0)
    if (validCommands.length === 0) {
      ElMessage.error('至少需要一个命令')
      return
    }

    const output = {
      stage_name: form.stage_name,
      stage_order: form.stage_order,
      config_mode: 'commands',
      commands: validCommands.map(cmd => ({
        type: 'bat',
        content: formatBatScript(convertPlaceholders(cmd.trim()))
      }))
    }

    emit('save', output)
    
  } else {
    // 脚本模式验证
    if (!form.scriptContent.trim()) {
      ElMessage.error('脚本内容不能为空')
      return
    }

    // Base64 编码脚本内容
    let encodedScript
    try {
      encodedScript = btoa(unescape(encodeURIComponent(form.scriptContent.trim())))
    } catch (e) {
      ElMessage.error('脚本内容编码失败，请检查内容格式')
      return
    }

    const output = {
      stage_name: form.stage_name,
      stage_order: form.stage_order,
      config_mode: 'script',
      script_type: 'raw_script',
      script_content: encodedScript
    }

    emit('save', output)
  }
}
</script>

<style scoped>
.stage-config-form {
  padding-right: 10px;
}

.command-block {
  margin-bottom: 16px;
}

/* 模式切换相关样式 */
.mode-tip {
  margin-top: 8px;
}

.tip-text {
  font-size: 12px;
  color: #909399;
}

/* 脚本模式相关样式 */
.script-textarea {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.4;
}

.script-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
}

.char-count {
  font-weight: 500;
}

.encoding-info {
  color: #67c23a;
}

/* 脚本状态显示 */
.script-status {
  margin-left: 12px;
  font-size: 12px;
  color: #67c23a;
  font-weight: 500;
}

.script-status-empty {
  margin-left: 12px;
  font-size: 12px;
  color: #909399;
}

/* 全屏编辑器样式 */
.script-editor-dialog :deep(.el-dialog) {
  border-radius: 8px;
}

.script-editor-dialog :deep(.el-dialog__header) {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
}

.script-editor-content {
  height: 70vh;
  display: flex;
  flex-direction: column;
}

.editor-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #e4e7ed;
  margin-bottom: 16px;
}

.toolbar-left {
  display: flex;
  gap: 8px;
}

.char-count-large {
  font-size: 14px;
  font-weight: 500;
  color: #409eff;
}

.fullscreen-textarea {
  flex: 1;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
}

.fullscreen-textarea :deep(.el-textarea__inner) {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
  resize: none;
  max-height: 60vh;
}

.editor-tips {
  margin-top: 16px;
}

.tips-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 12px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>