<template>
  <div class="stage-config-form">
    <el-form :model="form" label-width="120px">
      
      <el-form-item label="Stage Name" required>
        <el-input v-model="form.stage_name" placeholder="Build, Test, Deploy, etc." />
      </el-form-item>

      <el-form-item label="Stage Order" required>
        <el-input-number v-model="form.stage_order" :min="1" :max="100" disabled />
        <span style="margin-left: 8px; color: #909399; font-size: 12px;">
          (自动根据节点顺序设置)
        </span>
      </el-form-item>

      <el-divider content-position="left">Commands</el-divider>
      
      <el-alert
        title="命令配置说明"
        type="info"
        show-icon
        :closable="false"
        style="margin-bottom: 16px;">
        <template #default>
          <ul style="margin: 0; padding-left: 20px; font-size: 13px;">
            <li>每个命令框可以输入多行命令</li>
            <li><strong>⚠️ BAT语法要求</strong>：<code style="background: #f5f7fa; padding: 2px 4px;">^</code> 必须是行的最后一个字符</li>
            <li><strong>推荐写法</strong>：使用 <code style="background: #f5f7fa; padding: 2px 4px;">&&</code> 连接命令更简单</li>
            <li><strong>示例</strong>：<code style="background: #f5f7fa; padding: 2px 4px;">git tag -a V1.2 -m "release V1.2" && git push origin V1.2</code></li>
            <li>使用 %变量名% 引用环境变量：<code style="background: #f5f7fa; padding: 2px 4px;">%PROJECT_NUMBER%</code></li>
          </ul>
        </template>
      </el-alert>

      <div v-for="(command, idx) in form.commands" :key="idx" class="command-block">
        <el-form-item :label="`Command ${idx + 1}`">
          <div style="width: 100%;">
            <el-input
              type="textarea"
              v-model="form.commands[idx]"
              :rows="6"
              placeholder="输入BAT命令...&#10;例如：&#10;git tag -a V1.2 -m &quot;release V1.2&quot; && git push origin V1.2&#10;或者：&#10;build.bat %PROJECT_NUMBER%"
              style="width: 100%;"
            />
            <div style="margin-top: 8px;">
              <el-button type="danger" plain size="small" @click="removeCommand(idx)" v-if="form.commands.length > 1">
                删除命令
              </el-button>
            </div>
          </div>
        </el-form-item>
      </div>
      
      <el-button type="primary" plain size="small" @click="addCommand" style="margin-top: 8px;">
        新增命令
      </el-button>

      <el-divider />
      
      <el-form-item style="margin-bottom: 8px;">
        <el-button type="primary" @click="handleSave">保存配置</el-button>
        <el-button @click="$emit('cancel')">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { reactive } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  initialConfig: { type: Object, default: () => ({}) },
  stageOrder: { type: Number, default: 1 }
})
const emit = defineEmits(['save', 'cancel'])

// 默认表单数据
const defaultFormData = {
  stage_name: '',
  stage_order: props.stageOrder,
  commands: ['']
}

// 安全地合并初始配置
const mergeFormData = (initialConfig, defaultData) => {
  const result = JSON.parse(JSON.stringify(defaultData))
  result.stage_order = props.stageOrder
  
  if (initialConfig && typeof initialConfig === 'object') {
    if (initialConfig.stage_name) result.stage_name = initialConfig.stage_name
    if (Array.isArray(initialConfig.commands) && initialConfig.commands.length > 0) {
      // 处理两种可能的数据格式
      result.commands = initialConfig.commands.map(cmd => {
        // 如果是对象格式 {type: 'bat', content: '...'} 
        if (typeof cmd === 'object' && cmd.content) {
          return cmd.content
        }
        // 如果是字符串格式，直接返回
        return cmd
      })
    }
  }
  
  return result
}

const form = reactive(mergeFormData(props.initialConfig, defaultFormData))

const addCommand = () => {
  form.commands.push('')
}

const removeCommand = (idx) => {
  if (form.commands.length > 1) {
    form.commands.splice(idx, 1)
  }
}

// %VAR% -> ${env.VAR} 并转义反斜杠用于Groovy
const convertPlaceholders = (str) => {
  // 先转换占位符
  let result = str.replace(/%([A-Za-z0-9_]+)%/g, (_, v) => '${env.' + v + '}');
  // 然后转义反斜杠用于Groovy字符串
  result = result.replace(/\\/g, '\\\\');
  return result;
}

// 格式化 BAT 脚本：智能处理续行和多命令
const formatBatScript = (str) => {
  const lines = str.split(/\r?\n/).map(l => l.trim()).filter(l => l.length > 0);
  
  if (lines.length === 0) return '';
  if (lines.length === 1) return lines[0];
  
  // 检测独立命令的关键词
  const commandStarters = ['git', 'echo', 'cd', 'mkdir', 'copy', 'del', 'npm', 'yarn', 'mvn', 'docker', 'call', 'start', 'taskkill'];
  
  let result = '';
  let i = 0;
  
  while (i < lines.length) {
    let currentLine = lines[i];
    
    // 检查当前行是否以命令开头
    const isCommand = commandStarters.some(cmd => currentLine.toLowerCase().startsWith(cmd + ' '));
    
    // 如果当前行以命令开头，且不是第一行，则用 && 连接
    if (isCommand && result.length > 0) {
      result += ' && ' + currentLine;
    } else {
      // 如果是第一行或者不是命令开头，则直接添加
      if (result.length > 0) {
        // 检查上一行是否以 ^ 结尾
        if (result.endsWith(' ^')) {
          // 上一行已有 ^，直接换行连接
          result += '\\n' + currentLine;
        } else {
          // 上一行没有 ^，添加续行符
          result += ' ^\\n ' + currentLine;
        }
      } else {
        result = currentLine;
      }
    }
    
    i++;
  }
  
  // 转义反斜杠用于 JSON 字符串
  result = result.replace(/\\/g, '\\\\');
  return result;
};

const handleSave = () => {
  // 验证必填字段
  if (!form.stage_name.trim()) {
    ElMessage.error('Stage Name 不能为空')
    return
  }

  // 验证至少有一个非空命令
  const validCommands = form.commands.filter(cmd => cmd.trim().length > 0)
  if (validCommands.length === 0) {
    ElMessage.error('至少需要一个命令')
    return
  }

  const output = {
    stage_name: form.stage_name,
    stage_order: form.stage_order,
    commands: validCommands.map(cmd => ({
      type: 'bat',
      content: formatBatScript(convertPlaceholders(cmd.trim()))
    }))
  }

  emit('save', output)
}
</script>

<style scoped>
.stage-config-form {
  padding-right: 10px;
}

.command-block {
  margin-bottom: 16px;
}
</style>