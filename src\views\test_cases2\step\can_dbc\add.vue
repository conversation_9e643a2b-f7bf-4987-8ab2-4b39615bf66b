<template>
    <el-form :model="form" :rules="rules" ref="formRef">

        <el-form-item label="DBC文件" prop="dbc_file">
            <el-select v-model="form.dbc_file" placeholder="请选择DBC文件">
                <el-option v-for="file in dbc_files" :key="file.id" :label="file.file_name"
                    :value="file.id"></el-option>
            </el-select>
        </el-form-item>

        <el-form-item label="发送报文" prop="send_msg">
            <AddSendMsg v-model="form.send_msg" />
        </el-form-item>

        <el-form-item label="接收报文" prop="recv_msg">
            <AddRecvMsg v-model="form.recv_msg" />
        </el-form-item>

    </el-form>
</template>


<script setup>
import { ref, watch, onMounted, provide, inject } from 'vue';
import AddRecvMsg from './addRecvMsg.vue';
import AddSendMsg from './addSendMsg.vue'
import http from '@/utils/http/http.js';

const props = defineProps({
    initData: {
        type: Object,
        default: () => ({}),
    },
});

const model = defineModel();

const formRef = ref(null);
const dbc_files = ref([])
const dbcData = ref({})

provide('dbcData', dbcData);

const project_number = inject('project_number', '');

const form = ref({
    dbc_file: '',
    send_msg: [null],
    recv_msg: [null],
});

const rules = ref({
    dbc_file: [{ required: true, message: '请选择DBC文件', trigger: 'change' }],
    send_msg: [{ required: true, message: '请添加发送报文', trigger: 'change' }],
    recv_msg: [{ required: true, message: '请添加接收报文', trigger: 'change' }],
});

watch(form, () => {
    model.value = form.value;
}, { immediate: true });

watch(() => props.initData, (val) => {
    Object.assign(form.value, val);
}, { immediate: true });

watch(() => form.value.dbc_file, (newVal) => {
    if (newVal) {
        http.get(`/can_dbc/${newVal}`).then((res) => {
            dbcData.value = res.data.data;
        }).catch((error) => {
            console.error('获取DBC文件数据时发生错误:', error);
        });
    } else {
        dbcData.value = {};
    }
}, { immediate: true });

const validate = (callback) => {
    formRef.value.validate(callback);
};

defineExpose({
    validate,
});

onMounted(() => {
    if (project_number) {
        http.get('/can_dbc', { params: { pagesize: 100000, project_number: project_number.value } }).then((res) => {
            dbc_files.value = res.data.data.results;
        }).catch((error) => {
            console.error('请求DBC文件列表时发生错误:', error);
        });
    } else {
        console.warn('未提供有效的项目编号');
    }
})

</script>

<style scoped>
.full-width {
    width: 100%;
}

.resizable-textarea :deep(textarea) {
    resize: both;
}

:deep(div.el-form-item__label) {
    font-weight: bold;
}
</style>