import axios from 'axios';
import router from '@/router';
import qs from 'qs';

const http = axios.create({
    baseURL: import.meta.env.VITE_BASE_URL,
    timeout: 60000,
    paramsSerializer: params => qs.stringify(params, { arrayFormat: 'repeat' })
});


http.interceptors.request.use(
    config => {
        let token = sessionStorage.getItem('token');
        if (token) {
            config.headers['Authorization'] = 'Bearer ' + token;
        } else {
            localStorage.setItem('redirectUrl', router.currentRoute.fullPath);
            router.push('/login');
        }
        return config;
    },
    error => {
        return Promise.reject(error);
    }
);

http.interceptors.response.use(
    response => {
        return response;
    },
    error => {
        if (error.response && error.response.status === 401) {
            localStorage.setItem('redirectUrl', router.currentRoute.fullPath);
            router.push('/login');
        }
        return Promise.reject(error);
    }
);


const getFileNameFromHeaders = (headers, defaultName = 'download') => {
    const cd = headers['content-disposition'] || headers['Content-Disposition'];

    if (cd) {
        // 1) RFC 5987: filename*=UTF-8''%E4%B8%AD%E6%96%87.xlsx
        let m = cd.match(/filename\*=UTF-8''([^;]+)/i);
        if (m && m[1]) {
            try { return decodeURIComponent(m[1]); } catch (_) { return m[1]; }
        }
        // 2) 常见的 filename=xxx（可能为 URL 编码或带引号）
        m = cd.match(/filename="?([^";]+)"?/i);
        if (m && m[1]) {
            const raw = m[1].trim().replace(/^["']|["']$/g, '');
            try { return decodeURIComponent(raw); } catch (_) { return raw; }
        }
    }
    // 兜底：依据 Content-Type 给扩展名
    const ct = (headers['content-type'] || headers['Content-Type'] || '').split(';')[0].toLowerCase();
    const extMap = {
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': '.xlsx',
        'application/vnd.ms-excel': '.xls',
        'application/pdf': '.pdf',
        'text/csv': '.csv',
        'application/zip': '.zip'
    };
    const ext = extMap[ct] || '';
    return `${defaultName}${ext}`;
};

const downloadFile = (url, config, defaultName = 'download') => {
    config.responseType = 'blob';
    return new Promise((resolve, reject) => {
        http.get(url, config).then(response => {
            const blob = new Blob([response.data]);
            const downloadUrl = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = downloadUrl;
            let fileName = getFileNameFromHeaders(response.headers, defaultName);
            if (!fileName) {
                fileName = defaultName;
            }
            link.download = fileName;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(downloadUrl);
            resolve();
        }).catch(error => {
            reject(error);
        });
    });
};

http.downloadFile = downloadFile;

export default http;