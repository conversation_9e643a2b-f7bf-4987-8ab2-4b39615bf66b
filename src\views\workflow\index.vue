<template>
    <div class="master-container">
      <!-- 左侧区域容器 -->
      <div class="left-area">
        <!-- 左侧节点面板 -->
        <div class="sidebar">
          <div class="sidebar-category">
            <div class="category-title">Jenkins Pipeline 节点</div>
            <div class="node-grid">
              <!-- 项目初始化节点 -->
              <div
                class="node-item"
                :class="{ 'node-disabled': isNodeOnCanvas('project-init') }"
                draggable="true"
                @dragstart="onDragStart($event, 'project-init')"
              >
                <div class="node-icon project-init">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24">
                    <path fill="#3b82f6" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                  </svg>
                </div>
                <div class="node-name">项目初始化</div>
              </div>

              <!-- Git配置节点 -->
              <div
                class="node-item"
                draggable="true"
                @dragstart="onDragStart($event, 'git-checkout')"
              >
                <div class="node-icon git-checkout">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24">
                    <path fill="#f05032" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                  </svg>
                </div>
                <div class="node-name">Git配置</div>
              </div>
    
              <!-- BAT脚本节点 -->
              <div
                class="node-item"
                draggable="true"
                @dragstart="onDragStart($event, 'bat-script')"
              >
                <div class="node-icon bat-script">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24">
                    <path fill="#10b981" d="M9 4H5a2 2 0 00-2 2v12a2 2 0 002 2h4m6-6h6m-6-4h6m-6-4h6M9 12h6"/>
                  </svg>
                </div>
                <div class="node-name">BAT脚本</div>
              </div>

              <!-- 旧版Jenkins节点(兼容) -->
              <div
                class="node-item"
                :class="{ 'node-disabled': isNodeOnCanvas('jenkins') }"
                draggable="true"
                @dragstart="onDragStart($event, 'jenkins')"
              >
                <div class="node-icon jenkins">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24">
                    <circle cx="12" cy="12" r="10" fill="#6b7280" />
                    <text x="12" y="16" font-size="12" text-anchor="middle" fill="white">J</text>
                  </svg>
                </div>
                <div class="node-name">旧版Jenkins</div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 日志部分单独放置，与sidebar有间距 -->
        <div class="operation-logs-section">
          <div class="log-header">操作日志</div>
          
          <!-- 分析结果链接 - 移到日志顶部，更加突出显示 -->
          <div v-if="analysisResultUrl" class="analysis-url-container">
            <el-link type="primary" :href="analysisResultUrl" target="_blank">
              <div class="analysis-link-content">
                <i class="el-icon-document"></i>
                <span>点击查看详细分析报告</span>
              </div>
            </el-link>
          </div>
          
          <div class="log-container">
            <div v-for="(log, index) in operationLogs" :key="index" class="log-item">
              {{ log }}
            </div>
          </div>
        </div>
      </div>
  
      <div id="vue-flow-container" class="main-content">
        <VueFlow
          v-model:nodes="nodes"
          v-model:edges="edges"
          :node-types="nodeTypes"
          :default-viewport="{ x: 150, y: 50, zoom: 1 }"
          :nodes-draggable="true"  
          :connect-on-click="false" 
          :select-nodes-on-drag="false" 
          :pan-on-drag="true" 
          :zoom-on-scroll="true"  
          :prevent-scrolling="false"
          class="flow-canvas"
          @dragover="onDragOver"
          @drop="onDrop"
          @node-click="onNodeClick"
          @edge-update="onEdgeUpdate"
          @connect="onConnect"
        >
          <Background />
          <div class="custom-controls">
            <!-- 显示当前流程信息 -->
            <div v-if="editMode !== 'normal'" class="flow-info">
              <div class="flow-title">
                <span class="flow-label">当前流程:</span>
                <span class="flow-value">{{ currentFlowName }}</span>
              </div>
              <div class="flow-project">
                <span class="flow-label">所属项目:</span>
                <span class="flow-value">{{ getProjectDisplayName(currentProject) }}</span>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="flow-actions">
              <button v-if="editMode !== 'normal'" @click="saveFlow" class="save-button">
                保存流程
              </button>
              <button @click="onSave">执行流水线</button>
              <button @click="clearCanvas">清空画布</button>
            </div>
          </div>
        </VueFlow>
      </div>
  
      <!-- 右侧抽屉 -->
      <div class="drawer-backdrop" v-if="showDrawer" @click="closeDrawer"></div>
      <div class="drawer" :class="{ 'drawer-open': showDrawer }" :style="{ width: drawerWidth + 'px' }">
        <div class="drawer-header">
          <div class="drawer-resize-handle" @mousedown="startResize"></div>
          <h3>{{ getDrawerTitle() }}</h3>
          <button class="close-button" @click="closeDrawer">×</button>
        </div>
        <div class="drawer-content">
          <GitClone
            v-if="configuringNodeType === 'git-clone'"
            :initial-config="configuringNodeData"
            @save="saveNodeConfig"
            @cancel="closeDrawer"
          />
          <Analyzer
            v-if="configuringNodeType === 'analysis'"
            :initial-config="configuringNodeData"
            @save="saveNodeConfig"
            @cancel="closeDrawer"
          />
          <GenerateDoc
            v-if="configuringNodeType === 'generate-doc'"
            :initial-config="configuringNodeData"
            @save="saveNodeConfig"
            @cancel="closeDrawer"
          />
          <PushPlatform
            v-if="configuringNodeType === 'push-platform'"
            :initial-config="configuringNodeData"
            @save="saveNodeConfig"
            @cancel="closeDrawer"
          />
          <Jenkins
            v-if="configuringNodeType === 'jenkins'"
            :initial-config="configuringNodeData"
            @save="saveNodeConfig"
            @cancel="closeDrawer"
          />
          <ProjectInit
            v-if="configuringNodeType === 'project-init'"
            :initial-config="configuringNodeData"
            @save="saveNodeConfig"
            @cancel="closeDrawer"
          />
          <StageConfig
            v-if="configuringNodeType === 'bat-script'"
            :initial-config="configuringNodeData"
            :stage-order="getBatScriptOrder(configuringNodeId)"
            @save="saveNodeConfig"
            @cancel="closeDrawer"
          />
          <GitNode
            v-if="configuringNodeType === 'git-checkout'"
            :data="configuringNodeData"
            :is-drawer-mode="true"
            @save="saveNodeConfig"
            @cancel="closeDrawer"
          />
        </div>
      </div>
    </div>
  </template>
  
  <script setup>
  import { ref, onMounted, markRaw, defineComponent, computed, nextTick, watch, h } from 'vue';
  import { VueFlow, Handle, Position, useVueFlow, addEdge  } from '@vue-flow/core';
  import { onBeforeUnmount } from 'vue';
  import { Background } from '@vue-flow/background';
  import { ElNotification, ElMessage, ElMessageBox } from 'element-plus';
  import 'element-plus/es/components/notification/style/css';
  import 'element-plus/es/components/message/style/css';
  import 'element-plus/es/components/message-box/style/css';
  import { useRoute, useRouter } from 'vue-router';
  import http from '@/utils/http/http.js';
  
  import GitClone from './components/GitClone.vue'; 
  import Analyzer from './components/Analyzer.vue'; 
  import GenerateDoc from './components/GenerateDoc.vue';
  import PushPlatform from './components/PushPlatform.vue';
  import Jenkins from './components/Jenkins.vue';
  import ProjectInit from './components/ProjectInit.vue';
  import StageConfig from './components/StageConfig.vue';
  import GitNode from './components/GitNode.vue';
  
  import '@vue-flow/core/dist/style.css';
  import '@vue-flow/core/dist/theme-default.css';
  
  // --- Custom Node Definitions ---
  const createNodeComponent = (type, headerText) => defineComponent({
    props: ['data', 'selected'],
    components: { Handle },
    template: `
      <div 
        class="workflow-node ${type}-node" 
        :class="{ 
          selected, 
          configured: data.configured,
          'status-running': data.executionStatus === 'running',
          'status-success': data.executionStatus === 'success',
          'status-failed': data.executionStatus === 'failed',
          'status-pending': data.executionStatus === 'pending'
        }"
      >
        <Handle v-if="'${type}' !== 'project-init'" type="target" :position="Position.Left" />
        
        <!-- 执行顺序徽章 -->
        <div v-if="data.executionOrder" class="order-badge" 
             :class="{
               'badge-running': data.executionStatus === 'running',
               'badge-success': data.executionStatus === 'success', 
               'badge-failed': data.executionStatus === 'failed'
             }">
          {{ data.executionOrder }}
        </div>
        
        <div class="node-header">
          {{ '${type}' === 'bat-script' && data.stage_name ? data.stage_name : '${headerText}' }}
          <span v-if="data.configured" class="configured-indicator">✔️</span>
          <span v-else class="configured-indicator pending">⚙️</span>
          
          <!-- 状态指示器 -->
          <div v-if="data.executionStatus" class="status-indicator" 
               :class="{
                 'indicator-running': data.executionStatus === 'running',
                 'indicator-success': data.executionStatus === 'success',
                 'indicator-failed': data.executionStatus === 'failed'
               }"></div>
        </div>
        <div class="node-content">{{ data.label || '未配置' }}</div>
         <Handle type="source" :position="Position.Right" />
      </div>
    `,
    setup() {
      return { Position };
    }
  });
  
  const nodeTypes = markRaw({
    gitCloneNode: createNodeComponent('git-clone', '克隆'),
    analysisNode: createNodeComponent('analysis', '分析'),
    docNode: createNodeComponent('generate-doc', '生成文档'),
    pushNode: createNodeComponent('push-platform', '推送平台'),
    jenkinsNode: createNodeComponent('jenkins', 'Jenkins'),
    projectInitNode: createNodeComponent('project-init', '项目初始化'),
    batScriptNode: createNodeComponent('bat-script', 'BAT脚本'),
    gitCheckoutNode: defineComponent({
      props: ['data', 'selected'],
      components: { Handle, GitNode },
      template: `
        <div 
          class="workflow-node git-checkout-node" 
          :class="{ 
            selected, 
            configured: data.configured,
            'status-running': data.executionStatus === 'running',
            'status-success': data.executionStatus === 'success',
            'status-failed': data.executionStatus === 'failed',
            'status-pending': data.executionStatus === 'pending'
          }"
        >
          <Handle type="target" :position="Position.Left" />
          
          <!-- 执行顺序徽章 -->
          <div v-if="data.executionOrder" class="order-badge"
               :class="{
                 'badge-running': data.executionStatus === 'running',
                 'badge-success': data.executionStatus === 'success',
                 'badge-failed': data.executionStatus === 'failed'
               }">
            {{ data.executionOrder }}
          </div>
          
          <div class="node-header">
            <span>Git 配置</span>
            <span v-if="data.configured" class="configured-indicator">✔️</span>
            <span v-else class="configured-indicator pending">⚙️</span>
            
            <!-- 状态指示器 -->
            <div v-if="data.executionStatus" class="status-indicator"
                 :class="{
                   'indicator-running': data.executionStatus === 'running',
                   'indicator-success': data.executionStatus === 'success',
                   'indicator-failed': data.executionStatus === 'failed'
                 }"></div>
          </div>
          <div class="node-content">{{ data.label || '未配置' }}</div>
          <Handle type="source" :position="Position.Right" />
        </div>
      `,
      setup() {
        return { Position };
      }
    })
  });
  
  
  const { addNodes } = useVueFlow();
  
  // 路由参数
  const route = useRoute();
  const router = useRouter();

  // 编辑模式状态
  const editMode = ref('normal'); // 'normal', 'create', 'edit'
  const currentProject = ref(null);
  const currentFlowName = ref('');
  const currentFlowConfig = ref('');
  const currentRecordId = ref(null);

  const nodes = ref([]);  //节点配置
  const edges = ref([]);
  const showDrawer = ref(false);
  const configuringNodeId = ref(null);
  const configuringNodeType = ref(null);
  const configuringNodeData = ref({});
  
  const requiredNodeTypes = ['git-clone', 'analysis', 'generate-doc', 'push-platform'];
  let nodeIdCounter = 0; 
  
  const isNodeOnCanvas = (nodeType) => {
    for (let i = 0; i < nodes.value.length; i++) {
      const node = nodes.value[i];
      if (node.data && node.data.type === nodeType) {
        return true;
      }
    }
    return false;
  
  };
  
  
  onMounted(() => {
    console.log('组件已挂载');

    // 检测URL参数，初始化编辑模式
    initializeMode();
  });

  // 格式化项目名称显示，添加项目编号
  const getProjectDisplayName = (project) => {
    if (!project) return '';

    // 优先使用projectCode字段
    if (project.projectCode) {
      return `${project.name}(${project.projectCode})`;
    }

    // 如果有number字段，显示为"项目名称(编号)"格式
    if (project.number) {
      return `${project.name}(${project.number})`;
    }

    // 如果没有number字段但有id，使用id
    if (project.id) {
      return `${project.name}(${project.id})`;
    }

    // 如果都没有，只显示名称
    return project.name;
  };

  // 初始化编辑模式
  const initializeMode = () => {
    const query = route.query;
    console.log('工作流编辑器接收到的参数:', query);

    if (query.mode === 'create') {
      // 创建模式
      editMode.value = 'create';

      // 调试信息
      console.log('项目ID:', query.projectId);
      console.log('项目名称:', query.projectName);
      console.log('项目编码:', query.projectCode);
      console.log('项目编号:', query.projectNumber);

      currentProject.value = {
        id: query.projectId,
        name: query.projectName,
        projectCode: query.projectCode,
        number: query.projectNumber || query.projectId
      };

      console.log('设置的当前项目:', currentProject.value);

      currentFlowName.value = query.flowName || '';
      currentFlowConfig.value = query.flowDescription || '';

      addLog(`🆕 创建新流程: ${currentFlowName.value}`);
      addLog(`📁 关联项目: ${getProjectDisplayName(currentProject.value)}`);
      addLog('请在左侧拖拽节点到画布开始配置流程');

    } else if (query.editId) {
      // 编辑模式
      editMode.value = 'edit';
      currentRecordId.value = query.editId;
      loadPipelineRecord(query.editId);

    } else {
      // 普通模式
      editMode.value = 'normal';
      addLog('欢迎使用工作流编辑器');
    }
  };

  // 加载流程记录进行编辑
  const loadPipelineRecord = async (recordId) => {
    try {
      addLog('正在加载流程记录...');

      const response = await http.get(`/auto_jenkins/projects/?editId=${recordId}`, {
        timeout: 30000
      });
      if (response.data.success) {
        const recordData = response.data.data;

        // 设置基本信息
        currentProject.value = { name: recordData.project_name };
        currentFlowName.value = recordData.flow_config;

        // 恢复前端配置
        if (recordData.frontend_raw_config) {
          restoreWorkflowFromConfig(recordData.frontend_raw_config);
          addLog(`✅ 已加载流程: ${recordData.flow_config}`);
        } else {
          throw new Error('流程记录中缺少前端配置数据');
        }
      }
    } catch (error) {
      addLog(`❌ 加载流程失败: ${error.message}`);
      ElNotification({
        title: '错误',
        message: '加载流程失败: ' + error.message,
        type: 'error'
      });
    }
  };

  // 保存流程
  const saveFlow = async () => {
    try {
      // 验证是否有节点
      if (nodes.value.length === 0) {
        ElNotification({
          title: '提示',
          message: '请先添加节点再保存',
          type: 'warning'
        });
        return;
      }

      addLog('正在保存流程...');

      // 获取Git配置信息
      const gitConfigNode = nodes.value.find(n => n.data?.type === 'git-checkout');
      const projectInitNode = nodes.value.find(n => n.data?.type === 'project-init');
      
      // 组装保存数据
      const saveData = {
        project_name: projectInitNode?.data?.project_name || currentProject.value?.name || '',
        flow_config: currentFlowName.value,
        
        // 从节点配置中提取必需字段
        git_url: gitConfigNode?.data?.git_url || gitConfigNode?.data?.gitUrl || '',
        branch: gitConfigNode?.data?.branch || 'master',
        
        // 所属项目信息
        belong_project: getProjectDisplayName(currentProject.value),

        // Jenkins执行配置（如果有配置的话）
        pipeline_config: assemblePipelineConfig(),

        // 前端恢复配置
        frontend_raw_config: {
          nodes: nodes.value.map(node => ({
            type: node.data.type,
            configured: node.data.configured,
            label: node.data.label,
            // 保存所有节点配置数据
            ...node.data
          })),
          // 简化的连线信息（按顺序连接）
          connections: edges.value.map(edge => ({
            source: edge.source,
            target: edge.target
          }))
        }
      };

      let response;
      if (editMode.value === 'create') {
        // 创建新记录
        response = await http.post('/auto_jenkins/projects/', saveData, {
          timeout: 8000
        });
      } else if (editMode.value === 'edit') {
        // 更新现有记录
        response = await http.put(`/auto_jenkins/projects/${currentRecordId.value}`, saveData, {
          timeout: 8000
        });
      }

      if (response && response.data.success) {
        addLog('✅ 流程保存成功');
        ElMessage({
          message: '流程保存成功',
          type: 'success'
        });

        // 如果是创建模式，切换到编辑模式
        if (editMode.value === 'create' && response.data.data?.id) {
          editMode.value = 'edit';
          currentRecordId.value = response.data.data.id;

          // 询问用户是否返回列表
          ElMessageBox.confirm(
            '流程已保存成功，是否返回流程记录列表？',
            '保存成功',
            {
              confirmButtonText: '返回列表',
              cancelButtonText: '继续编辑',
              type: 'success',
            }
          )
            .then(() => {
              // 返回列表
              router.push('/cicd/records');
            })
            .catch(() => {
              // 继续编辑，不做操作
            });
        } else {
          // 编辑模式下保存成功
          ElMessageBox.confirm(
            '流程已更新成功，是否返回流程记录列表？',
            '更新成功',
            {
              confirmButtonText: '返回列表',
              cancelButtonText: '继续编辑',
              type: 'success',
            }
          )
            .then(() => {
              // 返回列表
              router.push('/cicd/records');
            })
            .catch(() => {
              // 继续编辑，不做操作
            });
        }
      } else {
        throw new Error(response?.data?.message || '保存失败');
      }

    } catch (error) {
      addLog(`❌ 保存失败: ${error.message}`);
      ElNotification({
        title: '错误',
        message: '保存失败: ' + error.message,
        type: 'error'
      });
    }
  };

  // 组装Pipeline配置（简化版）
  const assemblePipelineConfig = () => {
    const projectInitNode = nodes.value.find(n => n.data?.type === 'project-init');
    const gitConfigNode = nodes.value.find(n => n.data?.type === 'git-checkout');
    const batScriptNodes = nodes.value.filter(n => n.data?.type === 'bat-script');

    if (!projectInitNode) {
      return {};
    }

    return {
      project_name: projectInitNode.data?.project_name || currentProject.value?.name || '',
      git_url: gitConfigNode?.data?.git_url || '',
      branch: gitConfigNode?.data?.branch || 'master',
      agent: projectInitNode.data?.agent || 'slave_win10',
      environment: projectInitNode.data?.environment || {},
      stages: batScriptNodes.map(batScript => ({
        name: batScript.data?.stage_name || 'Stage',
        commands: batScript.data?.commands || []
      }))
    };
  };

  // 从配置恢复工作流
  const restoreWorkflowFromConfig = (config) => {
    // 清空现有节点和连线
    nodes.value = [];
    edges.value = [];

    // 恢复节点（按顺序自动排列）
    if (config.nodes && config.nodes.length > 0) {
      config.nodes.forEach((nodeConfig, index) => {
        const newNode = {
          id: `node_${index}`,
          type: getNodeComponentType(nodeConfig.type),
          data: {
            ...nodeConfig,
            label: nodeConfig.label || `${getNodeLabel(nodeConfig.type)} (已配置)`
          },
          // 自动计算位置，水平排列
          position: { x: 100 + (index * 250), y: 100 }
        };

        nodes.value.push(newNode);
      });

      // 恢复连线（按顺序连接）
      for (let i = 0; i < nodes.value.length - 1; i++) {
        edges.value.push({
          id: `edge_${i}_${i+1}`,
          source: `node_${i}`,
          target: `node_${i+1}`,
          animated: true
        });
      }

      addLog(`📊 恢复了 ${nodes.value.length} 个节点和 ${edges.value.length} 条连线`);
    }
  };
  
  
  const handleEscapeOnly = (event) => {
      if (event.key === 'Escape') {
          showDrawer.value = false;
      }
  };
  
  const setupEscKeyListener = () => {
      if (showDrawer.value) {
       //   console.log('抽屉打开,添加ESC键监听'); 
          document.addEventListener('keydown', handleEscapeOnly);
      } else {
          //   console.log('抽屉关闭,移除ESC键监听');   
          document.removeEventListener('keydown', handleEscapeOnly);
      }
  };
  
  watch(showDrawer, (newVal) => {
        setupEscKeyListener();
  });
  
  onBeforeUnmount(() => {
      document.removeEventListener('keydown', handleEscapeOnly);
      // 清理轮询定时器
      stopPollingTaskStatus();
  });
  
  
  
  const getId = () => `node_${nodeIdCounter++}`;
  
  //  1 开始拖拽
  const onDragStart = (event, nodeType) => {
     // 只有项目初始化节点需要检查是否已存在
     if (nodeType === 'project-init' && isNodeOnCanvas(nodeType)) {
       event.preventDefault();
       return;
     }
    if (event.dataTransfer) {
      event.dataTransfer.setData('application/vnd.flow.nodeType', nodeType);
      event.dataTransfer.effectAllowed = 'move';
    }
  };
  
  //  2 悬停在画布上 还未有放置好
  const onDragOver = (event) => {
    event.preventDefault();
    if (event.dataTransfer) {
      event.dataTransfer.dropEffect = 'move';
    }
  };
  
  
  
  const getNodeComponentType = (nodeType) => {
    switch (nodeType) {
      case 'git-clone': return 'gitCloneNode';
      case 'analysis': return 'analysisNode';
      case 'generate-doc': return 'docNode';
      case 'push-platform': return 'pushNode';
      case 'jenkins': return 'jenkinsNode';
      case 'project-init': return 'projectInitNode';
      case 'bat-script': return 'batScriptNode';
      case 'git-checkout': return 'gitCheckoutNode';
      default: return 'default';
    }
  };
  
  
  const getNodeLabel = (nodeType) => {
    switch (nodeType) {
      case 'git-clone': return '代码克隆';
      case 'analysis': return '代码分析';
      case 'generate-doc': return '文档生成';
      case 'push-platform': return '推送平台';
      case 'jenkins': return 'Jenkins';
      case 'project-init': return '项目初始化';
      case 'bat-script': return 'BAT脚本';
      case 'git-checkout': return 'Git配置';
      default: return nodeType;
    }
  };
  
  // 获取下一个执行顺序号（通用函数）
  const getNextExecutionOrder = () => {
    // 获取所有有执行顺序的节点
    const nodesWithOrder = nodes.value.filter(n => n.data?.executionOrder);
    if (nodesWithOrder.length === 0) return 1;
    
    // 找到最大的执行顺序号
    const maxOrder = Math.max(...nodesWithOrder.map(n => n.data.executionOrder));
    return maxOrder + 1;
  };

  // 更新所有节点的执行顺序
  const updateAllExecutionOrder = () => {
    // 获取所有需要执行顺序的节点（排除旧版节点）
    const executionNodes = nodes.value.filter(n => 
      n.data?.type && !['git-clone', 'analysis', 'generate-doc', 'push-platform', 'jenkins'].includes(n.data.type)
    );
    
    // 按x坐标排序
    const sortedNodes = [...executionNodes].sort((a, b) => a.position.x - b.position.x);
    
    // 重新分配执行顺序
    sortedNodes.forEach((node, index) => {
      node.data.executionOrder = index + 1;
    });
  };
  
  //  3 放置节点 
  const onDrop = (event) => {
    event.preventDefault();
    
    const nodeType = event.dataTransfer?.getData('application/vnd.flow.nodeType');
  
    if (!nodeType) {
      return;
    }
    
    // 项目初始化节点只能有一个
    if (nodeType === 'project-init' && isNodeOnCanvas(nodeType)) {
      return;
    }
  
    const newNode = {
      id: getId(),
      type: getNodeComponentType(nodeType),  
      data: {
        label: nodeType === 'git-checkout' ? '未配置Git' : `配置 ${getNodeLabel(nodeType)}`, 
        type: nodeType, 
        configured: false,
        executionStatus: 'pending',
        // 为所有节点类型分配执行顺序（除了旧版节点）
        executionOrder: !['git-clone', 'analysis', 'generate-doc', 'push-platform', 'jenkins'].includes(nodeType) 
          ? getNextExecutionOrder() : null,
      },
      position: getInitialNodePosition(nodeType),
    };
  
    addNodes([newNode]); 
    
    // 如果是bat-script节点，自动连线和更新顺序
    nextTick(() => {
      autoConnectBatScripts();
      updateAllExecutionOrder();
    });
  
    openDrawerForNode(newNode);  //4  放置以后 打开右侧的抽屉组件
  };
  
  
  const getInitialNodePosition = (nodeType) => {
      const yPos = 100;
      const spacing = 300;
      
      if (nodeType === 'project-init') {
        return { x: 50, y: yPos };
      }
      
      if (nodeType === 'git-checkout') {
        // Git配置节点放在项目初始化节点右侧
        return { x: 50 + spacing, y: yPos };
      }
      
      if (nodeType === 'bat-script') {
        // 获取现有BAT脚本节点数量，BAT脚本应该在Git配置节点之后开始
        const batScriptNodes = nodes.value.filter(n => n.data?.type === 'bat-script');
        const batScriptCount = batScriptNodes.length;
        // 项目初始化(50) + Git配置(350) + BAT脚本起始位置(650)
        return { x: 650 + (batScriptCount * spacing), y: yPos };
      }
      
      // 兼容旧版节点
      switch(nodeType) {
          case 'git-clone': return { x: 5, y: yPos };
          case 'analysis': return { x: 5 + spacing, y: yPos };
          case 'generate-doc': return { x: 5 + spacing * 2, y: yPos };
          case 'push-platform': return { x: 5 + spacing * 3, y: yPos };
          case 'jenkins': return { x: 5 + spacing * 4, y: yPos };
          default: return { x: 100, y: 100 };
      }
  };
  
  // 处理连线事件 - 新增方法
  const onConnect = (params) => {
  
    const newEdge = {
      id: `e-${params.source}-${params.target}`,
      source: params.source,
      target: params.target,
      animated: true,
      style: { stroke: '#6366f1', strokeWidth: 2 }
    };
    edges.value = addEdge(newEdge, edges.value);
  };
  
  
  const onEdgeUpdate = (oldEdge, newConnection) => {
    edges.value = edges.value.filter(e => e.id !== oldEdge.id);
    const newEdge = {
      ...oldEdge,
      source: newConnection.source || oldEdge.source,
      target: newConnection.target || oldEdge.target,
    };
    edges.value.push(newEdge);
 //   console.log("Edge updated:", newEdge);
  };
  
  
  const onNodeClick = (event) => {
    // console.log("Node clicked:", event.node);
    openDrawerForNode(event.node);
  };
  
  // 4 填入节点 打开抽屉  赋值当前变量
  const openDrawerForNode = (node) => {
    if (!node || !node.data || !node.data.type) return;
    configuringNodeId.value = node.id;
    configuringNodeType.value = node.data.type;
    // 深拷贝节点数据，确保包含所有已配置的数据
    configuringNodeData.value = JSON.parse(JSON.stringify(node.data)); 
    showDrawer.value = true;
  };
  
  const closeDrawer = () => {
    showDrawer.value = false;
    configuringNodeId.value = null;
    configuringNodeType.value = null;
    configuringNodeData.value = {};
  };
  
  // 5 子组件点击确认  提交结果
  const saveNodeConfig = (config) => {
    if (!configuringNodeId.value) return;
    
    // 使用 reactive 更新而不是重新赋值整个数组
    const nodeIndex = nodes.value.findIndex(n => n.id === configuringNodeId.value);
    
    if (nodeIndex > -1) {
      // 保存原始的 type 信息
      const originalType = nodes.value[nodeIndex].data.type;
      
      // 根据节点类型生成不同的label
      let nodeLabel = '';
      if (originalType === 'git-checkout') {
        nodeLabel = 'Git配置 (已配置)';
      } else if (originalType === 'bat-script') {
        // BAT脚本节点显示Stage名称
        nodeLabel = config.stage_name ? `${config.stage_name} (已配置)` : 'BAT脚本 (已配置)';
      } else {
        nodeLabel = `${getNodeLabel(originalType)} (已配置)`;
      }
      
      // 直接更新节点数据，确保响应式更新
      nodes.value[nodeIndex].data = {
        type: originalType, // 确保保留原始类型
        ...config,
        configured: true,
        label: nodeLabel,
        executionStatus: nodes.value[nodeIndex].data.executionStatus || 'pending',
        executionOrder: nodes.value[nodeIndex].data.executionOrder
      };
      
      // 触发响应式更新
      nextTick(() => {
        if (originalType === 'bat-script') {
          updateAllExecutionOrder();
        }
        autoConnectBatScripts();
        checkAndConnectNodes();
      });
    }
    
    closeDrawer();
  };
  
    // 获取BAT脚本节点的顺序号
  const getBatScriptOrder = (nodeId) => {
    const node = nodes.value.find(n => n.id === nodeId);
    return node?.data?.executionOrder || 1;
  };
  
  // Git配置节点现在可以从侧边栏拖拽，不再自动生成
  
  // 自动连线BAT脚本节点
  const autoConnectBatScripts = () => {
    const projectInitNode = nodes.value.find(n => n.data?.type === 'project-init');
    const gitCheckoutNode = nodes.value.find(n => n.data?.type === 'git-checkout');
    const batScriptNodes = nodes.value.filter(n => n.data?.type === 'bat-script');
    
    // 清除旧的自动连线
    edges.value = edges.value.filter(edge => !edge.id.startsWith('auto-'));
    
    if (!projectInitNode) return;
    
    let currentSource = projectInitNode;
    
    // 连接到Git配置节点
    if (gitCheckoutNode) {
      edges.value.push({
        id: 'auto-init-to-git',
        source: projectInitNode.id,
        target: gitCheckoutNode.id,
        animated: true,
        style: { stroke: '#10b981', strokeWidth: 2 }
      });
      currentSource = gitCheckoutNode;
    }
    
    // 连接到BAT脚本
    if (batScriptNodes.length > 0) {
      // 按x坐标排序
      const sortedBatScripts = [...batScriptNodes].sort((a, b) => a.position.x - b.position.x);
      
      // 连接当前源到第一个BAT脚本
      edges.value.push({
        id: 'auto-to-bat1',
        source: currentSource.id,
        target: sortedBatScripts[0].id,
        animated: true,
        style: { stroke: '#10b981', strokeWidth: 2 }
      });
      
      // 连接各个BAT脚本
      for (let i = 1; i < sortedBatScripts.length; i++) {
        edges.value.push({
          id: `auto-bat${i}-to-bat${i+1}`,
          source: sortedBatScripts[i-1].id,
          target: sortedBatScripts[i].id,
          animated: true,
          style: { stroke: '#10b981', strokeWidth: 2 }
        });
      }
    }
  };
  
  // 新的验证逻辑 - 适用于新版Jenkins Pipeline
  const validateNewJenkinsPipeline = () => {
    const projectInitNode = nodes.value.find(n => n.data?.type === 'project-init');
    const gitConfigNode = nodes.value.find(n => n.data?.type === 'git-checkout');
    const batScriptNodes = nodes.value.filter(n => n.data?.type === 'bat-script');
    
    if (!projectInitNode) {
      ElNotification({
        title: '错误',
        message: '需要一个项目初始化节点！',
        type: 'error',
        duration: 3000
      });
      return false;
    }
    
    if (!projectInitNode.data?.configured) {
      ElNotification({
        title: '错误',
        message: '项目初始化节点尚未配置完成！',
        type: 'error',
        duration: 3000
      });
      return false;
    }
    
    if (!gitConfigNode) {
      ElNotification({
        title: '错误',
        message: '需要一个Git配置节点！',
        type: 'error',
        duration: 3000
      });
      return false;
    }
    
    if (!gitConfigNode.data?.configured) {
      ElNotification({
        title: '错误',
        message: 'Git配置节点尚未配置完成！',
        type: 'error',
        duration: 3000
      });
      return false;
    }
    
    if (batScriptNodes.length === 0) {
      ElNotification({
        title: '错误',
        message: '至少需要一个BAT脚本节点！',
        type: 'error',
        duration: 3000
      });
      return false;
    }
    
    // 检查所有BAT脚本是否已配置
    for (const batScript of batScriptNodes) {
      if (!batScript.data?.configured) {
        ElNotification({
          title: '错误',
          message: `BAT脚本节点 "${batScript.data?.stage_name || '未命名'}" 尚未配置完成！`,
          type: 'error',
          duration: 3000
        });
        return false;
      }
    }
    
    return true;
  };
  
  // 组装新版Pipeline数据
  const assembleNewJenkinsPipelineData = () => {
    const projectInitNode = nodes.value.find(n => n.data?.type === 'project-init');
    const gitConfigNode = nodes.value.find(n => n.data?.type === 'git-checkout');
    const batScriptNodes = nodes.value.filter(n => n.data?.type === 'bat-script');
    
    // 按x坐标排序，确定正确的执行顺序
    const sortedBatScripts = [...batScriptNodes].sort((a, b) => a.position.x - b.position.x);
    
    // 确保参数格式正确，每个参数都有 type 字段
    const formatParameters = (params) => {
      if (!Array.isArray(params)) return [];
      return params.map(param => ({
        type: 'string',  // 固定为 string 类型
        name: param.name || '',
        default_value: param.default_value || '',
        description: param.description || ''
      }));
    };
    
    return {
      project_name: projectInitNode.data.project_name,
      git_url: gitConfigNode?.data?.git_url || gitConfigNode?.data?.gitUrl || '',
      branch: gitConfigNode?.data?.branch || 'master',
      agent: projectInitNode.data.agent || 'slave_win10',
      pipeline_config: {
        agent: projectInitNode.data.agent || 'slave_win10',
        environment: projectInitNode.data.environment || {},
        parameters: formatParameters(projectInitNode.data.parameters),
        stages: sortedBatScripts.map((batScript, index) => ({
          name: batScript.data.stage_name,
          commands: batScript.data.commands
        }))
      }
    };
  };
  
  // Check if all required nodes are present and configured, then create edges
  const checkAndConnectNodes = () => {
    const nodeMap = new Map(); 
    let allConfigured = true;
  
  
    for (const type of requiredNodeTypes) {
      const node = nodes.value.find(n => n.data?.type === type);
      if (node) {
        nodeMap.set(type, node);
        if (!node.data?.configured) {
          allConfigured = false;
   //       console.log(`Node type ${type} found but not configured.`);
        }
      } else {
        allConfigured = false; 
     //   console.log(`Required node type ${type} not found on canvas.`);
        break; 
      }
    }
  
    // 保留用户创建的连线
    const userEdges = edges.value.filter(edge => {
      // 查找是否是自动创建的默认连线的ID
      return !(['e-clone-analysis', 'e-analysis-doc', 'e-doc-push'].includes(edge.id));
    });
    
    // 如果所有节点已配置，创建默认连线
    if (allConfigured && nodeMap.size === requiredNodeTypes.length) {
   //   console.log("All required nodes are configured. Creating connections.");
      const cloneNode = nodeMap.get('git-clone');
      const analysisNode = nodeMap.get('analysis');
      const docNode = nodeMap.get('generate-doc');
      const pushNode = nodeMap.get('push-platform');
  
      // 检查默认连线是否已存在
      const hasDefaultCloneAnalysis = edges.value.some(e => e.id === 'e-clone-analysis');
      const hasDefaultAnalysisDoc = edges.value.some(e => e.id === 'e-analysis-doc');
      const hasDefaultDocPush = edges.value.some(e => e.id === 'e-doc-push');
  
      // 创建默认连线数组
      const defaultEdges = [];
      
      if (!hasDefaultCloneAnalysis) {
        defaultEdges.push({ 
          id: 'e-clone-analysis', 
          source: cloneNode.id, 
          target: analysisNode.id, 
          animated: true, 
          style: { stroke: '#6366f1', strokeWidth: 2 } 
        });
      }
      
      if (!hasDefaultAnalysisDoc) {
        defaultEdges.push({ 
          id: 'e-analysis-doc', 
          source: analysisNode.id, 
          target: docNode.id, 
          animated: true, 
          style: { stroke: '#6366f1', strokeWidth: 2 } 
        });
      }
      
      if (!hasDefaultDocPush) {
        defaultEdges.push({ 
          id: 'e-doc-push', 
          source: docNode.id, 
          target: pushNode.id, 
          animated: true, 
          style: { stroke: '#6366f1', strokeWidth: 2 } 
        });
      }
      
      // 合并用户连线和默认连线
      edges.value = [...userEdges, ...defaultEdges];
 //     console.log("Edges updated:", edges.value);
    } else {
      // 如果不满足创建默认连线的条件，只保留用户创建的连线
      edges.value = userEdges;
 //     console.log("Conditions not met for automatic connection, keeping user edges.");
    }
  };
  
  // Add a new variable to track active notifications
  const activeNotifications = ref(new Set());
  const notificationShown = ref(false);
  
  // Validate before saving
  const validateWorkflow = () => {
      // 如果已经显示过通知，直接返回false
      if (notificationShown.value) {
          return false;
      }
      
      const presentTypes = new Set(nodes.value.map(n => n.data?.type));
      let allPresent = true;
      let allConfigured = true;
  
      for (const type of requiredNodeTypes) {
          if (!presentTypes.has(type)) {
              allPresent = false;
              // 只显示一个通知，可以手动关闭或自动消失
              ElNotification({
                  title: '错误',
                  message: `错误：必需的节点 "${getNodeLabel(type)}" 不在画布上！`,
                  type: 'error',
                  duration: 3000,
                  showClose: true,
                  onClose: () => {
                      setTimeout(() => {
                          notificationShown.value = false;
                      }, 1000);
                  }
              });
              notificationShown.value = true;
              return false;
          }
          
          const node = nodes.value.find(n => n.data?.type === type);
          if (!node?.data?.configured) {
              allConfigured = false;
              // 只显示一个通知，可以手动关闭或自动消失
              ElNotification({
                  title: '错误',
                  message: `错误：节点 "${getNodeLabel(type)}" 尚未配置完成！`,
                  type: 'error',
                  duration: 3000,
                  showClose: true,
                  onClose: () => {
                      setTimeout(() => {
                          notificationShown.value = false;
                      }, 1000);
                  }
              });
              notificationShown.value = true;
              return false;
          }
      }
  
      return true; 
  };
  
  // 添加日志和结果URL相关变量
  const operationLogs = ref([]);
  const analysisResultUrl = ref('');

  // 抽屉拖拽相关变量
  const drawerWidth = ref(500);
  const isResizing = ref(false);
  const startX = ref(0);
  const startWidth = ref(0);
  
  // 添加日志函数
  const addLog = (log) => {
    const now = new Date();
    const hours = now.getHours().toString().padStart(2, '0');
    const minutes = now.getMinutes().toString().padStart(2, '0');
    const seconds = now.getSeconds().toString().padStart(2, '0');
    const timeString = `${hours}:${minutes}:${seconds}`;
    operationLogs.value.unshift(`${timeString} - ${log}`);
  };
  
  // 修改onSave方法
  const onSave = () => {
    notificationShown.value = false;
    
    // 检查是否使用新版Jenkins Pipeline
    const projectInitNode = nodes.value.find(n => n.data?.type === 'project-init');
    const batScriptNodes = nodes.value.filter(n => n.data?.type === 'bat-script');
    const jenkinsNodes = nodes.value.filter(n => n.data?.type === 'jenkins');
    
    if (projectInitNode || batScriptNodes.length > 0) {
      // 新版Jenkins Pipeline流程
      if (!validateNewJenkinsPipeline()) {
        return;
      }
      
      const pipelineData = assembleNewJenkinsPipelineData();
      addLog('开始执行Jenkins Pipeline...');
      addLog(`项目: ${pipelineData.project_name}`);
      addLog(`Git URL: ${pipelineData.git_url}`);
      addLog(`分支: ${pipelineData.branch}`);
      addLog(`Agent: ${pipelineData.agent}`);
      addLog(`BAT脚本数量: ${pipelineData.pipeline_config.stages.length}`);
      
      // 调试日志：显示完整的pipeline数据
      console.log('Pipeline数据:', JSON.stringify(pipelineData, null, 2));
      
      executeJenkins(pipelineData);
      return;
    }
    
    // 检查是否只有一个 Jenkins 节点(旧版兼容)
    if (jenkinsNodes.length === 1 && nodes.value.length === 1) {
      const jenkinsNode = jenkinsNodes[0];
      if (jenkinsNode.data?.configured) {
        // 执行 Jenkins 流水线
        executeJenkins(jenkinsNode.data);
        return;
      } else {
        ElNotification({
          title: '错误',
          message: 'Jenkins 节点尚未配置完成！',
          type: 'error',
          duration: 3000
        });
        return;
      }
    }
    
    // 原有的流水线验证和执行逻辑
    if (!validateWorkflow()) {
      return;
    }
    
    // 执行流水线
    addLog('开始执行流水线...');
    addLog('使用固定项目: HiwaySDK_2.0');
    
    // 固定项目名
    const projectName = 'HiwaySDK_2.0';
    
    // 执行克隆操作
    addLog(`开始克隆项目: ${projectName}...`);
    
    // Promise链式调用
    cloneProject(projectName)
      .then(cloneResult => {
        if (cloneResult) {
          addLog(`成功克隆项目: ${projectName}`);
          addLog('开始扫描项目...');
          return scanProject(projectName);
        } else {
          throw new Error('克隆失败');
        }
      })
      .then(scanResult => {
        if (scanResult === 1004) {
          addLog('扫描完成');
          addLog('开始分析项目代码...');
          return analyzeProject(projectName);
        } else if (scanResult === 1002) {
          addLog('项目未克隆');
          throw new Error('项目未克隆');
        } else if (scanResult === 1003) {
          addLog('项目中没有.c文件，无法进行分析');
          throw new Error('项目中没有.c文件');
        } else {
          throw new Error('扫描失败');
        }
      })
      .then(([result, analysis_result, parent_url]) => {
        if (result === true) {
          addLog('代码分析完成');
          if (Array.isArray(analysis_result) && analysis_result.length > 0) {
            const firstFile = analysis_result[0];
            addLog(`示例文件: ${firstFile.文件名}`);
            addLog(`  路径: ${firstFile.文件路径}`);
            addLog(`  头文件: ${firstFile.头文件}`);
                    
            if (firstFile.函数列表.length > 0) {
              const function_list = firstFile.函数列表.length > 3 ? 3 : firstFile.函数列表.length;
              addLog(`  包含函数 (显示前${function_list}个):`);
              firstFile.函数列表.slice(0, function_list).forEach(func => {
                addLog(`    - ${func.函数名}`);
              });
              if (firstFile.函数列表.length > function_list) {
                addLog(`    ...以及其他 ${firstFile.函数列表.length - function_list} 个函数`);
              }
            }
            addLog(`其余 ${analysis_result.length - 1} 个文件的信息已省略`);
          }
          if (parent_url) {
            analysisResultUrl.value = parent_url;
            addLog(`分析结果已生成，可以查看完整报告: ${parent_url}`);
          }
          addLog('开始推送至产品开发平台...');
          addLog('功能待开发中');
        //   return pushProject(projectName);
        } else {
          throw new Error('分析失败');
        }
      })
      .then(result => {
        addLog('流水线执行完成');
        ElNotification({
          title: '成功',
          message: '流水线执行完成',
          type: 'success',
          duration: 3000
        });
      })
      .catch(error => {
        addLog(`流水线执行失败: ${error.message}`);
        ElNotification({
          title: '错误',
          message: `流水线执行失败: ${error.message}`,
          type: 'error',
          duration: 3000
        });
      });
  };
  
  // API调用函数
  const cloneProject = async (projectName) => {
    try {
      addLog('正在克隆...');
      const response = await http.get('/testqueuegit_project_clone', {
        timeout: 50000,
        params: {
          project_name: projectName
        }
      });
      addLog(response.data.message);
      return response.data.result;
    } catch (error) {
 //     console.error('克隆项目失败:', error);
      addLog('克隆项目失败，请检查网络连接');
      return false;
    }
  };
  
  const scanProject = async (projectName) => {
    try {
      addLog('正在扫描...');
      const response = await http.get('/testqueueproject_scanner', {
        timeout: 30000,
        params: {
          project_name: projectName
        }
      });
      addLog(response.data.message);
      return response.data.code;
    } catch (error) {
   //   console.error('扫描项目失败:', error);
      addLog('扫描项目失败，请检查网络连接');
      return false;
    }
  };
  
  const analyzeProject = async (projectName) => {
    try {
      addLog('正在分析...');
      const response = await http.get('/testqueueproject_analysis', {
        timeout: 30000,
        params: {
          project_name: projectName
        }
      });
      return [response.data.result, response.data.analysis_result, response.data.parent_url];
    } catch (error) {
      console.error('分析项目失败:', error);
      addLog('分析项目失败，请检查网络连接');
      return [false, null, null];
    }
  };
  
  // Jenkins执行相关变量
  const currentTaskId = ref(null);
  const isPollingLogs = ref(false);
  const pollLogsTimer = ref(null);

  // 执行状态解析器
  const ExecutionStatusParser = {
    parse(logs) {
      // 最新日志在最后，倒序查找更快
      const sortedLogs = [...logs].reverse();

      // 1) 检查是否已完成
      const completedLog = sortedLogs.find(log => /步(?:骤)?完成/.test(log) || /构建成功/.test(log));
      if (completedLog) {
        const match = completedLog.match(/(\d+)\/(\d+)\s*步(?:骤)?完成/);
        return {
          status: 'completed',
          progress: match ? `${match[1]}/${match[2]}` : undefined
        };
      }

      // 2) 解析 "正在执行: <Stage> (X/Y)"
      const runningLog = sortedLogs.find(log => /正在执行[:：]/.test(log));
      if (runningLog) {
        // 允许中文/空格/字母/数字/符号，直到 "(" 之前
        const match = runningLog.match(/正在执行[:：]\s*([^\(]+?)\s*\((\d+)\/(\d+)\)/);
        if (match) {
          return {
            status: 'running',
            currentStage: match[1].trim(),
            currentStep: parseInt(match[2]),
            totalSteps: parseInt(match[3])
          };
        }
      }

      // 3) 检查是否已开始构建
      const startedLog = sortedLogs.find(log => /构建已开始|已触发Jenkins任务/.test(log));
      if (startedLog) {
        return { status: 'started' };
      }

      return { status: 'idle' };
    }
  };

  // 节点状态管理器
  const NodeStatusManager = {
    updateStatuses(executionInfo) {
      console.log('🚀 更新节点状态:', executionInfo);
      const orderedNodes = this.getExecutionOrder();
      
      if (executionInfo.status === 'completed') {
        console.log('✅ 任务完成，设置所有节点为成功状态');
        this.setAllNodesStatus('success');
        this.triggerCompletionEffect();
        nodes.value = [...nodes.value]; // 强制重绘
        return;
      }
      
      if (executionInfo.status === 'running' && executionInfo.currentStage) {
        console.log('🏃 执行中，当前阶段:', executionInfo.currentStage);
        // 根据stage名称找到当前执行节点
        const currentIndex = this.findCurrentStageIndex(orderedNodes, executionInfo.currentStage);
        console.log('📍 当前执行节点索引:', currentIndex);
        
        orderedNodes.forEach((node, index) => {
          const newStatus = index < currentIndex ? 'success' : 
                           index === currentIndex ? 'running' : 'pending';
          
          console.log(`🔄 节点${index} (${node.data.stage_name || node.data.type}): ${node.data.executionStatus} → ${newStatus}`);
          node.data.executionStatus = newStatus;
          this.updateLabel(node, newStatus);
        });
      } else if (executionInfo.status === 'started') {
        console.log('🎬 构建开始，设置第一个节点为运行状态');
        // 构建刚开始，第一个节点执行中
        orderedNodes.forEach((node, index) => {
          const newStatus = index === 0 ? 'running' : 'pending';
          console.log(`🔄 启动节点${index}: ${node.data.executionStatus} → ${newStatus}`);
          node.data.executionStatus = newStatus;
          this.updateLabel(node, newStatus);
        });
      }

      nodes.value = [...nodes.value];   // 赋值  然后重新绘制
    },
    
    getExecutionOrder() {
      // 根据节点位置x坐标排序，确定执行顺序
      return nodes.value
        .filter(n => ['project-init', 'git-checkout', 'bat-script'].includes(n.data?.type))
        .sort((a, b) => a.position.x - b.position.x);
    },
    
    findCurrentStageIndex(orderedNodes, stageName) {
      const exactIndex = orderedNodes.findIndex(node => {
        const sn = (node.data.stage_name || '').trim();
        return sn === stageName;
      });
      if (exactIndex !== -1) return exactIndex;

      // 模糊匹配（忽略大小写、空格差异）
      const lower = stageName.toLowerCase();
      const fuzzyIndex = orderedNodes.findIndex(node => {
        const label = (node.data.label || node.data.stage_name || '').toLowerCase();
        return label.includes(lower);
      });
      return fuzzyIndex === -1 ? 0 : fuzzyIndex;
    },
    
    setAllNodesStatus(status) {
      const orderedNodes = this.getExecutionOrder();
      orderedNodes.forEach(node => {
        node.data.executionStatus = status;
        this.updateLabel(node, status);
      });
    },
    
    updateExecutionOrder(orderedNodes) {
      orderedNodes.forEach((node, index) => {
        node.data.executionOrder = index + 1;
      });
    },
    
    triggerCompletionEffect() {
      // 添加完成特效
      addLog('🎉 流水线执行完成！');
      
      // 添加整体完成特效
      const canvasContainer = document.querySelector('.vue-flow');
      if (canvasContainer) {
        canvasContainer.classList.add('pipeline-completed');
        setTimeout(() => {
          canvasContainer.classList.remove('pipeline-completed');
        }, 3000);
      }
    },
    
    // 根据执行状态更新 label 的后缀文字
    updateLabel(node, status) {
      const suffixMap = {
        success: '已完成',
        running: '运行中',
        pending: '待执行',
        failed: '失败',
      };
      const suffix = suffixMap[status];
      if (!suffix) return;
      // 去掉原有括号内容，替换为新的后缀
      const base = (node.data.label || '').replace(/（.*?）|\(.*?\)/, '').trim();
      node.data.label = `${base} (${suffix})`;
    },
  };

  // 在 analyzeProject 函数之后添加 executeJenkins 函数
  const executeJenkins = async (jenkinsData) => {
    try {
      addLog('开始执行 Jenkins 流水线...');
      addLog('项目: ' + jenkinsData.project_name);
      addLog('Git URL: ' + jenkinsData.git_url);
      addLog('Agent: ' + jenkinsData.agent);
      addLog('Branch: ' + jenkinsData.branch);
      
      // 1. 创建或更新Jenkins项目
      addLog('步骤 1: 创建Jenkins项目配置...');
      const createResponse = await http.post('/auto_jenkins/projects/', jenkinsData, {
        baseURL: 'http://*********:9000',
        timeout: 30000
      });
      
      if (createResponse.data.success) {
        addLog('✅ Jenkins项目创建成功: ' + createResponse.data.data.jenkins_job_name);
        
        // 2. 触发构建
        addLog('步骤 2: 触发Jenkins构建...');
        
        // 从pipeline_config中提取构建参数
        const buildParameters = {};
        if (jenkinsData.pipeline_config && jenkinsData.pipeline_config.environment) {
          Object.assign(buildParameters, jenkinsData.pipeline_config.environment);
        }
        
        addLog('构建参数: ' + JSON.stringify(buildParameters));
        
        const buildResponse = await http.post(`/auto_jenkins/projects/${jenkinsData.project_name}/build/`, {
          parameters: buildParameters
        }, {
          baseURL: 'http://*********:9000',
          timeout: 30000
        });
        
        if (buildResponse.data.success) {
          addLog('✅ 构建已触发，任务ID: ' + buildResponse.data.data.task_id);
          currentTaskId.value = buildResponse.data.data.task_id;
          
          // 3. 开始轮询日志
          startPollingTaskStatus(buildResponse.data.data.task_id);
          
          ElNotification({
            title: '成功',
            message: 'Jenkins 构建已开始，正在监控进度...',
            type: 'success',
            duration: 3000
          });
        } else {
          throw new Error(buildResponse.data.message || '触发构建失败');
        }
      } else {
        throw new Error(createResponse.data.message || '创建项目失败');
      }
      
    } catch (error) {
      console.error('Jenkins 流水线执行失败:', error);
      addLog('❌ Jenkins 流水线执行失败: ' + error.message);
      
      ElNotification({  
        title: '错误',
        message: 'Jenkins 流水线执行失败: ' + error.message,
        type: 'error',
        duration: 3000
      });
    }
  };

  // 轮询任务状态和日志
  const startPollingTaskStatus = (taskId) => {
    if (isPollingLogs.value) {
      clearInterval(pollLogsTimer.value);
    }
    
    isPollingLogs.value = true;
    addLog('开始监控任务状态...');
    
    const pollStatus = async () => {
      try {
        const response = await http.get(`/auto_jenkins/tasks/${taskId}/status/`,{
          baseURL: 'http://*********:9000',
          timeout: 30000
        });
        
        if (response.data.success) {
          const taskData = response.data.data;
          
          // 显示最新日志
          if (taskData.latest_logs && taskData.latest_logs.length > 0) {
            taskData.latest_logs.forEach(log => {
              addLog('📝 ' + log);
            });
            
            // 解析执行状态并更新节点
            const executionInfo = ExecutionStatusParser.parse(taskData.latest_logs);
            console.log('解析的执行状态:', executionInfo);
            console.log('原始日志:', taskData.latest_logs);
            
            // 更新节点状态
            console.log('更新节点状态前，当前节点状态:');
            const orderedNodes = NodeStatusManager.getExecutionOrder();
            orderedNodes.forEach(node => {
              console.log(`节点 ${node.data.stage_name || node.data.type}: ${node.data.executionStatus}`);
            });
            
            NodeStatusManager.updateStatuses(executionInfo);
            
            console.log('更新节点状态后，当前节点状态:');
            orderedNodes.forEach(node => {
              console.log(`节点 ${node.data.stage_name || node.data.type}: ${node.data.executionStatus}`);
            });
          }
          
          // 检查任务是否完成
          if (taskData.status === 'success') {
            addLog('🎉 Jenkins构建成功完成！');
            NodeStatusManager.setAllNodesStatus('success');
            NodeStatusManager.triggerCompletionEffect();
            stopPollingTaskStatus();
            ElNotification({
              title: '成功',
              message: 'Jenkins构建成功完成！',
              type: 'success',
              duration: 5000
            });
          } else if (taskData.status === 'failed') {
            addLog('💥 Jenkins构建失败！');
            if (taskData.error_message) {
              addLog('错误信息: ' + taskData.error_message);
            }
            NodeStatusManager.setAllNodesStatus('failed');
            stopPollingTaskStatus();
            ElNotification({
              title: '失败',
              message: 'Jenkins构建失败！',
              type: 'error',
              duration: 5000
            });
          }
          // 如果状态是 running 或 queued，继续轮询
        } else {
          addLog('⚠️ 获取任务状态失败: ' + response.data.message);
        }
        
      } catch (error) {
        addLog('⚠️ 轮询状态异常: ' + error.message);
      }
    };
    
    // 立即执行一次
    pollStatus();
    
    // 每3秒轮询一次
    pollLogsTimer.value = setInterval(pollStatus, 5000);
  };

  const stopPollingTaskStatus = () => {
    if (pollLogsTimer.value) {
      clearInterval(pollLogsTimer.value);
      pollLogsTimer.value = null;
    }
    isPollingLogs.value = false;
    addLog('停止监控任务状态');
  };
  
  
  

  
  // 清空整个画布
  const clearCanvas = () => {
    nodes.value = [];
    edges.value = [];
    nodeIdCounter = 0;
    console.log("画布已清空");
    // 重置通知状态
    notificationShown.value = false;
    activeNotifications.value.clear();
  };
  
  // 抽屉拖拽相关方法
  const startResize = (e) => {
    isResizing.value = true;
    startX.value = e.clientX;
    startWidth.value = drawerWidth.value;
    
    document.addEventListener('mousemove', handleResize);
    document.addEventListener('mouseup', stopResize);
    document.body.style.cursor = 'ew-resize';
    document.body.style.userSelect = 'none';
  };

  const handleResize = (e) => {
    if (!isResizing.value) return;
    
    const deltaX = startX.value - e.clientX;
    const newWidth = startWidth.value + deltaX;
    
    // 限制抽屉宽度范围
    if (newWidth >= 300 && newWidth <= 800) {
      drawerWidth.value = newWidth;
    }
  };

  const stopResize = () => {
    isResizing.value = false;
    document.removeEventListener('mousemove', handleResize);
    document.removeEventListener('mouseup', stopResize);
    document.body.style.cursor = '';
    document.body.style.userSelect = '';
  };

  const getDrawerTitle = () => {
    switch (configuringNodeType.value) {
      case 'git-clone': return 'Git Clone 配置';
      case 'analysis': return '分析配置';
      case 'generate-doc': return '生成文档配置';
      case 'push-platform': return '推送产品开发平台配置';
      case 'jenkins': return 'Jenkins 配置';
      case 'project-init': return '项目初始化配置';
      case 'bat-script': return 'BAT脚本配置';
      case 'git-checkout': return 'Git 配置';
      default: return '节点配置';
    }
  };
  
  // 测试增强动效
  window.testEnhancedEffects = () => {
    const orderedNodes = NodeStatusManager.getExecutionOrder();
    if (orderedNodes.length >= 2) {
      orderedNodes[0].data.executionStatus = 'success';
      orderedNodes[1].data.executionStatus = 'running';
      console.log('已应用增强动效：第一个节点为success，第二个节点为running');
    }
  };
  
  </script>
  
  <style>
  html, body {
    margin: 0;
    padding: 0;
    height: 100%;
    overflow: hidden;
  }
  
  .master-container {
    display: flex;
    width: 100%;
    height: 100vh; /* 使用视口高度确保占满整个视口 */
    padding-bottom: 60px; /* 添加底部填充 */
    box-sizing: border-box; /* 确保padding不会增加总高度 */
    overflow: hidden;
  }
  
  .left-area {
    display: flex;
    flex-direction: column;
    width: 280px;
    height: 100vh;
    flex-shrink: 0;
  }
  
  .sidebar {
    flex: 0 0 auto; /* 不伸缩，保持原始大小 */
    background-color: #ffffff;
    border-right: 1px solid #e5e7eb;
    overflow-y: auto;
    padding: 16px;
    z-index: 10;
    max-height: 50vh; /* 设置最大高度，确保sidebar不会占用过多空间 */
  }
  
  .sidebar-category {
    margin-bottom: 24px;
  }
  
  .category-title {
    font-size: 14px;
    font-weight: 500;
    color: #374151;
    margin-bottom: 12px;
    padding-left: 8px;
  }
  
  .node-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }
  
  .node-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 12px 8px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    cursor: grab;
    transition: all 0.2s;
    user-select: none;
    background-color: #fff; 
  }
  
  .node-item:active {
    cursor: grabbing;
  }
  
  
  .node-item.node-disabled {
    cursor: not-allowed;
    opacity: 0.5;
    background-color: #f9fafb;
  }
  .node-item.node-disabled:hover {
     box-shadow: none;
     transform: none;
  }
  
  
  .node-item:not(.node-disabled):hover {
    background-color: #f9fafb;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
  }
  
  .node-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 8px;
    border-radius: 8px;
    background-color: #f3f4f6;
  }
  
  .node-name {
    font-size: 12px;
    color: #1f2937;
    text-align: center;
  }
  
  .main-content {
    flex: 1;
    height: 100vh;
    position: relative;
    background-color: #f3f4f6ab;
    border-radius: 8px;
  }
  .flow-canvas {
    width: 100%;
    height: 100%;
    position: relative; /* 相对定位 */
  }
  
  /* --- Workflow Node Styles --- */
  .workflow-node {
    min-width: 180px; /* Slightly wider */
    border-radius: 8px;
    background: white;
    border: 1px solid #cbd5e1; /* Default border */
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    overflow: visible;
    position: relative;
    padding: 0; /* Remove padding if header/content handle it */
     transition: border-color 0.2s ease-in-out;
  }
  
  .workflow-node.selected {
    border-color: #4f46e5; /* Indigo border for selected */
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.2);
  }
  
  /* Style for configured nodes */
  .workflow-node.configured {
    border-color: #16a34a; /* Green border for configured */
  }
  .workflow-node.configured.selected {
    border-color: #16a34a; /* Keep green border even when selected */
     box-shadow: 0 0 0 3px rgba(22, 163, 74, 0.2);
  }
  
  
  .workflow-node .node-header {
    padding: 8px 12px;
    border-bottom: 1px solid #e2e8f0;
    font-weight: 600; /* Slightly bolder */
    font-size: 13px;
    text-align: left; /* Align left */
    display: flex;
    justify-content: space-between; /* Space out title and icon */
    align-items: center;
    background-color: #f8fafc; /* Light header background */
  }
  
  /* Indicator Icons */
  .configured-indicator {
    font-size: 14px;
    margin-left: 8px;
  }
  .configured-indicator.pending {
    color: #f59e0b; /* Amber color for pending */
  }
  
  
  .workflow-node .node-content {
    padding: 12px;
    font-size: 12px;
    color: #475569; /* Slightly darker text */
    text-align: left; /* Align left */
    min-height: 30px; /* Ensure some height even if no label */
  }
  
  
  
  .vue-flow__handle {
    width: 10px !important;
    height: 10px !important;
    background-color: #a855f7 !important; /* Purple handle */
    border: 1px solid white !important;
    border-radius: 50% !important;
    opacity: 0.8 !important;
    transition: background-color 0.2s ease-in-out;
  }
  .vue-flow__handle:hover {
    background-color: #7e22ce !important;
    opacity: 1 !important;
  }
  /* Position handles slightly off the edge */
  .vue-flow__handle-left { left: -6px !important; }
  .vue-flow__handle-right { right: -6px !important; }
  
  
  .vue-flow__edge-path {
    stroke: #8b5cf6 !important; /* Violet edge */
    stroke-width: 2px !important;
  }
  
  .vue-flow__edge.animated .vue-flow__edge-path {
    stroke-dasharray: 4 !important;
    animation: dashdraw 0.5s linear infinite !important;
  }
  
  @keyframes dashdraw {
    from { stroke-dashoffset: 8; }
  }
  
  /* --- Custom Controls --- */
  .custom-controls {
    position: absolute;
    top: 15px;
    right: 15px;
    z-index: 10;
    display: flex;
    flex-direction: column;
    gap: 10px;
    background-color: rgba(255, 255, 255, 0.95);
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.15);
    min-width: 280px;
  }

  .flow-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 12px;
    padding-bottom: 12px;
    border-bottom: 1px solid #e5e7eb;
  }

  .flow-title, .flow-project {
    display: flex;
    align-items: center;
    font-size: 14px;
    line-height: 1.4;
  }

  .flow-label {
    font-weight: 600;
    color: #6b7280;
    margin-right: 8px;
    min-width: 80px;
  }

  .flow-value {
    color: #111827;
    font-weight: 500;
    flex: 1;
  }

  .flow-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
  }

  .custom-controls button {
    background-color: #4F46E5;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    flex: 1;
    min-width: 80px;
  }

  .custom-controls button:hover {
    background-color: #4338CA;
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
  }

  .custom-controls button:active {
    transform: translateY(0);
  }

  .custom-controls .save-button {
    background-color: #10B981;
  }

  .custom-controls .save-button:hover {
    background-color: #059669;
  }
  
  .app-title {
    text-align: center;
    margin: 16px 0;
    font-size: 24px;
    font-weight: 600;
    color: #1F2937;
    letter-spacing: 1px;
  }
  
  /* --- Drawer Styles --- */
  .drawer-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.3); /* Slightly darker backdrop */
    z-index: 90;
    transition: opacity 0.3s ease-in-out;
  }
  
  .drawer {
    position: fixed;
    top: 0;
    right: -900px; /* Start further off-screen */
    width: 900px;   /* 继续增宽 */
    height: 100%;
    background-color: white;
    box-shadow: -3px 0 15px rgba(0, 0, 0, 0.15);
    z-index: 100;
    display: flex;
    flex-direction: column;
    transition: right 0.35s cubic-bezier(0.25, 0.8, 0.25, 1); /* Smoother transition */
  }
  
  .drawer-open {
    right: 0;
  }
  
  .drawer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    border-bottom: 1px solid #e5e7eb;
    flex-shrink: 0; /* Prevent header shrinking */
    position: relative;
  }

  .drawer-resize-handle {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 8px;
    background: transparent;
    cursor: ew-resize;
    z-index: 101;
  }

  .drawer-resize-handle:hover {
    background: linear-gradient(to right, #3b82f6, transparent);
  }
  
  .drawer-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600; /* Bolder title */
    color: #1f2937;
  }
  
  .close-button {
    border: none;
    background: none;
    font-size: 28px; /* Larger close icon */
    line-height: 1;
    cursor: pointer;
    width: 36px; /* Larger hit area */
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%; /* Circular */
    color: #6b7280;
    transition: background-color 0.2s, color 0.2s;
  }
  
  .close-button:hover {
    background-color: #f3f4f6;
    color: #1f2937;
  }
  
  .drawer-content {
    flex: 1;
    overflow-y: auto;
    padding: 24px;
    padding-left: 48px;
  }
  
  .operation-logs-section {
    flex: 1; /* 占据剩余空间 */
    margin-top: 20px; /* 与sidebar的间距 */
    border-right: 1px solid #e5e7eb;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    z-index: 10;
    overflow: hidden; /* 防止内容溢出 */
    min-height: 0; /* 允许flex正常收缩 */
  }
  
  .log-header {
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 500;
    color: #374151;
    border-bottom: 1px solid #f0f0f0;
  }
  
  .log-container {
    flex: 1;
    overflow-y: auto;
    padding: 8px;
    font-size: 12px;
    min-height: 250px; /* 确保日志容器有足够的高度 */
  }
  
  .log-item {
    padding: 2px 8px;
    font-family: monospace;
    line-height: 1.4;
    white-space: pre-wrap;
    word-break: break-all;
    overflow-wrap: break-word;
  }
  
  .analysis-url-container {
    padding: 10px 16px;
    margin: 0 8px 8px 8px;
    background-color: #f0f9eb;
    border-radius: 4px;
    border-left: 4px solid #67c23a;
    font-size: 14px;
  }
  
  .analysis-link-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }
  
  .analysis-link-content i {
    font-size: 16px;
  }
  
  /* 确保链接文本在小屏幕上不会被截断 */
  .analysis-url-container .el-link {
    width: 100%;
    white-space: normal;
    word-break: break-word;
  }

  .node-icon.jenkins {
    background-color: #d1fae5;
  }

  .jenkins-node .node-header {
    background-color: #ecfdf5;
  }
  
  .node-icon.project-init {
    background-color: #dbeafe;
  }
  
  .project-init-node .node-header {
    background-color: #eff6ff;
  }
  
  .node-icon.bat-script {
    background-color: #d1fae5;
  }
  
  .bat-script-node .node-header {
    background-color: #ecfdf5;
  }
  
  /* 执行顺序徽章 */
  .order-badge {
    position: absolute;
    top: -8px;
    left: -8px;
    width: 24px;
    height: 24px;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 600;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    z-index: 10;
  }
  
  /* 状态指示器 */
  .status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-left: 8px;
  }
  
  .status-indicator.pending {
    background-color: #9ca3af;
  }
  
  .status-indicator.running {
    background-color: #3b82f6;
    animation: pulse 2s infinite;
  }
  
  .status-indicator.success {
    background-color: #10b981;
  }
  
  .status-indicator.failed {
    background-color: #ef4444;
  }
  
  .status-indicator.skipped {
    background-color: #f59e0b;
  }
  
  /* 节点整体状态样式 */
  .workflow-node.pending {
    border-color: #9ca3af;
    background: #f9fafb;
    opacity: 0.8;
  }
  

  

  
  /* 执行状态样式 - 使用最高优先级 */
  .workflow-node.status-running {
    border: 3px solid #60a5fa !important;
    background: linear-gradient(135deg, #93c5fd, #60a5fa) !important;
    box-shadow: 0 0 20px rgba(96, 165, 250, 0.3) !important;
    animation: intense-pulse 1.5s infinite !important;
    transform: scale(1.06) !important;
  }

  .workflow-node.status-success {
    border: 3px solid #6ee7b7 !important;
    background: linear-gradient(135deg, #bbf7d0, #6ee7b7) !important;
    box-shadow: 0 0 18px rgba(110, 231, 183, 0.35) !important;
    animation: success-glow 2s ease-in-out !important;
    transform: scale(1.04) !important;
  }

  .workflow-node.status-failed {
    border: 3px solid #f87171 !important;
    background: linear-gradient(135deg, #fecaca, #f87171) !important;
    box-shadow: 0 0 18px rgba(248, 113, 113, 0.35) !important;
    animation: error-shake 0.5s ease-in-out !important;
    transform: scale(1.04) !important;
  }

  .order-badge.badge-running {
    background: #60a5fa !important;
    box-shadow: 0 0 10px rgba(96, 165, 250, 0.6) !important;
  }

  .order-badge.badge-success {
    background: #6ee7b7 !important;
    box-shadow: 0 0 8px rgba(110, 231, 183, 0.55) !important;
  }

  .order-badge.badge-failed {
    background: #f87171 !important;
    box-shadow: 0 0 8px rgba(248, 113, 113, 0.55) !important;
  }

  .status-indicator.indicator-running {
    background-color: #3b82f6 !important;
  }

  .status-indicator.indicator-success {
    background-color: #10b981 !important;
  }

  .status-indicator.indicator-failed {
    background-color: #ef4444 !important;
  }

  /* 动画效果 */
  @keyframes intense-pulse {
    0%, 100% { 
      transform: scale(1.05);
      box-shadow: 0 0 30px rgba(37, 99, 235, 0.6), 0 0 60px rgba(37, 99, 235, 0.3);
    }
    50% { 
      transform: scale(1.08);
      box-shadow: 0 0 40px rgba(37, 99, 235, 0.8), 0 0 80px rgba(37, 99, 235, 0.4);
    }
  }

  @keyframes border-flow {
    0% { 
      border-color: #2563eb;
    }
    25% { 
      border-color: #3b82f6;
    }
    50% { 
      border-color: #60a5fa;
    }
    75% { 
      border-color: #3b82f6;
    }
    100% { 
      border-color: #2563eb;
    }
  }

  @keyframes success-glow {
    0% { 
      transform: scale(1.02);
      box-shadow: 0 0 25px rgba(5, 150, 105, 0.5), 0 0 50px rgba(5, 150, 105, 0.2);
    }
    50% { 
      transform: scale(1.04);
      box-shadow: 0 0 35px rgba(5, 150, 105, 0.7), 0 0 70px rgba(5, 150, 105, 0.3);
    }
    100% { 
      transform: scale(1.02);
      box-shadow: 0 0 25px rgba(5, 150, 105, 0.5), 0 0 50px rgba(5, 150, 105, 0.2);
    }
  }

  @keyframes error-shake {
    0%, 100% { 
      transform: scale(1.02) translateX(0);
    }
    25% { 
      transform: scale(1.02) translateX(-2px);
    }
    75% { 
      transform: scale(1.02) translateX(2px);
    }
  }

  @keyframes badge-bounce {
    0%, 100% { 
      transform: scale(1.1) translateY(0);
    }
    50% { 
      transform: scale(1.15) translateY(-2px);
    }
  }

  @keyframes badge-success-flash {
    0%, 100% { 
      transform: scale(1.05);
      box-shadow: 0 0 12px rgba(4, 120, 87, 0.5);
    }
    50% { 
      transform: scale(1.1);
      box-shadow: 0 0 20px rgba(4, 120, 87, 0.8);
    }
  }

  @keyframes badge-error-pulse {
    0%, 100% { 
      transform: scale(1.05);
      opacity: 1;
    }
    50% { 
      transform: scale(1.1);
      opacity: 0.8;
    }
  }
  

  
    /* 执行顺序徽章样式 */
  .order-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #6b7280;
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    z-index: 10;
    border: 2px solid white;
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
  }
  

  
  @keyframes badge-pulse {
    0%, 100% { 
      transform: scale(1);
      opacity: 1;
    }
    50% { 
      transform: scale(1.1);
      opacity: 0.8;
    }
  }

  /* 脉动动效 */
  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }

  @keyframes pulse-border {
    0%, 100% {
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
    }
    50% {
      box-shadow: 0 0 0 6px rgba(59, 130, 246, 0.1);
    }
  }
  </style>
  