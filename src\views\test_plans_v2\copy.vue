<template>
    <div>
        <el-form :model="form" label-width="auto" :rules="rules" status-icon ref="formRef">
            <el-row :gutter="15" style="max-width: 1400px;">
                <el-col :span="10">
                    <el-form-item label="计划名称" prop="name">
                        <el-input v-model="form.name" placeholder="请输入计划名称"></el-input>
                    </el-form-item>
                    <el-form-item label="样品信息">
                        <el-input type="textarea" :rows="3" v-model="form.sample_information" placeholder="请输入样品信息"
                            maxlength="120" show-word-limit></el-input>
                    </el-form-item>
                    <el-form-item label="计划描述">
                        <el-input v-model="form.desc" type="textarea" :rows="3" placeholder="请输入计划描述"></el-input>
                    </el-form-item>
                </el-col>

                <el-col :span="14">
                    <el-form-item label="所属项目" prop="project_number">
                        <span>{{ form.project_name }}({{ form.project_number }})</span>
                    </el-form-item>
                    <el-row :gutter="10">
                        <el-col :span="12">
                            <el-form-item label="测试版本" prop="m_version">
                                <el-tree-select v-model="form.m_version" lazy clearable :load="loadVersions"
                                    :data="projectVersionData" v-if="projectVersionV"
                                    :props="{ label: 'label', value: 'label', children: 'children', isLeaf: 'isLeaf' }" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="次级版本">
                                <el-tree-select ref="subVersionsRef" v-model="form.sub_versions" lazy
                                    :load="loadVersions" multiple clearable :data="projectVersionData"
                                    :cache-data="initProjectVersionData" v-if="projectVersionV"
                                    :props="{ label: 'label', value: 'value', children: 'children', isLeaf: 'isLeaf' }">
                                </el-tree-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-form-item label="关联版本">
                        <el-tree-select ref="productVersionRef" v-model="form.product_version" lazy :load="loadVersions"
                            multiple clearable :data="projectVersionData" :cache-data="initProjectVersionData"
                            v-if="projectVersionV"
                            :props="{ label: 'label', value: 'value', children: 'children', isLeaf: 'isLeaf' }">
                        </el-tree-select>
                    </el-form-item>
                    <el-row :gutter="10">
                        <el-col :span="12">
                            <el-form-item label="测试类型" prop="test_type">
                                <el-select v-model="form.test_type" placeholder="请选择测试类型">
                                    <el-option v-for="item in test_types" :label="item.label"
                                        :value="item.remark"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="计划用途" prop="plan_use">
                                <el-select v-model="form.plan_use" placeholder="请选择计划用途">
                                    <el-option label="全功能测试" value="FULL_FUNCTIONALITY_TEST"></el-option>
                                    <el-option label="版本回归测试" value="VERSION_REGRESSION_TEST"></el-option>
                                    <el-option label="专项验证测试" value="SPECIFIC_VALIDATION_TEST"></el-option>
                                    <el-option label="问题验证测试" value="PROBLEM_VALIDATION_TEST"></el-option>
                                    <el-option label="耐久测试" value="DURABILITY_TEST"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="10">
                        <el-col :span="12">
                            <el-form-item label="异常停止" prop="abnormal_stop">
                                <el-select v-model="form.abnormal_stop">
                                    <el-option label="是" value="1"></el-option>
                                    <el-option label="否" value="0"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="负责人" prop="pic_email">
                                <Organizaiton v-model="form.pic_email" :cache-data="picInitData" ref="picRef" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="10">
                        <el-col :span="12">
                            <el-form-item label="计划开始时间" prop="p_start_time">
                                <el-date-picker v-model="form.p_start_time" type="date" placeholder="选择计划开始时间"
                                    format="YYYY-MM-DD" value-format="YYYY-MM-DD" style="width: 100%" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="计划结束时间" prop="p_end_time">
                                <el-date-picker v-model="form.p_end_time" type="date" placeholder="选择计划结束时间"
                                    format="YYYY-MM-DD" value-format="YYYY-MM-DD" style="width: 100%" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter=10>
                        <el-col :span="12">
                            <el-form-item label="计划类型" prop="plan_type">
                                <el-select v-model="form.plan_type" placeholder="请选择计划类型">
                                    <el-option label="对外发布" value="0"></el-option>
                                    <el-option label="内部验证" value="1"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="测试完成通知" prop="finish_notice">
                                <el-select v-model="form.finish_notice">
                                    <el-option label="是" value="1"></el-option>
                                    <el-option label="否" value="0"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-col>
            </el-row>
        </el-form>

        <div slot="footer" class="dialog-footer" style="display: flex; justify-content: end;">
            <el-button @click="cancel">取 消</el-button>
            <el-button type="primary" @click="confirm">确 定</el-button>
        </div>
    </div>
</template>


<script setup>
import { ref, onMounted } from 'vue';
import http from '@/utils/http/http.js';
import Organizaiton from '@/components/Organization/index.vue';
import { ElMessageBox } from 'element-plus';

const props = defineProps(
    {
        id: {
            type: Number,
            required: true
        }
    }
);

const formRef = ref(null);
const productVersionRef = ref(null);
const projectVersionData = ref([]);
const projectVersionDataInit = ref([]);
const projectVersionV = ref(true);
const test_types = ref([]);
const picInitData = ref([]);
const picRef = ref(null);
const modules = ref([]);
let actionTypeMap = ref({});
const subVersionsRef = ref(null);
const initProjectVersionData = ref([]);

let tptc_ids = [];

const form = ref({
    name: '',
    project_number: "",
    project_name: "",
    project_id: "",
    product_version: [],
    sub_versions: [],
    desc: '',
    m_version: '',
    abnormal_stop: '0',
    test_case_extra_args: {},
    test_type: '',
    test_type_name: '',
    plan_use: 'FULL_FUNCTIONALITY_TEST',
    pic_name: '',
    pic_email: '',
    p_start_time: null,
    p_end_time: null,
    sample_information: '',
    plan_type: '0',
    finish_notice: '0',
})

const rules = ref({
    name: [
        { required: true, message: '请输入计划名称', trigger: 'blur' }
    ],
    project_number: [
        { required: true, message: '请选择所属项目', trigger: 'change' }
    ],
    test_type: [
        { required: true, message: '请选择测试类型', trigger: 'change' }
    ],
    plan_use: [
        { required: true, message: '请选择计划用途', trigger: 'change' }
    ],
    p_start_time: [
        { required: true, message: '请选择计划开始时间', trigger: 'change' }
    ],
    p_end_time: [
        { required: true, message: '请选择计划结束时间', trigger: 'change' }
    ],
    m_version: [
        { required: true, message: '请选择主版本', trigger: 'change' }
    ],
    pic_email: [
        { required: true, message: '请选择负责人', trigger: 'change' }
    ],
    plan_type: [
        { required: true, message: '请选择计划类型', trigger: 'change' }
    ],
})


function updateOnCopyTestPlan() {
    const id = props.id;

    http.get(`/v2/test_plans/${id}`).then(res => {
        let data = res.data.data;
        form.value = {
            name: data.name + '_Copy',
            project_number: data.project_number,
            project_name: data.project_name,
            project_id: data.project_id,
            desc: data.desc,
            m_version: data.m_version,
            product_version: data.product_version.filter(item => item != null).map(item => item.id),
            sub_versions: data.sub_versions.filter(item => item != null).map(item => item.id),
            abnormal_stop: data.abnormal_stop ? '1' : '0',
            finish_notice: data.finish_notice ? '1' : '0',
            test_case_extra_args: data.test_case_extra_args,
            test_type: data.test_type,
            test_type_name: data.test_type_name,
            plan_use: data.plan_use,
            plan_type: String(data.plan_type),
            pic_name: data.pic_name,
            pic_email: data.pic_email,
            p_start_time: data.p_start_time,
            p_end_time: data.p_end_time,
            sample_information: data.sample_information,
        }
        picInitData.value = [{ label: data.pic_name, value: data.pic_email }];
        initProjectVersionData.value = data.product_version.filter(item => item != null).concat(data.sub_versions.filter(item => item != null));
        tptc_ids = data.test_cases.map(item => item.id);

        updateTestTypes();

    }).catch(err => {
        console.log(err);
    });

}

function loadVersions(node, resolve) {
    if (node.level === 0) {
        return resolve(projectVersionDataInit.value);
    }
    if (node.level === 1) {
        http.get('/issues/project_version_number',
            { params: { type: node.data.code, project_number: form.value.project_number } }).then(res => {
                if (res.data.err_code != 0) {
                    resolve([{ label: '无数据', value: '无数据', isLeaf: true, disabled: true }]);
                    return;
                }
                let data = res.data.data.records;
                data = data.map(item => {
                    item.label = item.name;
                    item.value = item.id;
                    item.type_name = node.data.label;
                    item.isLeaf = true;
                    return item;
                });
                resolve(data);
                if (data.length == 0) {
                    resolve([{ label: '无数据', value: '无数据', isLeaf: true, disabled: true }]);
                }
            });
    }
}

function updateTestTypes() {
    if (!form.value.project_number) {
        test_types.value = [];
        return;
    }
    http.get('/test_cases/test_types', { params: { project_number: form.value.project_number } }).then(res => {
        test_types.value = res.data.data;
    });
}

const emit = defineEmits(['confirm', 'cancel']);

function confirm() {
    form.value.test_type_name = test_types.value.find(item => item.remark == form.value.test_type)?.label;

    let pic = picRef.value.getNode(form.value.pic_email);

    form.value.pic_name = pic?.label;

    if (!form.value.pic_name) {
        ElMessageBox.alert(
            '负责人不能为空，请选择负责人！',
            '警告',
            {
                confirmButtonText: '确定',
                type: 'warning',
            }
        )
        return;
    }

    formRef.value.validate((valid) => {
        if (valid) {
            let data = {
                ...form.value,
                test_cases: tptc_ids,
                test_case_extra_args: JSON.stringify(form.value.test_case_extra_args),
            }

            let pv = data.product_version.map(item => {
                let i = initProjectVersionData.value.find(item2 => item2.value == item);
                if (i) {
                    return i;
                }
                return productVersionRef.value.getNode(item).data;
            });
            data.product_version = JSON.stringify(pv);

            let sub_versions = data.sub_versions.map(item => {
                let i = initProjectVersionData.value.find(item2 => item2.value == item);
                if (i) {
                    return i;
                }
                return subVersionsRef.value.getNode(item).data;
            });
            data.sub_versions = JSON.stringify(sub_versions);

            http.post(`/v2/test_plans/${props.id}/copy`, data).then(res => {
                ElMessage({
                    message: '保存成功.',
                    type: 'success',
                });
                emit('confirm');
            }).catch(err => {
                ElMessageBox.alert(
                    err.response.data.msg,
                    '保存失败',
                    {
                        confirmButtonText: '确定',
                        type: 'warning',
                    }
                )
            });
        } else {
            return false;
        }
    });
}

function cancel() {
    emit('cancel');
}

onMounted(() => {
    http.get('/issues/project_version_type').then(res => {
        let data = res.data.data.dictList;
        data = data.map(item => {
            item.value = item.id;
            item.isLeaf = false;
            return item;
        });
        projectVersionDataInit.value = data;
        projectVersionData.value = data;
    });

    updateTestTypes();

    updateOnCopyTestPlan();

    http.get('/functions').then(res => {
        let data = res.data.data.results;
        data.forEach(item => {
            item.m = item.number;
            if (item.children) {
                item.children.forEach(item2 => {
                    item2.m = item.number + '-' + item2.number;
                    if (item2.children) {
                        item2.children.forEach(item3 => {
                            item3.m = item.number + '-' + item2.number + '-' + item3.number;
                        });
                    }
                });
            }
        });
        modules.value = data;
    });

    http.get('/test_m/test_case_types', { params: { pagesize: 10000 } }).then(res => {
        res.data.data.results.forEach(item => {
            actionTypeMap.value[item.number] = item.name;
        });

    });
})

</script>


<style lang="scss" scoped>
.top-tool-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: white;
    height: 50px;
}

.tool-bar-container {
    display: flex;
    justify-content: start;
    align-items: center;
}

.filter-container {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    margin-bottom: 10px;

    .el-input,
    .el-select {
        width: 50%;
        max-width: 300px;
        margin-right: 10px;
    }
}

:deep(.el-collapse-item__arrow) {
    order: -1;
    margin: 0 8px 0 0;
}

:deep(.el-collapse-item__header) {
    font-size: 16px;
}

::v-deep .el-collapse-item__header {
    font-weight: bold;
    font-size: 15px;
}

.column-popper-title {
    border-bottom: 1px solid #ebeef5;
}

/* 自定义滚动条样式 */
.column-content::-webkit-scrollbar {
    width: 6px;
    /* 滚动条宽度 */
}

.column-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    /* 轨道背景色 */
    border-radius: 4px;
    /* 轨道圆角 */
}

/* 滚动条滑块默认样式（浅色） */
.column-content::-webkit-scrollbar-thumb {
    background: #e4e3e3;
    /* 浅色 */
    border-radius: 4px;
    /* 滑块圆角 */
}

/* 滚动条滑块悬停样式（深色） */
.column-content::-webkit-scrollbar-thumb:hover {
    background: #c7c7c7;
    /* 深色 */
}
</style>