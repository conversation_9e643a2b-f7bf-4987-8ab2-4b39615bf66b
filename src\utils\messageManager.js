import { ElMessage } from 'element-plus';

/**
 * ElMessage管理器 - 确保界面上只显示一条消息
 * 如果存在历史消息，则先清除历史消息再显示新消息
 */
class MessageManager {
  constructor() {
    this.currentMessage = null;
    this.isClearing = false; // 防止重复清除
  }

  /**
   * 清除当前显示的消息
   */
  clear() {
    if (this.currentMessage && !this.isClearing) {
      this.isClearing = true;
      try {
        this.currentMessage.close();
      } catch (error) {
        console.warn('清除消息时出错:', error);
      } finally {
        this.currentMessage = null;
        this.isClearing = false;
      }
    }
  }

  /**
   * 通用消息显示方法
   * @param {string} type - 消息类型 (success, error, warning, info)
   * @param {string} message - 消息内容
   * @param {number} duration - 显示时长，默认3000ms
   * @returns {Object} 消息实例
   */
  _showMessage(type, message, duration = 3000) {
    // 确保先清除现有消息
    this.clear();

    try {
      this.currentMessage = ElMessage[type]({
        message,
        duration,
        onClose: () => {
          this.currentMessage = null;
        }
      });
      return this.currentMessage;
    } catch (error) {
      console.error('显示消息时出错:', error);
      return null;
    }
  }

  /**
   * 显示成功消息
   * @param {string} message - 消息内容
   * @param {number} duration - 显示时长，默认3000ms
   * @returns {Object} 消息实例
   */
  success(message, duration = 3000) {
    return this._showMessage('success', message, duration);
  }

  /**
   * 显示错误消息
   * @param {string} message - 消息内容
   * @param {number} duration - 显示时长，默认3000ms
   * @returns {Object} 消息实例
   */
  error(message, duration = 3000) {
    return this._showMessage('error', message, duration);
  }

  /**
   * 显示警告消息
   * @param {string} message - 消息内容
   * @param {number} duration - 显示时长，默认3000ms
   * @returns {Object} 消息实例
   */
  warning(message, duration = 3000) {
    return this._showMessage('warning', message, duration);
  }

  /**
   * 显示信息消息
   * @param {string} message - 消息内容
   * @param {number} duration - 显示时长，默认3000ms
   * @returns {Object} 消息实例
   */
  info(message, duration = 3000) {
    return this._showMessage('info', message, duration);
  }

  /**
   * 检查是否有消息正在显示
   * @returns {boolean} 是否有消息正在显示
   */
  hasMessage() {
    return this.currentMessage !== null;
  }

  /**
   * 获取当前消息实例
   * @returns {Object|null} 当前消息实例
   */
  getCurrentMessage() {
    return this.currentMessage;
  }
}

// 创建全局单例实例
const messageManager = new MessageManager();

export default messageManager;
