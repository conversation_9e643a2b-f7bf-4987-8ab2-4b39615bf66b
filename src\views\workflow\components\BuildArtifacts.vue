<template>
  <div class="artifacts-form">
    <el-form :model="form" label-width="140px">
      
      <el-form-item label="启用编译产物归档" required>
        <el-switch v-model="form.enable_build_artifacts" />
        <span style="margin-left: 12px; color: #606266; font-size: 14px;">
          启用后，构建完成时会自动归档编译产物
        </span>
      </el-form-item>

      <template v-if="form.enable_build_artifacts">
        <el-form-item label="打包目录" required>
          <el-input v-model="form.archive_pattern" placeholder="例如: Version" />
          <div style="margin-top: 8px; color: #909399; font-size: 12px;">
            要归档的目录或文件模式，相对于工作空间根目录
          </div>
        </el-form-item>

        <el-form-item label="归档模式">
          <el-radio-group v-model="form.archive_mode">
            <el-radio value="directory">整个目录</el-radio>
            <el-radio value="pattern">文件模式</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="文件模式" v-if="form.archive_mode === 'pattern'">
          <el-input v-model="form.file_pattern" placeholder="例如: **/*.zip, **/*.exe" />
          <div style="margin-top: 8px; color: #909399; font-size: 12px;">
            支持通配符，多个模式用逗号分隔
          </div>
        </el-form-item>

        <el-form-item label="允许空归档">
          <el-switch v-model="form.allow_empty_archive" />
          <span style="margin-left: 12px; color: #606266; font-size: 14px;">
            当没有匹配的文件时不报错
          </span>
        </el-form-item>

        <el-form-item label="生成指纹">
          <el-switch v-model="form.fingerprint" />
          <span style="margin-left: 12px; color: #606266; font-size: 14px;">
            为归档文件生成唯一指纹，便于追踪
          </span>
        </el-form-item>

        <el-form-item label="仅成功时归档">
          <el-switch v-model="form.only_if_successful" />
          <span style="margin-left: 12px; color: #606266; font-size: 14px;">
            只有构建成功时才归档产物
          </span>
        </el-form-item>

        <el-form-item label="归档描述">
          <el-input 
            v-model="form.archive_description" 
            type="textarea" 
            :rows="3"
            placeholder="归档描述信息，例如: 构建产物 - ${BUILD_NUMBER}"
          />
        </el-form-item>
      </template>

      <el-divider />
      
      <el-form-item style="margin-bottom: 8px;">
        <el-button type="primary" @click="handleSave">保存配置</el-button>
        <el-button @click="$emit('cancel')">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  initialConfig: { type: Object, default: () => ({}) }
})
const emit = defineEmits(['save', 'cancel'])

// 默认表单数据
const defaultFormData = {
  enable_build_artifacts: true,
  archive_pattern: 'Version',
  archive_mode: 'directory', // 'directory' 或 'pattern'
  file_pattern: '**/*',
  allow_empty_archive: true,
  fingerprint: true,
  only_if_successful: false,
  archive_description: '构建产物 - ${BUILD_NUMBER}'
}

// 表单数据
const form = reactive({
  ...defaultFormData,
  ...props.initialConfig
})

// 监听 props 变化，更新表单数据
watch(() => props.initialConfig, (newConfig) => {
  Object.assign(form, defaultFormData, newConfig)
}, { deep: true, immediate: false })

// 保存配置
const handleSave = () => {
  // 验证必填项
  if (form.enable_build_artifacts) {
    if (!form.archive_pattern.trim()) {
      ElMessage.error('请填写打包目录')
      return
    }
    
    if (form.archive_mode === 'pattern' && !form.file_pattern.trim()) {
      ElMessage.error('请填写文件模式')
      return
    }
  }
  
  emit('save', form)
}
</script>

<style scoped>
.artifacts-form {
  padding: 20px;
}

/* 让表单项靠左对齐 */
.artifacts-form :deep(.el-form) {
  text-align: left;
}

/* 表单项整体靠左 */
.artifacts-form :deep(.el-form-item) {
  margin-bottom: 20px;
  text-align: left;
  display: flex;
  align-items: flex-start;
}

/* 标签靠左对齐，固定宽度 */
.artifacts-form :deep(.el-form-item__label) {
  text-align: left !important;
  justify-content: flex-start !important;
  width: 140px !important;
  min-width: 140px;
  padding-right: 12px;
  font-weight: 500;
  color: #606266;
}

/* 表单内容区域靠左 */
.artifacts-form :deep(.el-form-item__content) {
  text-align: left;
  flex: 1;
  margin-left: 0 !important;
}

/* 输入框、选择器等组件宽度调整 */
.artifacts-form :deep(.el-input),
.artifacts-form :deep(.el-select) {
  width: 100%;
  max-width: 300px;
}

/* Switch组件特殊处理 */
.artifacts-form :deep(.el-switch) {
  margin-right: 12px;
}

/* 按钮组靠左 */
.artifacts-form :deep(.el-form-item:last-child) {
  margin-top: 30px;
}

.artifacts-form :deep(.el-button) {
  margin-right: 12px;
}
</style>
