<template>
  <div class="sdk-list-container" v-custom-loading="loading">
    <!-- 工具栏：新增按钮 -->
    <div class="input-action-row">
      <el-button 
        icon="Plus"
        type="primary" 
        class="action-button" 
        @click="openAddDialog"
      >
        新增版本
      </el-button>
    </div>

    <!-- 新增版本弹窗 -->
    <el-dialog
      v-model="addDialogVisible"
      title="新增SDK版本信息"
      width="600px"
    >
      <el-form :model="addForm" label-width="120px" :rules="addRules" ref="addFormRef">
        <el-form-item label="版本号" prop="version">
          <el-input 
            v-model="addForm.version" 
            placeholder="请输入版本号（如1.0.0）" 
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="修改内容" prop="content">
          <el-input 
            v-model="addForm.content" 
            placeholder="请输入版本修改内容" 
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="发布人" prop="publisher">
          <el-input 
            v-model="addForm.publisher" 
            placeholder="请输入发布人姓名" 
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="发布日期" prop="date">
          <el-date-picker
            v-model="addForm.date"
            type="date"
            placeholder="选择发布日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="addDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleAddSave">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- SDK版本列表表格 -->
    <el-table
        class="version-table"
        :data="paginatedData"
        row-key="id"
        stripe
        border
        highlight-current-row
        style="width: 100%;"
      >
        <!-- 版本号 -->
        <el-table-column
          prop="version"
          label="版本号"
          min-width="200"
          align="center"
        />

        <!-- 修改内容 -->
        <el-table-column
          prop="content"
          label="修改内容"
          min-width="300"
          align="center"
        />

        <!-- 发布人 -->
        <el-table-column
          prop="publisher"
          label="发布人"
          min-width="200"
          align="center"
        />

        <!-- 发布日期 -->
        <el-table-column
          prop="date"
          label="发布日期"
          min-width="200"
          align="center"
        />

        <!-- 操作列 -->
        <el-table-column label="操作" align="center">
          <template #default="{ row }">
            <div style="display: flex; justify-content: center; gap: 10px;">
                <el-button
                  size="small"
                
                  type="primary"
                  @click="handleDetail(row)"
                  class="detail-btn"
                >
                  详情
                </el-button>
                <el-button
                  size="small"
                
                  type="primary"
                  @click="handleCopy(row)"
                  class="copy-btn"
                >
                  复制
                </el-button>
                <el-button
                  size="small"
                
                  type="danger"
                  @click="handleDelete(row)"
                  class="copy-btn"
                >
                  删除
                </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>


    <!-- 分页组件 -->
    <el-pagination
      class="custom-pagination"
      background
      layout="prev, pager, next, jumper, total, sizes"
      :page-sizes="[10, 20, 30, 50]"
      :current-page="currentPage"
      :page-size="pageSize"
      :total="tableData.length"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from "vue"
import { ElMessage, ElMessageBox} from "element-plus"
import { useRoute, useRouter } from 'vue-router';

// 加载状态
const loading = ref(false)

// 表格数据源
const tableData = ref([])

// 分页配置
const currentPage = ref(1)
const pageSize = ref(10)
const paginatedData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return tableData.value.slice(start, end)
})

// 新增弹窗配置
const addDialogVisible = ref(false)
const addFormRef = ref(null)
const addForm = reactive({
  version: "",
  content: "",
  publisher: "",
  date: ""
})
const addRules = reactive({
  version: [{ required: true, message: "请输入版本号", trigger: "blur" }],
  content: [{ required: true, message: "请输入修改内容", trigger: "blur" }],
  publisher: [{ required: true, message: "请输入发布人", trigger: "blur" }],
  date: [{ required: true, message: "请选择发布日期", trigger: "change" }]
})

// 详情弹窗配置
const detailDialogVisible = ref(false)
const detailForm = ref({})
const router = useRouter();

// 模拟获取接口数据
const fetchData = async () => {
  loading.value = true
  try {
    // 模拟接口返回数据
    const res = [
      { id: 1, version: "2.0.0", content: "基础版本数据", publisher: "system", date: "2025-07-14" }
    ]
    tableData.value = res
  } finally {
    loading.value = false
  }
}

// 打开新增弹窗
const openAddDialog = () => {
  // 重置表单
  if (addFormRef.value) addFormRef.value.resetFields()
  addDialogVisible.value = true
}

// 新增保存
const handleAddSave = async () => {
  await addFormRef.value?.validate(async (valid) => {
    if (valid) {
      // 模拟接口提交
      loading.value = true
      try {
        const newRow = {
          id: Date.now(), // 用时间戳作为临时ID
          ...addForm
        }
        tableData.value.unshift(newRow)
        addDialogVisible.value = false
        ElMessage.success("新增版本成功")
      } catch (err) {
        ElMessage.error("新增版本失败，请重试")
      } finally {
        loading.value = false
      }
    }
  })
}

// 查看详情
const handleDetail = (row) => {
  detailForm.value = { ...row }
  console.log(detailForm.value)
  router.push('/code_management/sdk_view')
}

// 复制数据
const handleCopy = (row) => {
  navigator.clipboard.writeText(JSON.stringify(row, null, 2))
  ElMessage.success("版本信息已复制到剪贴板")
}

// 删除数据
const handleDelete = (row) => {
  ElMessageBox.confirm(
    "确定要删除该版本信息吗？删除后不可恢复",
    "确认删除",
    {
      type: "warning"
    }
  ).then(async () => {
    // loading.value = true
    // try {
    //   // 过滤掉删除的行
    //   tableData.value = tableData.value.filter(item => item.id !== row.id)
    //   ElMessage.success("删除成功")
    // } catch (err) {
    //   ElMessage.error("删除失败，请重试")
    // } finally {
    //   loading.value = false
    // }
    ElMessageBox.confirm(
      "当前用户没有删除权限，无法进行删除"
    )
  }
)
}

// 分页事件
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1 // 切换页大小时重置到第一页
}
const handleCurrentChange = (val) => {
  currentPage.value = val
}

// 初始化加载数据
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
/* 容器样式，让容器占满视口高度 */
.sdk-list-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  /* padding: 20px; */
  box-sizing: border-box;
}

/* 表格容器占满剩余空间 */
.version-table {
  flex: 1;
  min-height: 0;
}

/* 确保表格无数据时下方显示空白 */
:deep(.el-table__body-wrapper) {
  min-height: calc(100% - 40px); /* 减去表头高度，可根据实际表头高度调整 */
}

/* 工具栏样式 */
.input-action-row {
  /* display: flex;
  flex-wrap: wrap; */
  gap: 15px;
  margin-bottom: 20px;
  /* padding: 0 5px; */
  justify-content: flex-end;
}

.action-button {
  background-color: #409eff;
  border-color: #409eff;
  /* color: white; */
  /* text-align: right; */
  
}

.action-button:hover {
  background-color: #337ecc;
  border-color: #337ecc;
}

/* 操作列按钮样式
.detail-btn,
.copy-btn {
  margin-right: 12px;
  font-size: 13px;
  color: #409eff;
} */


/* 自定义分页组件样式，使其位于屏幕底部左侧 */
.custom-pagination {
  margin-top: auto; /* 自动顶到底部 */
  align-self: flex-start; /* 左对齐 */
  padding: 15px 0;
  width: 100%;
}

/* 弹窗样式 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

.el-dialog :deep(.el-dialog__body) {
  padding: 20px 30px;
  max-height: 80vh;
  overflow-y: auto;
}

.el-dialog :deep(.el-dialog__header) {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}

.el-dialog :deep(.el-dialog__title) {
  font-weight: 600;
  font-size: 18px;
}

.el-dialog :deep(.el-dialog__footer) {
  border-top: 1px solid #ebeef5;
  padding: 15px 20px;
}

.el-dialog :deep(.el-input),
.el-dialog :deep(.el-date-picker) {
  width: 100%;
}
</style>