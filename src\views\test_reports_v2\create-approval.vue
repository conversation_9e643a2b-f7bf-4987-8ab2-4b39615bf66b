<template>
    <div>
        <el-affix :offset="63">
            <div class="top-tool-container">

                <h2>
                    创建软件发布评审
                </h2>

            </div>
        </el-affix>

        <el-divider style="margin: 0;"></el-divider>

        <div class="form-container">
            <el-form :model="form" label-width="auto" :rules="rules" status-icon ref="formRef">

                <el-form-item label="项目名称" prop="project_name">
                    <el-select v-model="form.project_name" placeholder="请选择项目名称" filterable clearable>
                        <el-option v-for="item in projectNameList" :key="item.id" :label="item.value"
                            :value="item.id"></el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="产品名称" prop="product_name">
                    <el-input v-model="form.product_name" placeholder="请输入产品名称"></el-input>
                </el-form-item>

                <el-form-item label="客户名称">
                    <el-select v-model="form.customer_name" placeholder="请选择客户名称" filterable clearable>
                        <el-option v-for="item in customerNameList" :key="item.id" :label="item.value"
                            :value="item.id"></el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="发布原因" prop="release_reason">
                    <el-select v-model="form.release_reason" placeholder="请选择发布原因" multiple clearable>
                        <el-option label="客户需求变更" value="l9r6ydji-rj80rvz0yz-0"></el-option>
                        <el-option label="内部提出变更" value="l9r6ysqp-58zqa3cnxdx-1"></el-option>
                        <el-option label="测试问题修复" value="l9r6yyhz-bn1pcfhjur9-1"></el-option>
                        <el-option label="供应商问题修复" value="l9r6z3nf-t6353vj42t-1"></el-option>
                        <el-option label="功能迭代" value="l9r6zco7-9po1agkfw9i-1"></el-option>
                    </el-select>
                </el-form-item>

                <version-list v-model="form.versions" :formRef="versionListRef" />

                <el-form-item label="升级验证" prop="ota_validate">
                    <el-select v-model="form.ota_validate" placeholder="请选择升级验证" multiple clearable>
                        <el-option label="主机验证" value="l9wlrbdm-mqfl626b3ib-0"></el-option>
                        <el-option label="VDS验证" value="l9wlrmx3-iqot4fwm12m-1"></el-option>
                        <el-option label="工具验证" value="l9wlrqto-lnsq60ejybj-1"></el-option>
                        <el-option label="未验证" value="l9wlrzn2-9fop0cn39hq-1"></el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="测试报告" prop="soft_report">
                    <upload-item ref="uploadSoftReportRef" v-model="form.soft_report"></upload-item>
                </el-form-item>

                <el-form-item label="线下评审纪要" prop="soft_review_minutes">
                    <upload-item ref="uploadSoftReviewMinutesRef" v-model="form.soft_review_minutes"></upload-item>
                </el-form-item>

                <el-form-item label="烧录工具及SOP" prop="test_tool_update">
                    <el-select v-model="form.test_tool_update" placeholder="请选择烧录工具及SOP" clearable>
                        <el-option label="是" value="l9qqa4n8-zd54iifi4j-0"></el-option>
                        <el-option label="否" value="l9qqa4ni-l80cdp7m89-1"></el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="测试责任人" prop="tester">
                    <Orga v-model="form.tester" :multiple="true" :valueField="'openId'" />
                </el-form-item>

                <el-form-item label="烧录SOP完成时间" prop="test_end_time">
                    <el-date-picker v-model="form.test_end_time" type="date" placeholder="请选择烧录SOP完成时间" />
                </el-form-item>

                <el-form-item label="烧录SOP备注">
                    <el-input type="textarea" :rows="3" v-model="form.test_note" placeholder="请输入烧录SOP备注"></el-input>
                </el-form-item>

                <el-form-item label="硬件开发工程师" prop="hardware_developer">
                    <Orga v-model="form.hardware_developer" :multiple="true" :valueField="'openId'" />
                </el-form-item>

                <el-form-item label="软件开发工程师" prop="software_developer">
                    <Orga v-model="form.software_developer" :multiple="true" :valueField="'openId'" />
                </el-form-item>

                <el-form-item label="项目PM" prop="pm">
                    <Orga v-model="form.pm" :multiple="true" :valueField="'openId'" />
                </el-form-item>

                <el-form-item label="NPI工程师" prop="npi">
                    <Orga v-model="form.npi" :multiple="true" :valueField="'openId'" />
                </el-form-item>
            </el-form>
        </div>

        <div class="submit-button-container">
            <el-button @click="onBack">返回</el-button>
            <el-button type="primary" @click="onSave" :loading="saveLoading">发起评审</el-button>
        </div>
    </div>
</template>

<script setup>
import { useRouter } from 'vue-router';
import http from '@/utils/http/http.js';
import { ref, onMounted, provide } from 'vue';
import VersionList from './version-list.vue';
import UploadItem from './upload-item.vue'
import Orga from '@/components/Organization/index2.vue';
import dayjs from 'dayjs';
import { ElMessageBox } from 'element-plus';


const router = useRouter();
const saveLoading = ref(false);
const formRef = ref(null);
const versionListRef = ref(null);

const projectNameList = ref([]);
const customerNameList = ref([]);
const softTypeList = ref([]);

const uploadSoftReportRef = ref(null);
const uploadSoftReviewMinutesRef = ref(null);

provide('softTypeList', softTypeList);

const form = ref({
    project_name: "",
    product_name: "",
    customer_name: "",
    release_reason: [],

    versions: [],

    ota_validate: [],

    test_tool_update: "",

    soft_report: "",
    soft_review_minutes: "",
    tester: [],
    test_end_time: "",
    test_note: "",
    hardware_developer: [],
    software_developer: [],
    pm: [],
    npi: []
});

const rules = ref({
    project_name: [
        { required: true, message: '请选择项目名称', trigger: 'change' }
    ],
    product_name: [
        { required: true, message: '请输入产品名称', trigger: 'blur' }
    ],
    customer_name: [
        { required: true, message: '请选择客户名称', trigger: 'change' }
    ],
    ota_validate: [
        { required: true, message: '请选择升级验证', trigger: 'change' }
    ],
    release_reason: [
        { required: true, message: '请选择发布原因', trigger: 'change' }
    ],
    test_tool_update: [
        { required: true, message: '请选择烧录工具及SOP', trigger: 'change' }
    ],
    soft_report: [
        { required: true, message: '请上传测试报告', trigger: 'change' }
    ],
    soft_review_minutes: [
        { required: true, message: '请上传线下评审纪要', trigger: 'change' }
    ],
    tester: [
        { required: true, message: '请输入测试责任人', trigger: 'blur' }
    ],
    test_end_time: [
        { required: true, message: '请选择烧录SOP完成时间', trigger: 'change' }
    ],
    hardware_developer: [
        { required: true, message: '请选择硬件开发工程师', trigger: 'blur' }
    ],
    software_developer: [
        { required: true, message: '请选择软件开发工程师', trigger: 'blur' }
    ],
    pm: [
        { required: true, message: '请选择项目PM', trigger: 'blur' }
    ],
    npi: [
        { required: true, message: '请选择NPI工程师', trigger: 'blur' }
    ]

});


const onBack = () => {
    router.back();
};

const onSave = () => {
    formRef.value.validate((valid) => {
        if (valid) {
            const formData = new FormData();

            formData.append("project_name", form.value.project_name);
            formData.append("product_name", form.value.product_name);
            formData.append("customer_name", form.value.customer_name);
            formData.append("release_reason", JSON.stringify(form.value.release_reason));
            formData.append("versions", JSON.stringify(form.value.versions));
            formData.append("ota_validate", JSON.stringify(form.value.ota_validate));
            formData.append("test_tool_update", form.value.test_tool_update);
            formData.append("soft_report", form.value.soft_report);
            formData.append("soft_review_minutes", form.value.soft_review_minutes);
            formData.append("tester", JSON.stringify(form.value.tester));
            formData.append("test_end_time", dayjs(form.value.test_end_time).format('YYYY-MM-DDTHH:mm:ss[Z]'));
            formData.append("test_note", form.value.test_note);
            formData.append("hardware_developer", JSON.stringify(form.value.hardware_developer));
            formData.append("software_developer", JSON.stringify(form.value.software_developer));
            formData.append("pm", JSON.stringify(form.value.pm));
            formData.append("npi", JSON.stringify(form.value.npi));

            saveLoading.value = true;
            http.post("/v2/test_reports/create_approval", formData, { headers: { "Content-Type": "multipart/form-data" } }).then((res) => {

                router.back();

            }).catch((error) => {
                ElMessageBox.alert(
                    error.response.data.msg,
                    '操作失败',
                    {
                        confirmButtonText: '确定',
                        type: 'warning',
                    }
                )
            }).finally(() => {
                saveLoading.value = false;
            });
        }
    });
}

onMounted(() => {
    http.post("/v2/test_reports/approval_helper", { method: "get_project_names", }).then((res) => {
        projectNameList.value = res.data.data.result.options;
    }).catch((error) => {
        console.log(error);
    });
    http.post("/v2/test_reports/approval_helper", { method: "get_customer_names", }).then((res) => {
        customerNameList.value = res.data.data.result.options;
    }).catch((error) => {
        console.log(error);
    });
    http.post("/v2/test_reports/approval_helper", { method: "get_soft_types", }).then((res) => {
        softTypeList.value = res.data.data.result.options;
    }).catch((error) => {
        console.log(error);
    });

});


</script>

<style lang="scss" scoped>
.top-tool-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: white;
    height: 50px;
}

.form-container {
    padding: 20px;
}

.submit-button-container {
    display: flex;
    justify-content: center;
    padding-bottom: 20px;
}
</style>
