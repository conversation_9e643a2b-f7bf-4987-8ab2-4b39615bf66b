<template>

    <div>
        <div style="display: flex;flex-direction: column; height: calc(100vh - 201px); margin: 0xp 20px;">
            <div class="tool-bar-container" style="display: flex;">
                <el-button icon="Plus" type="primary" @click="handleAdd">新增</el-button>
                <div style="margin-left: auto; display: flex; gap: 10px;">
                    <el-tooltip class="box-item" effect="dark" content="重置已添加的筛选条件" placement="top-start">
                        <el-button text bg @click="handleReset">重置</el-button>
                    </el-tooltip>
                    <filterButton @click="onFilterStatusChange" :count="filterCount" />
                    <el-button icon="Refresh" text bg @click="handleRefresh">刷新</el-button>
                </div>
            </div>

            <div class="filter-container" v-if="showFilterContainer">
                <el-input v-model="form.program_name_re" placeholder="请输入程序名称" @keyup.enter="onFilter" clearable>
                    <template #append>
                        <el-button icon="Search" @click="onFilter"></el-button>
                    </template>
                </el-input>
                <el-input v-model="form.version_re" placeholder="请输入程序版本" @keyup.enter="onFilter" clearable>
                    <template #append>
                        <el-button icon="Search" @click="onFilter"></el-button>
                    </template>
                </el-input>
            </div>

            <el-table :data="tableData" stripe border style="width: 100%; flex: 1; overflow: auto;">
    <el-table-column prop="program_name" label="程序名称" min-width="200" align="center"></el-table-column>
    <el-table-column prop="version" label="程序版本" min-width="150" align="center"></el-table-column>
    <!-- <el-table-column prop="path" label="路径(WINDOWS)" width="350" align="center">
                    <template #default="{ row }">
                        <el-link type="primary" :href="row.path" target="_blank">{{ row.path }}</el-link>
                    </template>
                </el-table-column>
                <el-table-column prop="linux_path" label="路径(LINUX)" width="350" align="center">
                    <template #default="{ row }">
                        <el-link type="primary" :href="row.linux_path" target="_blank">{{ row.linux_path }}</el-link>
                    </template>
                </el-table-column> -->
    <el-table-column prop="desc" label="描述" min-width="350" align="center">
        <template #default="{ row }">
            <el-scrollbar max-height="200px">
                <p class="preserve-whitespace">{{ row.desc }}</p>
            </el-scrollbar>
        </template>
    </el-table-column>

    <el-table-column prop="is_disabled" label="状态" min-width="100" align="center">
        <template #default="{ row }">
            <el-tag v-if="row.is_disabled" type="danger">弃用</el-tag>
            <el-tag v-else type="success">启用</el-tag>
        </template>
    </el-table-column>

    <el-table-column prop="update_time" label="更新时间" min-width="180" align="center"></el-table-column>

    <el-table-column label="操作" min-width="120" fixed="right" align="center">
        <template #default="{ row }">
            <div style="display: flex; justify-content: center; gap: 10px;">
                <el-button type="primary" size="small" @click="handleEdit(row)">编辑</el-button>
                <el-button @click="handleDelete(row)" type="danger" size="small">弃用</el-button>
            </div>
        </template>
    </el-table-column>
</el-table>

            <div class="pagination-container">
                <el-pagination :page-sizes="[1, 10, 15, 20, 25, 50, 100]"
                    layout="prev, pager, next, jumper, total, sizes" v-model:current-page="form.page"
                    v-model:page-size="form.pagesize" :total="total" background @change="onPageChange" />
            </div>
        </div>

        <el-dialog v-if="dialogAddVisible" v-model="dialogAddVisible" title="添加程序版本" width="800"
            :close-on-click-modal="false">
            <Add @confirm="onAddConfirm" @cancel="onAddCancel" />
        </el-dialog>

        <el-dialog v-if="dialogEditVisible" v-model="dialogEditVisible" title="编辑程序版本" width="800"
            :close-on-click-modal="false">
            <Edit @confirm="onEditConfirm" @cancel="onEditCancel" :r_id="r_id" />
        </el-dialog>
    </div>

</template>


<script setup>

import { ref, reactive, onMounted } from 'vue';
import http from '@/utils/http/http.js';
import Add from './add.vue';
import Edit from './edit.vue';
import filterButton from '@/components/filterButton.vue';
import { useAccessStat } from '@/utils/accessStat';

useAccessStat('/programs/versions/list', '程序版本列表');
const tableData = ref([]);

let r_id = ref(0);

const dialogAddVisible = ref(false);
const filterCount = ref(0);
const dialogEditVisible = ref(false);

let form = reactive({
    page: 1,
    pagesize: 10,
});

let total = ref(0);

let showFilterContainer = ref(false);

function onFilterStatusChange() {
    showFilterContainer.value = !showFilterContainer.value;
};

function update_table() {
    http.get('/programs/versions', { params: form }).then(res => {
        tableData.value = res.data.data.results;
        total.value = res.data.data.count;
    });

    filterCount.value = Object.keys(form).filter(key => !['page', 'pagesize'].includes(key)).reduce((count, key) => {
        if (form[key] == '' || form[key] == undefined || form[key] == null || form[key].length == 0) {
            return count;
        } else {
            return count + 1;
        }
    }, 0)
};


function handleReset() {
    form = reactive({
        page: 1,
        pagesize: 10,
    });
    update_table();
};

function onPageChange() {
    update_table();
};

function onFilter() {
    form.page = 1;
    form.pagesize = 10;
    update_table();
};

function handleAdd() {
    dialogAddVisible.value = true;
};

function handleEdit(row) {
    r_id.value = row.id;
    dialogEditVisible.value = true;
};

function handleDelete(row) {
    ElMessageBox.confirm(
        '确定弃用吗?',
        '提示',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }
    ).then(() => {
        http.delete(`/programs/versions/${row.id}`).then(res => {
            ElMessage({
                message: '弃用成功.',
                type: 'success',
            });
            update_table();
        }).catch(err => {
            ElMessageBox.alert(
                err.response.data.msg,
                '警告',
                {
                    confirmButtonText: '确定',
                    type: 'warning',
                }
            )
        });
    }).catch(() => {
        ElMessage({
            type: 'info',
            message: '已取消弃用'
        });
    });
};

function onAddConfirm() {
    dialogAddVisible.value = false;
    update_table();
};

function onAddCancel() {
    dialogAddVisible.value = false;
};

function onEditConfirm() {
    dialogEditVisible.value = false;
    update_table();
};

function onEditCancel() {
    dialogEditVisible.value = false;
};

function handleRefresh() {
    update_table();
};

onMounted(() => {
    update_table();
});

</script>


<style scoped>
.search-container {
    width: 100%;
    display: flex;
    justify-content: center;
    justify-items: center;
    height: 40px;
}

.search-input {
    width: 50%;
    max-width: 500px;
}

.search-button {
    border: 1px solid #dcdfe6;
}

.filter-container {
    width: 100%;
    display: flex;
    justify-content: flex-start;

    margin-bottom: 10px;

    .el-input {
        width: 50%;
        max-width: 300px;
        margin-right: 10px;
    }
}

.preserve-whitespace {
    white-space: pre-wrap;
    text-align: left;
    /* 或者使用 pre */
}

.pagination-container {
  margin-top: auto; /* 自动顶到底部 */
  align-self: flex-start; /* 左对齐 */
  padding-top: 15px;
  width: 100%;
}

</style>