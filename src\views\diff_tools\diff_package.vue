<template>
    <div class="container">
        <template v-if="diffPackageInfo">
            <el-form label-width="auto" class="form">
                <el-form-item label="项目:">
                    <span>{{ diffPackageInfo.project_name }}</span>
                    <el-input style="display: none"></el-input>
                </el-form-item>
                <el-form-item label="差分包:">
                    <span>{{ diffPackageInfo.diff_package_name }}</span>
                    <el-input style="display: none"></el-input>
                </el-form-item>
                <el-form-item label="差分包版本号:">
                    <span>{{ diffPackageInfo.diff_package_version }}</span>
                    <el-input style="display: none"></el-input>
                </el-form-item>
                <el-form-item label="差分包MD5:">
                    <span>{{ diffPackageInfo.diff_package_md5 }}</span>
                    <el-input style="display: none"></el-input>
                </el-form-item>
                <el-form-item label="基准软件包版本号:">
                    <span>{{ diffPackageInfo.old_package_version }}</span>
                    <el-input style="display: none"></el-input>
                </el-form-item>
                <el-form-item label="目标软件包版本号:">
                    <span>{{ diffPackageInfo.new_package_version }}</span>
                    <el-input style="display: none"></el-input>
                </el-form-item>
                <el-form-item label="操作人:">
                    <span>{{ diffPackageInfo.operator_name }}</span>
                    <el-input style="display: none"></el-input>
                </el-form-item>
                <el-form-item label="验证状态:">
                    <span>{{ diffPackageInfo.diff_package_status ? '验证通过' : '验证不通过' }}</span>
                    <el-input style="display: none"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" plain @click="handleDownload"
                        style="width: 100%;margin-top: 10px;">下载</el-button>
                </el-form-item>
            </el-form>
        </template>
        <template v-else>
            <h2 style="margin-top: 40px;">链接已失效</h2>
        </template>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import axios from 'axios';
import { useRoute } from 'vue-router';

const route = useRoute();

const diffPackageInfo = ref(null);

function handleDownload() {
    window.open(import.meta.env.VITE_BASE_URL + '/diff_tool/records/share_urls/' + route.params.token + '/download');
}

onMounted(() => {
    axios.get(import.meta.env.VITE_BASE_URL + '/diff_tool/records/share_urls/' + route.params.token).then(res => {
        diffPackageInfo.value = res.data.data;
    }).catch(err => {
        console.log(err);
    });
});

</script>

<style lang="scss" scoped>
.container {
    display: flex;
    justify-content: center;
    height: 100vh;
    background-color: #f5f5f5;
}

.form {
    padding: 20px;
    width: 400px;
}

:deep(.el-form-item) {
    margin: 0;
}
</style>