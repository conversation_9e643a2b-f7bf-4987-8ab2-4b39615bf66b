<template>
    <div class="nav-container">
        <div class="left-containter full-height no-padding">

            <div style="width: 280px;margin-right: 20px;" v-if="projectShow">
                <Projects v-model="current_project_number" ref="projectsRef" @change="onProjectChange" />
            </div>

            <el-menu @select="onMenuSelect" :default-active="sider.activeIndex2" mode="horizontal" :ellipsis="false">

                <template v-for="(route, index) in tabs">
                    <el-menu-item :index="index.toString()" @click="$router.push({ path: route.path })">
                        {{ route.title }}
                    </el-menu-item>
                </template>

            </el-menu>
        </div>

        <div class="right-menu">
             
            <!-- AI 助手按钮 -->
            <el-button
                type="primary"
                style="border-radius: 4px; margin-right: 30px;"
                @click="showChatDialog = true"
            >
                AI 助手
            </el-button>

            <!-- 弹窗内嵌 iframe 聊天 -->
            <el-dialog
                v-model="showChatDialog"
                title="AI 助手"
                width="50%"
                top="10vh"
                :close-on-click-modal="false"
                :destroy-on-close="true"
            >
                <iframe
                :src="chatbotUrl"
                style="width: 100%; height: 600px; border: none;"
                ></iframe>
            </el-dialog>

             <el-dropdown>
                <div class="right-menu-label">
                    <el-avatar :key="avatar_url" :src="avatar_url" />
                    <span>{{ user.user_info?.username }}</span>
                </div>
                <template #dropdown>
                    <el-dropdown-menu>
                        <el-dropdown-item @click="logout" icon="SwitchButton">重新进入</el-dropdown-item>
                        <el-dropdown-item icon="Document">
                            <el-link :underline="false" target="_blank"
                                href="https://hiway.feishu.cn/wiki/Ibafw1sQwiRXDwk9UHtc2wDgnag">帮助文档</el-link>
                        </el-dropdown-item>
                        <el-dropdown-item icon="Document">
                            <el-link :underline="false" target="_blank"
                                href="https://hiway.feishu.cn/wiki/Grn3wETMziCXT1kQo8Jc0bj3nAg">更新日志</el-link>
                        </el-dropdown-item>
                        <el-dropdown-item icon="Document">
                            <el-link :underline="false" target="_blank"
                                href="https://ecm.hiwaytech.com/apps/files/files/13327475?dir=/jenkins_release/WPTSN11/Tool/HWCollectX/A1-2025-06-11">海微自动化工具</el-link>
                        </el-dropdown-item>
                        <el-divider style="margin: 0;" />
                        <div style="display: flex; justify-content: center; align-items: center;margin-top: 5px;">
                            {{ version }}
                        </div>
                    </el-dropdown-menu>
                </template>
            </el-dropdown>
        </div>
    </div>
</template>


<script setup>
import { ref, watch, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import menu from '@/api/menu.json';
import { useUserStore } from '@/stores/user';
import { useSiderStore } from '@/stores/sider.js';
import Projects from '@/components/projects.vue';
import { useProjectStore } from '@/stores/project.js';

const sider = useSiderStore();
const user = useUserStore();
const avatar_url = computed(() => {
    return user.user_info?.avatar;
});
const route = useRoute();
const router = useRouter();
let projectStore = useProjectStore();
const projectsRef = ref(null);
const current_project_number = ref(null);
const version = import.meta.env.VITE_VERSION;
const tabs = ref([]);
const projectShow = ref(true);

function onProjectChange() {
    let p_info = projectsRef.value.getProjectInfo(current_project_number.value);
    projectStore.update(p_info || {});
};

watch(() => projectStore.project_info, () => {
    current_project_number.value = projectStore.project_info.projectCode;
}, { immediate: true });

function onMenuSelect(index) {
    sider.setActiveIndex2(index);
}

function logout() {
    user.logout();
    sessionStorage.clear();
    router.push('/login');
}


function getTabs(m, index) {
    if (index.length == 1) {
        return m[index[0]]?.tabs || [];
    }

    return getTabs(m[index[0]]?.children, index.slice(1));
}



const showChatDialog = ref(false)
const chatbotUrl = ref('http://*********:8081/chatbot/NMIm1X8bsLhrxDU4') // 替换成你的地址

watch([() => sider.activeIndex, () => route.path], () => {
    if (!sider.activeIndex) {
        return;
    }

    let index = sider.activeIndex.split('-');
    tabs.value = getTabs(menu, index);

    route.matched.forEach((item) => {
        tabs.value.forEach((tab, i) => {
            if (tab.path == item.path) {
                sider.setActiveIndex2(i.toString());
            }
        });
    });

}, { immediate: true });

</script>


<style lang="scss" scoped>
.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
    padding-left: 22px;
}

.el-menu {
    border: none;
}


.left-containter {
    display: flex;
    justify-content: left;
    align-items: center;
    padding: 0 32px;
}

.el-breadcrumb {
    margin-left: 20px;
    padding: 0;
    font-size: 16px;
    color: #606266;
}

.right-menu {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 40px;

    .el-avatar {
        margin-right: 3px;
        width: 25px;
        height: 25px;
    }
}

.right-menu-label {
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
}

.el-menu-item {
    border-style: none;
}

.el-menu-item.is-active {
    border-style: none;

    border-top-color: rgb(64, 158, 255);
    border-top-style: solid;
    border-top-width: 2px;

    background-color: #ecf5ff;
}
</style>