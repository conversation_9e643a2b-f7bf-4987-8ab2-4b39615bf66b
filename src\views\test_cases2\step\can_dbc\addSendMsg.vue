<template>

    <div style="width: 100%;">
        <div v-for="(v, index) in model" class="recv_msg_item">
            <el-tooltip :content="showMessage(v)" placement="top" effect="light" :disabled="!v">
                <el-input :value="showMessage(v)" class="hex-input" readonly></el-input>
            </el-tooltip>
            <el-button-group style="margin-left: 10px;width: 40%;">
                <el-button type="primary" icon="Edit" @click="handleSelect(index)" />
                <!-- <el-button type="primary" icon="Edit" @click="handleEdit(index)" /> -->
                <el-button type="primary" icon="Delete" @click="handleDelete(index)" />
            </el-button-group>
        </div>
        <el-button type="primary" plain @click="handleAdd" icon="Plus">添加发送报文</el-button>


        <el-dialog v-if="dialogSelectMsgVisible" v-model="dialogSelectMsgVisible" title="选择消息" width="1300"
            :close-on-click-modal="false">
            <Msg :index="insertIndex" :message="insertMessage" @confirm="onSelectMsgConfirm" @cancel="onSelectMsgCancel" />
        </el-dialog>

    </div>

</template>

<script setup>
import { ref } from 'vue'
import Msg from './msg.vue'

const dialogSelectMsgVisible = ref(false);
const model = defineModel();
const insertIndex = ref(-1);
const insertMessage = ref(null);

function handleSelect(index) {
    insertIndex.value = index;
    insertMessage.value = model.value[index];
    dialogSelectMsgVisible.value = true;
}

function showMessage(v) {
    if (!v || v.frame_id === undefined) return '请选择消息';
    const hexId = '0x' + Number(v.frame_id).toString(16).toUpperCase().padStart(3, '0');
    const bytes = normalizeToBytes(v.msg);
    const payload = formatHex(bytes, { groupSize: 4 });
    return `${hexId} | [${bytes.length}] ${payload}`;
}

function normalizeToBytes(msg) {
    let m = msg;
    if (m && typeof m === 'object' && !Array.isArray(m) && !(m instanceof Uint8Array)) {
        if ('data' in m) m = m.data;
    }
    if (Array.isArray(m)) {
        return m.map(n => (Number(n) & 0xFF) >>> 0);
    }
    if (m instanceof Uint8Array) {
        return Array.from(m);
    }
    if (typeof m === 'string') {
        // 去掉0x/0X、空格、非十六进制字符
        let s = m.replace(/^0x/i, '').replace(/\s+/g, '').replace(/[^0-9a-fA-F]/g, '');
        if (s.length % 2 === 1) s = '0' + s;
        const out = [];
        for (let i = 0; i < s.length; i += 2) {
            out.push(parseInt(s.slice(i, i + 2), 16) & 0xFF);
        }
        return out;
    }
    return [];
}

function formatHex(bytes, { groupSize = 4 } = {}) {
    if (!bytes?.length) return '';
    const hex = bytes.map(b => (b & 0xFF).toString(16).toUpperCase().padStart(2, '0'));
    const parts = [];
    for (let i = 0; i < hex.length; i++) {
        parts.push(hex[i]);
        if (i !== hex.length - 1) {
            parts.push(' ');
            if (groupSize > 0 && (i + 1) % groupSize === 0) parts.push(' ');
        }
    }
    return parts.join('').replace(/\s{2,}/g, '  ');
}

function onSelectMsgConfirm(index, selectedMsg) {
    model.value[index] = selectedMsg;
    dialogSelectMsgVisible.value = false;
}

function onSelectMsgCancel() {
    dialogSelectMsgVisible.value = false;
}

function handleDelete(index) {
    if (model.value.length === 1) {
        return
    }
    model.value.splice(index, 1)
}

function handleAdd() {
    model.value.push(null);
}

</script>

<style scoped>
.recv_msg_item {
    display: flex;
    justify-content: space-between;
    width: 100%;
}

.recv_msg_item:not(:last-child) {
    margin-bottom: 10px;
}
</style>